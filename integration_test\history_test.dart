import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/history/directory_state.dart';
import 'package:qfiler/presentation/history/history.dart';
import 'package:qfiler/presentation/history/history_store.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // TODO: tests to add:
  // TODO: - right side
  // TODO: - go back in history (trigger with alt+right arrow)
  // TODO: - go forward in history (trigger with alt+left arrow)
  // TODO: - switch tab

  // TODO: Navigation tests to add:
  // TODO: - start from non-existing dir
  // TODO: - navigate to non-existing dir
  // TODO: - navigate to non-existing dir which suddenly becomes existing
  // TODO: - navigate to existing dir which suddenly becomes non-existing
  // TODO: - navigate up from dir focuses on parent
  // TODO: - navigate into previously visted dir focuses on previously focused rows
  // TODO: - navigate back in history from dir focuses on last focused row
  // TODO: - navigate forward in history from dir focuses on last focused row
  // TODO: - navigate back in history from dir but previous row does not exist (fake scenario but still)
  // TODO: - navigate forward in history from a dir but that dir doesn't exist

  // Create simple test file hierarchy
  final leftFiles = [
    TestFile(name: 'file1.txt'),
    TestFile(name: 'subdir', isDirectory: true, children: [
      TestFile(name: 'nested_file.txt'),
    ]),
  ];

  final rightFiles = [
    TestFile(name: 'file2.txt'),
    TestFile(name: 'subdir2', isDirectory: true, children: [
      TestFile(name: 'nested_file.txt'),
    ]),
  ];

  final leftFilesWithMoreSubdirs = [
    TestFile(name: 'file1.txt'),
    TestFile(name: 'file_to_focus_on.txt'),
    TestFile(name: 'subdir', isDirectory: true, children: [
      TestFile(name: 'nested_file.txt'),
    ]),
    TestFile(name: 'another_subdir', isDirectory: true, children: [
      TestFile(name: 'another_nested_file.txt'),
    ]),
  ];

  group('History Persistence Tests', () {
    // TODO: Do it for the right as well

    testWidgets('History should persist on focused row change', (tester) async {
      final test = await prepareTest(tester, name: 'history_persistence_focused_row_test', leftFiles: leftFiles, rightFiles: rightFiles);

      final initialLeftHistory = HistoryStoreStateSnapshot([History.single(test.leftDir)]);

      // Verify initial state
      expect(test.historyStore(Side.left).snapshot(), equals(initialLeftHistory), reason: 'Initial history is not as expected');

      // Navigate down 1 row
      await test.sendKeyAndWait(LogicalKeyboardKey.arrowDown);

      final updatedLeftHistory =
          HistoryStoreStateSnapshot([History.single(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1)]);

      // Verify updated history is as expected and was persisted
      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
      expect(await readHistoryFromFile(test, Side.left), equals(updatedLeftHistory), reason: 'Persisted history is not as expected');
    });

    testWidgets('History should persist on directory change', (tester) async {
      final test = await prepareTest(tester, name: 'history_persistence_directory_change_test', leftFiles: leftFiles, rightFiles: rightFiles);

      // Navigate into subdirectory: .. is at index 0, subdir is at index 1
      // So we need 1 arrow down keypress to reach subdir
      await test.sendKeyAndWait(LogicalKeyboardKey.arrowDown);
      await test.sendKeyAndWait(LogicalKeyboardKey.enter);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
          DirectoryState(test.leftDir.child('subdir'))
        ], currentIndex: 1),
      ]);

      // Verify updated history is as expected and was persisted
      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
      expect(await readHistoryFromFile(test, Side.left), equals(updatedLeftHistory), reason: 'Persisted history is not as expected');
    });
  });

  group('Navigation Tests', () {});

  group('Navigate to parent', () {
    testWidgets('focuses on navigated dir when parent history matches', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_focus_matches_test',
          leftFiles: leftFiles,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  DirectoryState(basePath, focusedPath: basePath.child('subdir'), focusedRowIndex: 1),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent is already open in other pane', (tester) async {
      // This test is significant because if the directory we navigate to is already watched, some reactions may not run due to bugs.
      // For example, the directory will not load again (because its already loaded) and that can prevent focus tracking from working.
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_right_side_is_parent_test',
          leftFiles: leftFiles,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  DirectoryState(basePath, focusedPath: basePath.child('subdir'), focusedRowIndex: 1),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Change right side to point to the parent of left. This will make sure left navigation to parent does not trigger a load.
      test.historyStore(Side.right).current.replace(History.single(test.leftDir));
      await tester.pumpAndSettle();
      test.assertReady();
      expect(test.historyStore(Side.right).current, equals(History.single(test.leftDir)));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir even when parent history points to another existing dir', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_focus_other_dir_exists',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  // Note that another_subdir is at index 0, file1 is at 1, file_to_focus is at 2, subdir is at 3
                  DirectoryState(basePath, focusedPath: basePath.child('another_subdir'), focusedRowIndex: 0),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('another_subdir'), focusedRowIndex: 0),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir even when parent history is older and points elsewhere', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_focus_from_older_history',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  // This is the history entry for the parent dir that we expect to see restored
                  DirectoryState(basePath, focusedPath: basePath.child('another_subdir'), focusedRowIndex: 0),
                  // This is an unrelated history entry
                  DirectoryState(basePath.child('another_subdir').child('another_nested_file.txt')),
                  // This is the current directory
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 2),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('another_subdir'), focusedRowIndex: 0),
          DirectoryState(test.leftDir.child('another_subdir').child('another_nested_file.txt')),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 3),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent history points to a non-existent file', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_focus_file_does_not_exist',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  DirectoryState(basePath, focusedPath: basePath.child('file_that_does_not_exist.txt'), focusedRowIndex: 2),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('file_that_does_not_exist.txt'), focusedRowIndex: 2),
          DirectoryState(test.leftDir.child('subdir')),
          // Note that another_subdir is at index 0, file1 is at 1, file_to_focus is at 2, subdir is at 3
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent history points to an existing file', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_focus_file_exists',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  // Note that another_subdir is at index 0, file1 is at 1, file_to_focus is at 2, subdir is at 3
                  DirectoryState(basePath, focusedPath: basePath.child('file_to_focus_on.txt'), focusedRowIndex: 2),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('file_to_focus_on.txt'), focusedRowIndex: 2),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent history points to a non-existent dir', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_focus_other_dir_does_not_exist',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  DirectoryState(basePath, focusedPath: basePath.child('another_subdir_that_does_not_exist'), focusedRowIndex: 0),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('another_subdir_that_does_not_exist'), focusedRowIndex: 0),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('creates new history and focuses on navigated dir when no parent history exists', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_no_history_test',
          leftFiles: leftFiles,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History.single(basePath.child('subdir')),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
        ], currentIndex: 1),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent history has correct path but incorrect index', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_correct_path_wrong_index',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  // Note that another_subdir is at index 0, file1 is at 1, file_to_focus is at 2, subdir is at 3
                  DirectoryState(basePath, focusedPath: basePath.child('another_subdir'), focusedRowIndex: 99),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('another_subdir'), focusedRowIndex: 99),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent history has correct path but negative index', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_correct_path_negative_index',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  // Note that another_subdir is at index 0, file1 is at 1, file_to_focus is at 2, subdir is at 3
                  DirectoryState(basePath, focusedPath: basePath.child('another_subdir'), focusedRowIndex: -1),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('another_subdir'), focusedRowIndex: -1),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent history has correct path but incorrect index', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_correct_index_wrong_path',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  // Note that another_subdir is at index 0, file1 is at 1, file_to_focus is at 2, subdir is at 3
                  DirectoryState(basePath, focusedPath: basePath.child('non_existent.txt'), focusedRowIndex: 0),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('non_existent.txt'), focusedRowIndex: 0),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });

    testWidgets('focuses on navigated dir when parent history has mismatched path and index', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_mismatched_path_and_index',
          leftFiles: leftFilesWithMoreSubdirs,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  // Path points to 'another_subdir' (index 0), but index points to 'file_to_focus_on.txt' (index 2)
                  DirectoryState(basePath, focusedPath: basePath.child('another_subdir'), focusedRowIndex: 2),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('another_subdir'), focusedRowIndex: 2),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 3),
        ], currentIndex: 2),
      ]);

      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
    });
  });
}

Future<HistoryStoreStateSnapshot> readHistoryFromFile(TestState test, Side side) =>
    HistoryStore.readSnapshot(test.rootStore.dataRepository, side);
