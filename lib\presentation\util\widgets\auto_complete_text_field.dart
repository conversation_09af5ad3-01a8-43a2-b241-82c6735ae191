import 'dart:math';

import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:mobx/mobx.dart' hide Action;

import '../../../app/util/async_value.dart';
import '../../../app/util/string_utils.dart';
import '../context_extensions.dart';
import '../error_formatter.dart';
import '../scroll_utils.dart';
import './error_text.dart';
import './shimmer.dart';

part '.gen/auto_complete_text_field.freezed.dart';
part '.gen/auto_complete_text_field.g.dart';

const numLoadingRows = 5;
const itemScrollMargin = 2;
const amountOfVisibleItems = 10;
const suggestionsPadding = 4.0;

class AutoCompleteTextField<T> extends StatefulWidget {
  final AutoCompleteTextFieldController<T> controller;
  final void Function(String) onSubmitted;

  final Widget Function(BuildContext, SuggestionItem<T>)? suggestionItemBuilder;
  final double suggestionItemHeight;

  final Widget Function(BuildContext)? loadingBuilder;
  final Widget Function(BuildContext, Exception)? errorBuilder;
  final Widget Function(BuildContext)? emptyBuilder;

  final VoidCallback? onShow;
  final VoidCallback? onHide;

  final FocusNode? focusNode;

  final TextStyle? textStyle;

  final InputDecoration? inputDecoration;
  final SuggestionsBoxDecoration suggestionsBoxDecoration;

  const AutoCompleteTextField({
    super.key,
    required this.controller,
    required this.onSubmitted,
    this.suggestionItemBuilder,
    this.suggestionItemHeight = 23,
    this.loadingBuilder,
    this.errorBuilder,
    this.emptyBuilder,
    this.onShow,
    this.onHide,
    this.focusNode,
    this.textStyle,
    this.inputDecoration,
    this.suggestionsBoxDecoration = const SuggestionsBoxDecoration(),
  });

  // FIXME LayerLink should cause textfield to resize.

  @override
  State createState() => AutoCompleteTextFieldState<T>();
}

class AutoCompleteTextFieldState<T> extends State<AutoCompleteTextField<T>> {
  final _layerLink = LayerLink();
  late final _focusNode = widget.focusNode ?? FocusNode(debugLabel: 'AutoCompleteTextField');
  final _scrollController = ScrollController();

  OverlayEntry? _overlayEntry;

  final List<ReactionDisposer> _disposers = [];

  AutoCompleteTextFieldController<T> get _textFieldController => widget.controller;

  void _focusListener() {
    if (_focusNode.hasFocus) {
      _showOverlay();
    } else {
      _hideOverlay();
    }
  }

  void _scrollListener() {
    addExtraScrollSpeed(_scrollController, extraScrollSpeed: (widget.suggestionItemHeight + suggestionsPadding) * 3);
  }

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_focusListener);
    _scrollController.addListener(_scrollListener);

    _disposers.addAll([
      reaction((_) => _textFieldController._selectedSuggestionIndex, (index) {
        if (index != null && _scrollController.hasClients) {
          _maintainScrollOffset(index);
        }

        // Re-render the textfield so the controller can display the auto-completed prefix.
        setState(() {});
      }),
    ]);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_focusListener);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    _hideOverlay();
    for (var d in _disposers) {
      d();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: Focus(
        onKeyEvent: _onKeyEvent,
        child: TextField(
          key: widget.key,
          controller: _textFieldController,
          focusNode: _focusNode,
          style: widget.textStyle,
          decoration: widget.inputDecoration,
        ),
      ),
    );
  }

  late final Map<LogicalKeyboardKey, bool Function()> _keyboardShortcuts = {
    LogicalKeyboardKey.arrowDown: () => _textFieldController._moveSelectedSuggestionDown(1),
    LogicalKeyboardKey.arrowUp: () => _textFieldController._moveSelectedSuggestionUp(1),
    LogicalKeyboardKey.pageDown: () => _textFieldController._moveSelectedSuggestionDown(amountOfVisibleItems),
    LogicalKeyboardKey.pageUp: () => _textFieldController._moveSelectedSuggestionUp(amountOfVisibleItems),
    LogicalKeyboardKey.tab: _useSelectedSuggestionIfApplicable,
    LogicalKeyboardKey.enter: _onSubmitted,
  };

  KeyEventResult _onKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;
    final result = _keyboardShortcuts[event.logicalKey]?.call() ?? false;
    return result ? KeyEventResult.handled : KeyEventResult.ignored;
  }

  bool _onSubmitted() => _submitSuggestionText(_textFieldController._selectedSuggestionText);

  bool _submitSuggestionText(String? suggestionText) {
    final typedText = _textFieldController.text;
    if (!_textFieldController.config.submitSuggestionOnEnter && suggestionText != null) {
      if (typedText != suggestionText) {
        _setTypedText(suggestionText);
        return true;
      } else {
        // If the user has typed the same text as the suggestion, we want to submit the typed text.
      }
    }

    final textToSubmit = suggestionText ?? _textFieldController.text;
    _focusNode.unfocus();
    widget.onSubmitted(textToSubmit);
    return true;
  }

  bool _useSelectedSuggestionIfApplicable() {
    final suggestionText = _textFieldController._selectedSuggestionText;
    if (suggestionText != null) {
      _setTypedText(suggestionText);
    }
    return suggestionText != null;
  }

  void _setTypedText(String suggestionText) {
    _textFieldController.value = TextEditingValue(
      text: suggestionText,
      selection: TextSelection.fromPosition(TextPosition(offset: suggestionText.length)),
    );
  }

  void _showOverlay() {
    var overlay = _overlayEntry;
    if (overlay != null) {
      return;
    }

    RenderBox renderBox = context.findRenderObject() as RenderBox;
    var size = renderBox.size;
    var offset = renderBox.localToGlobal(Offset.zero);

    overlay = OverlayEntry(
      builder: (context) => Positioned(
        left: offset.dx,
        top: offset.dy + size.height + 1,
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height + 1),
          child: TextFieldTapRegion(child: _buildSuggestions()),
        ),
      ),
    );

    widget.onShow?.call();
    _textFieldController._start();

    _overlayEntry = overlay;
    WidgetsBinding.instance.addPostFrameCallback((_) => context.overlay.insert(overlay!));
  }

  Widget _buildSuggestions() {
    return Material(
      elevation: widget.suggestionsBoxDecoration.elevation,
      borderRadius: widget.suggestionsBoxDecoration.borderRadius,
      shadowColor: widget.suggestionsBoxDecoration.shadowColor,
      child: Container(
        // TODO: I think we're getting an exception from here.
        constraints: BoxConstraints(maxHeight: widget.suggestionItemHeight * amountOfVisibleItems),
        child: Observer(builder: (context) {
          final isLoading = _textFieldController.isLoading;
          final error = _textFieldController.error;
          final suggestions = _textFieldController.suggestions;

          if (isLoading && widget.loadingBuilder != null) {
            return widget.loadingBuilder!(context);
          }
          if (error != null && widget.errorBuilder != null) {
            return widget.errorBuilder!(context, error);
          }
          if (!isLoading && error == null && suggestions.isEmpty) {
            return widget.emptyBuilder?.call(context) ?? const SizedBox.shrink();
          }

          return ListView.builder(
            controller: _scrollController,
            shrinkWrap: true,
            padding: const EdgeInsets.all(suggestionsPadding),
            itemCount: isLoading
                ? numLoadingRows
                : error != null
                    ? 1
                    : suggestions.length,
            physics: isLoading ? const NeverScrollableScrollPhysics() : null,
            itemBuilder: (context, index) {
              if (isLoading) {
                return const ShimmerLoading(
                  isLoading: true,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                    child: ShimmerPlaceholder(height: 16),
                  ),
                );
              }
              if (error != null) {
                return ErrorText(ErrorFormatter.formatError(error));
              }

              final suggestion = suggestions[index];

              return Observer(builder: (context) {
                return Container(
                  color: _textFieldController._selectedSuggestionIndex == index
                      ? widget.suggestionsBoxDecoration.selectedColor ?? context.theme.hoverColor.withOpacity(0.1)
                      : null,
                  child: InkWell(
                    child: (widget.suggestionItemBuilder ?? _defaultSuggestionItemBuilder).call(context, suggestion),
                    onTap: () => _onSuggestionSelected(suggestion),
                  ),
                );
              });
            },
          );
        }),
      ),
    );
  }

  Widget _defaultSuggestionItemBuilder(BuildContext context, SuggestionItem<T> suggestion) {
    final item = suggestion.item;
    final matches = suggestion.matches;
    final str = _textFieldController.stringifySuggestion(item);
    final RichText text;
    if (matches.isEmpty) {
      text = RichText(text: TextSpan(style: context.bodyMedium, text: str));
    } else {
      final children = <TextSpan>[];
      var lastEnd = 0;
      for (final match in matches) {
        if (match.start > lastEnd) {
          children.add(TextSpan(text: str.substring(lastEnd, match.start)));
        }
        children.add(TextSpan(
            text: str.substring(match.start, min(match.end, str.length)), style: context.bodyMedium!.copyWith(fontWeight: FontWeight.bold)));
        lastEnd = match.end;
      }
      if (lastEnd < str.length) {
        children.add(TextSpan(text: str.substring(lastEnd)));
      }
      text = RichText(text: TextSpan(style: context.bodyMedium, children: children));
    }
    return Padding(padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2), child: text);
  }

  void _onSuggestionSelected(SuggestionItem<T> suggestion) {
    _submitSuggestionText(_textFieldController.stringifySuggestion(suggestion.item));
  }

  void _hideOverlay() {
    final overlayEntry = _overlayEntry;
    if (overlayEntry != null && overlayEntry.mounted) {
      overlayEntry.remove();
      _overlayEntry = null;
      _textFieldController._stop();
      widget.onHide?.call();
    }
  }

  void _maintainScrollOffset(int index) {
    final suggestions = _textFieldController.suggestions;

    final maxScrollOffset = _scrollController.position.maxScrollExtent;
    final scrollOffset = _scrollController.offset;

    final firstVisibleIndex = scrollOffset > 0 ? ((scrollOffset - suggestionsPadding) ~/ widget.suggestionItemHeight).coerceAtLeast(1) : 0;
    var lastVisibleIndex = suggestions.isNotEmpty ? (firstVisibleIndex + amountOfVisibleItems - 1).coerceIn(0, suggestions.length - 1) : 0;
    if (lastVisibleIndex > 0 && lastVisibleIndex * widget.suggestionItemHeight + suggestionsPadding > maxScrollOffset) {
      lastVisibleIndex--;
    }

    // Keep an "itemScrollMargin" margin of items from the top and bottom.
    var allowedVisibleIndex = (index - itemScrollMargin).coerceAtLeast(0);
    if (allowedVisibleIndex < firstVisibleIndex) {
      final targetOffset = allowedVisibleIndex == 0 ? 0.0 : allowedVisibleIndex * widget.suggestionItemHeight + suggestionsPadding;
      if (targetOffset != scrollOffset) {
        _scrollController.jumpTo(targetOffset);
      }
      return;
    }

    allowedVisibleIndex = (index + itemScrollMargin).coerceAtMost(suggestions.length - 1);
    if (allowedVisibleIndex > lastVisibleIndex) {
      final targetOffset = allowedVisibleIndex == suggestions.length - 1
          ? maxScrollOffset
          : (allowedVisibleIndex - amountOfVisibleItems + 1) * widget.suggestionItemHeight + suggestionsPadding;
      if (targetOffset != scrollOffset) {
        _scrollController.jumpTo(targetOffset);
      }
    }
  }
}

class AutoCompleteTextFieldController<T> = AutoCompleteTextFieldControllerBase<T> with _$AutoCompleteTextFieldController<T>;

@StoreConfig(hasToString: false)
abstract class AutoCompleteTextFieldControllerBase<T> extends TextEditingController with Store {
  AutoCompleteTextFieldControllerBase({
    super.text,
    required this.calcSuggestions,
    required this.stringifySuggestion,
    this.config = const AutoCompleteTextFieldControllerConfig(),
  }) {
    AutoCompleteSuggestions<T>? prevResult;

    // This reactions makes sure a selected suggestion remains selected as long as it is in the list
    // of suggestions, the contextId remains the same and the suggestion was manually selected.
    _trackSelectedSuggestionDisposer = autorun((_) {
      if (!_suggestions.hasValue) {
        return;
      }

      final result = _suggestions.value;
      final suggestions = result.suggestions;
      final indexToAutoSelect = config.preselectFirstSuggestion && suggestions.isNotEmpty ? 0 : null;

      final int? newSelectedIndex;
      if (prevResult == null || prevResult!.contextId != result.contextId || !_shouldTrackCurrentlySelectedSuggestion) {
        // Either the contextId changed or the currently select suggestion was marked as not to be
        // tracked. In both cases we should not try to re-select the previous suggestion in
        // the new suggestions list - Select either the first suggestion or nothing.

        _shouldTrackCurrentlySelectedSuggestion = false;
        newSelectedIndex = indexToAutoSelect;
      } else {
        final prevSelectedSuggestion = prevResult![_selectedSuggestionIndex];

        // Find the index of the previously selected suggestion in the new suggestions list.
        final indexOfPrevSelectedSuggestion =
            prevSelectedSuggestion != null ? suggestions.indexWhere((e) => e.item == prevSelectedSuggestion.item) : -1;

        // Select the previously selected suggestion if it is still in the new suggestions list.
        if (indexOfPrevSelectedSuggestion != -1) {
          newSelectedIndex = indexOfPrevSelectedSuggestion;
        } else {
          newSelectedIndex = indexToAutoSelect;
          _shouldTrackCurrentlySelectedSuggestion = false;
        }
      }

      _setSelectedSuggestionIndex(newSelectedIndex);
      prevResult = result;
    }, name: 'AutoCompleteTextFieldStore.trackSelectedSuggestion');

    addListener(_changeListener);
  }

  final AsyncValue<AutoCompleteSuggestions<T>> Function(String) calcSuggestions;
  final String Function(T) stringifySuggestion;

  final AutoCompleteTextFieldControllerConfig config;

  @observable
  int? _selectedSuggestionIndex;

  @observable
  AsyncValue<AutoCompleteSuggestions<T>> _suggestions = AsyncValue.loading(const AutoCompleteSuggestions([]));

  late final ReactionDisposer _trackSelectedSuggestionDisposer;
  ReactionDisposer? _calcSuggestionsDisposer;

  String? _prevText;
  TextSelection _prevSelection = const TextSelection.collapsed(offset: 0);
  bool _shouldTrackCurrentlySelectedSuggestion = false;

  void _start() {
    addListener(_changeListener);
  }

  void _stop() {
    _calcSuggestionsDisposer?.call();
    removeListener(_changeListener);
  }

  void _changeListener() {
    final text = this.text;
    if (text != _prevText) {
      _prevText = text;

      _calcSuggestionsDisposer?.call();
      _calcSuggestionsDisposer = autorun((_) {
        final result = calcSuggestions(text);
        _setSuggestions(result);
      }, name: 'AutoCompleteTextFieldStore.calcSuggestions');
    }

    if (config.showAutoCompletedTextInTheMiddle) {
      // The below is an attempt to display auto-complete suggestions in the middle of the typed text.
      // It required skipping those auto completed characters when navigating with the keyboard.

      final selection = this.selection;
      if (selection != _prevSelection) {
        final selectedSuggestionText = _selectedSuggestionText;
        // We're only interested in cursor move events when displaying an auto completed suggestion.
        if (selectedSuggestionText != null && selection.isCollapsed) {
          final isMovingLeft = selection.start < _prevSelection.start;
          // Find the next legal position for the cursor in the direction of the cursor change.
          // Skip regions of the text which are auto completed,
          // move to the next char the user typed in the direction of the cursor change.
          final nextLegalPosition = _findNextLegalPosition(selection.start, selectedSuggestionText, isMovingLeft);
          if (nextLegalPosition != selection.start) {
            final newSelection = TextSelection.fromPosition(TextPosition(offset: nextLegalPosition, affinity: selection.affinity));
            this.selection = newSelection;
          }
        }
        _prevSelection = selection;
      }
    }
  }

  int _findNextLegalPosition(int position, String selectedSuggestionText, bool isMovingLeft) {
    // Matches are sorted ascending by start position and are non-overlapping.
    TextRange? prevMatch;
    for (final match in _selectedSuggestion!.matches) {
      if (match.contains(position)) {
        return position;
      }
      if (position < match.start) {
        return isMovingLeft ? (prevMatch?.end ?? match.start) - 1 : match.start;
      }
      prevMatch = match;
    }
    return (prevMatch?.end ?? position);
  }

  @action
  void _setSuggestions(AsyncValue<AutoCompleteSuggestions<T>> value) {
    if (_suggestions != value) {
      _suggestions = value;
    }
  }

  @computed
  bool get isLoading => _suggestions.isLoading;

  @computed
  Exception? get error => _suggestions.error;

  AutoCompleteSuggestions<T> get _result => _suggestions.value;

  String get suggestionContextId => _result.contextId;

  List<SuggestionItem<T>> get suggestions => _result.suggestions;

  @action
  void _setSelectedSuggestionIndex(int? value) {
    if (_selectedSuggestionIndex != value) {
      _selectedSuggestionIndex = value;
    }
  }

  @action
  bool _moveSelectedSuggestionDown([int amount = 1]) {
    if (suggestions.isEmpty) return false;
    final index = (_selectedSuggestionIndex ?? -1) + amount;
    _setSelectedSuggestionIndex(index < suggestions.length ? index : null);
    _shouldTrackCurrentlySelectedSuggestion = true;
    return true;
  }

  @action
  bool _moveSelectedSuggestionUp([int amount = 1]) {
    if (suggestions.isEmpty) return false;
    final index = (_selectedSuggestionIndex ?? suggestions.length) - amount;
    _setSelectedSuggestionIndex(index >= 0 ? index : null);
    _shouldTrackCurrentlySelectedSuggestion = true;
    return true;
  }

  SuggestionItem<T>? get _selectedSuggestion => _result[_selectedSuggestionIndex];

  String? get _selectedSuggestionText {
    final selectedSuggestion = _selectedSuggestion;
    return selectedSuggestion != null ? stringifySuggestion(selectedSuggestion.item) : null;
  }

  @override
  TextSpan buildTextSpan({
    required BuildContext context,
    TextStyle? style,
    required bool withComposing,
  }) {
    final selectedSuggestionText = _selectedSuggestionText;
    final text = this.text;
    const autoCompleteTextStyle = TextStyle(color: Colors.black45);

    if (!config.showAutoCompletedTextInTheMiddle) {
      // Only show a grey autocomplete silhouette if the typed text is a prefix of the selected suggestion.
      if (selectedSuggestionText == null || !selectedSuggestionText.startsWithReversed(text)) {
        return TextSpan(style: style, text: text);
      }

      return TextSpan(
        style: style,
        children: [
          TextSpan(text: text),
          TextSpan(
            style: style?.merge(autoCompleteTextStyle) ?? autoCompleteTextStyle,
            text: selectedSuggestionText.substring(text.length),
          ),
        ],
      );
    } else {
      // FIXME: Doesn't work.
      // Show a grey autocomplete silhouette also in the middle of the text.
      if (selectedSuggestionText == null || selectedSuggestionText.isEmpty) {
        return TextSpan(text: text, style: style);
      }

      final List<TextSpan> children = [];

      final matches = _selectedSuggestion!.matches;
      int suggestionIndex = 0;
      for (final match in matches) {
        final prefix = selectedSuggestionText.substring(suggestionIndex, match.start);
        final matchText = selectedSuggestionText.substring(match.start, match.end);

        if (prefix.isNotEmpty) {
          children.add(TextSpan(text: prefix, style: autoCompleteTextStyle));
        }

        children.add(TextSpan(text: matchText, style: style));
        suggestionIndex = match.end;
      }

      if (suggestionIndex < selectedSuggestionText.length) {
        final remaining = selectedSuggestionText.substring(suggestionIndex);
        children.add(TextSpan(text: remaining, style: autoCompleteTextStyle));
      }

      return TextSpan(children: children);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _stop();
    _trackSelectedSuggestionDisposer();
  }
}

@immutable
@freezed
class SuggestionItem<T> with _$SuggestionItem<T> {
  const SuggestionItem(this.item, {this.matches = const []});

  @override
  final T item;

  @override
  final List<TextRange> matches;
}

@immutable
@freezed
class AutoCompleteSuggestions<T> with _$AutoCompleteSuggestions<T> {
  const AutoCompleteSuggestions(this.suggestions, {this.contextId = ''});

  @override
  final List<SuggestionItem<T>> suggestions;

  @override
  final String contextId;

  SuggestionItem<T>? operator [](int? index) {
    return index != null && index < suggestions.length ? suggestions[index] : null;
  }
}

@immutable
@freezed
class AutoCompleteTextFieldControllerConfig with _$AutoCompleteTextFieldControllerConfig {
  const AutoCompleteTextFieldControllerConfig({
    this.preselectFirstSuggestion = false,
    this.showAutoCompletedTextInTheMiddle = false,
    this.submitSuggestionOnEnter = false,
  });

  /// Whether to automatically select the first auto complete suggestion when the suggestions are updated.
  @override
  final bool preselectFirstSuggestion;

  /// Whether to show the auto completed text in the middle of the text.
  /// If false, the auto completed text will be shown only at the end of the text.
  /// This behavior can be very confusing, so it is disabled by default.
  @override
  final bool showAutoCompletedTextInTheMiddle;

  /// Whether pressing enter with a selected suggestion will submit the suggestion or fill in its
  /// text in the textfield.
  /// If false, "enter" will behave the same as "tab" when a suggestion is selected and submitting
  /// by pressing enter is only possible if no suggestion is selected.
  @override
  final bool submitSuggestionOnEnter;
}

@immutable
class SuggestionsBoxDecoration {
  const SuggestionsBoxDecoration({
    this.elevation = 4.0,
    this.borderRadius = const BorderRadius.only(bottomLeft: Radius.circular(4), bottomRight: Radius.circular(4)),
    this.shadowColor = const Color(0xFF000000),
    this.selectedColor,
  });

  /// The z-coordinate at which to place the suggestions box. This controls the size
  /// of the shadow below the box.
  ///
  /// Same as [Material.elevation](https://docs.flutter.io/flutter/material/Material/elevation.html)
  final double elevation;

  /// If non-null, the corners of this box are rounded by this [BorderRadius](https://docs.flutter.io/flutter/painting/BorderRadius-class.html).
  ///
  /// Same as [Material.borderRadius](https://docs.flutter.io/flutter/material/Material/borderRadius.html)
  final BorderRadius borderRadius;

  /// The color to paint the shadow below the material.
  ///
  /// Same as [Material.shadowColor](https://docs.flutter.io/flutter/material/Material/shadowColor.html)
  final Color shadowColor;

  final Color? selectedColor;
}

extension TextRangeContains on TextRange {
  bool contains(int index) => start <= index && index < end;
}
