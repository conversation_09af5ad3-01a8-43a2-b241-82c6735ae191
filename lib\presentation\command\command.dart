import 'package:class_to_string/class_to_string.dart';
import 'package:meta/meta.dart';

import 'command_context.dart';
import 'command_id.dart';

/// Represents a command that can be executed in the application
@immutable
class Command {
  const Command({
    required this.id,
    required this.label,
    required this.group,
    this.description,
    this.requiredContext,
  });

  /// Unique identifier for the command
  final CommandId id;

  /// Human-readable label for the command
  final String label;

  /// Optional group/category for the command
  final String group;

  /// Optional description of what the command does
  final String? description;

  /// Required context for this command to be applicable
  /// If a field is null, any value is acceptable
  /// If a field is true/false, that must be the value in the current context
  final CommandContext? requiredContext;

  /// Check if this command is applicable in the given context
  bool isApplicableInContext(CommandContext context) {
    final req = requiredContext;
    return req == null || req == context;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Command && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => (ClassToString.flat('Command')
        ..addIfExist('id', id)
        ..addIfExist('requiredContext', requiredContext))
      .toString();
}
