import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:multi_split_view/multi_split_view.dart';
import 'package:provider/provider.dart';

import 'command/command_palette.dart';
import 'command/keybind_repository.dart';
import 'command/keyboard_handler.dart';
import 'pane/pane_store.dart';
import 'root_store.dart';
import 'tab_view/widgets/directory_tabs.dart';
import 'util/context_extensions.dart';

final pageStorageBucket = PageStorageBucket();

class App extends StatelessWidget {
  final RootStore rootStore;

  const App(this.rootStore, {super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<RootStore>.value(value: rootStore),
        ChangeNotifierProvider<KeyBindRepository>.value(value: rootStore.keyBindRepository),
      ],
      child: PageStorage(
        bucket: pageStorageBucket,
        child: Builder(
          builder: (context) {
            return MaterialApp(
              title: 'QFiler',
              debugShowCheckedModeBanner: false,
              builder: (context, widget) {
                Widget error = const Text('...rendering error...');
                if (widget is Scaffold || widget is Navigator) {
                  error = Scaffold(body: Center(child: error));
                }

                // ErrorWidget.builder = (errorDetails) => error;
                if (widget != null) return widget;
                throw ('widget is null');
              },
              theme: ThemeData(
                // primarySwatch: Colors.blue,
                // dataTableTheme: const DataTableThemeData(dividerThickness: 0),
                // useMaterial3: true,
                textButtonTheme: TextButtonThemeData(style: TextButton.styleFrom(foregroundColor: Colors.black)),
              ),
              home: CommandPalette(
                key: commandPaletteKey,
                child: KeyboardHandler(
                  // autofocus: true,
                  canRequestFocus: false,
                  debugLabel: "App",
                  child: MainScreen(),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

final splitAreas = [
  Area(
    min: 400,
    flex: 1,
    builder: (context, area) => Provider<PaneStore>(create: (_) => context.rootStore.allPanesStore.left, child: const DirectoryTabs(debugLabel: 'LeftPane')),
  ),
  Area(
    min: 400,
    flex: 1,
    builder: (context, area) => Provider<PaneStore>(create: (_) => context.rootStore.allPanesStore.right, child: const DirectoryTabs(debugLabel: 'RightPane')),
  ),
];

class MainScreen extends StatelessWidget {
  MainScreen({super.key});

  final MultiSplitViewController _controller = MultiSplitViewController(areas: splitAreas);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: MultiSplitViewTheme(
        data: MultiSplitViewThemeData(
          dividerThickness: 3,
          dividerPainter: DividerPainters.background(
            color: Colors.grey[300]!,
            highlightedColor: context.theme.dividerColor,
            animationDuration: const Duration(milliseconds: 200),
          ),
        ),
        child: MultiSplitView(
          controller: _controller,
          onDividerDoubleTap: (_) => _controller.areas = splitAreas,
        ),
      ),
    );
  }
}
