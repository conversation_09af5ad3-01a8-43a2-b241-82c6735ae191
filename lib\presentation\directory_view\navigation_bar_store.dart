import 'package:dartx/dartx.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';

import '../../app/domain/file.dart';
import '../../app/domain/path.dart';
import '../../app/file_system/file_repository.dart';
import '../../app/util/async_value.dart';
import '../../app/util/log_utils.dart';
import '../../app/util/quick_score.dart';
import '../command/command_context.dart';
import '../command/command_context_repository.dart';
import '../domain/side.dart';
import '../history/history_store.dart';
import '../util/widgets/auto_complete_text_field.dart';

part '.gen/navigation_bar_store.g.dart';

class NavigationBarStore = NavigationBarStoreBase with _$NavigationBarStore;

abstract class NavigationBarStoreBase with Store {
  NavigationBarStoreBase(
      {required FileRepository fileRepository,
      required HistoryStore historyStore,
      required CommandContextRepository commandContextRepository,
      required Side side})
      : _historyStore = historyStore,
        _commandContextRepository = commandContextRepository,
        _autoCompleteSession = fileRepository.createWatchFileClient('${side.name}_navigation_bar_autocomplete', maxWatchedDirs: 5);

  final HistoryStore _historyStore;
  final CommandContextRepository _commandContextRepository;
  final WatchFileSession _autoCompleteSession;

  /// Whether the navigation bar is in input mode (showing text field)
  @observable
  bool isInputVisible = false;

  /// Index of the currently hovered breadcrumb item (-1 for ellipsis)
  @observable
  int? hoveredBreadcrumbIndex;

  RawPath get dir => _historyStore.current.current.dir;

  @action
  void showInput() {
    if (kDebugMode) {
      logger.fine('$dir showInput()');
    }
    isInputVisible = true;
    _commandContextRepository.pushContext(const CommandContext(inNavigationBarInput: true));
  }

  @action
  void hideInput() {
    if (!isInputVisible) {
      return;
    }

    if (kDebugMode) {
      logger.fine('$dir hideInput()');
    }
    isInputVisible = false;
    _autoCompleteSession.unwatchAll();
    _commandContextRepository.popContext(const CommandContext(inNavigationBarInput: true));
  }

  @action
  void setBreadcrumbHover(int? index, RawPath? path) {
    if (kDebugMode) {
      logger.finest("setBreadcrumbHover(index=$index, path=$path)");
    }
    if (index != null) {
      assert(hoveredBreadcrumbIndex == null, "Already hovered on [$hoveredBreadcrumbIndex]");
    } else {
      assert(hoveredBreadcrumbIndex != null, "Not hovered!");
    }
    hoveredBreadcrumbIndex = index;
  }

  AsyncValue<AutoCompleteSuggestions<File>> calcAutoCompleteSuggestions(String prefix) {
    // FIXME: How to handle '/'?
    // FIXME: E:\qfi highlights the wrong bits of text.
    // FIXME: Any prefix that matches a directory exactly should be the first suggestion with a separator at the end.
    // FIXME: All suggestions should have a separator at the end.
    // FIXME: Going back to E:\qfiler throws an error and marks the wrong portion of the suggestion as a match. This is because the match is larger than the str length.
    // FIXME: The empty string throws an error.
    // FIXME: If prefix does not end with a separator and is a valid directory, first suggestion should be prefix with separator, then its children.
    // FIXME: Scrolling auto complete list where some of the folders are larger than 1 line (D:\Download) doesn't behave correctly.
    // FIXME: Selecting the auto complete suggestion E:\qfiler\test2\asd from E:\qfiler\test2\ causes [E:\qfiler\test2] Previously existed but now doesn't, reloading..
    // FIXME: Names starting with dots still don't work.
    // FIXME: Going to E:\qfiler, selecting .dart_tool as the suggestion and then typing \t causes a scroll animation.
    // FIXME: Going to D:\Download and typing wick instead of the text causes an exception.
    // FIXME: All suggestions must end with a separator.
    // FIXME: Unhandled Flutter Platform error: Not found: 'Ew\'
    // FIXME: empty prefix should show all mounted drives / start points.
    // FIXME: Clicking on the textfield should immediately add a separator to it.
    // FIXME: If all suggestions end with prefix, the first suggestion should not be automatically selected, because pressing enter will navigate to it.
    // FIXME: Alternatively, if suggestions don't end with a prefix, the first suggestion should be automatically selected.
    // FIXME: Make this a configuration value.

    // TODO: Is the below needed?
    // if (prefix.isEmpty) {
    //   // Return current dir with all children marked as matches.
    //   return _directory.snapshot.map((files) => AutoCompleteSuggestions(
    //       files.filter((e) => e.isDirectory).toList(),
    //       matches: files.map((e) => [TextRange(start: 0, end: e.absolutePath.length)]).toList()));
    // }

    final unresolvedPrefix = prefix.asPath(resolve: false);
    final resolvedPrefix = prefix.isNotEmpty ? unresolvedPrefix.resolved() : dir;
    final parent = resolvedPrefix.parent;

    final RawPath dirToFetch;
    final String? nameToMatch;
    if (prefix.isEmpty || prefix.endsWith(RawPath.pathSeparator) || parent == null) {
      // Fetch the directory that the resolvedPrefix points to and mark all its children as matches.
      dirToFetch = resolvedPrefix;
      nameToMatch = null;
    } else {
      dirToFetch = parent;
      nameToMatch = resolvedPrefix.name;
    }

    if (kDebugMode) {
      logger.finest(
          "$dir calcAutoCompleteSuggestions($prefix): dirToFetch=$dirToFetch, nameToMatch=$nameToMatch, unresolvedPrefix=$unresolvedPrefix, resolvedPrefix=$resolvedPrefix");
    }

    // Add matches of the prefix from the parent.
    // _autoCompleteHandler.watchDir is safe to call multiple times because result of watching is
    // cached and only performed once. On top of that, it is limited to watch at most 5 directories
    // after which it starts un-watching the oldest ones.
    final currentDirFiles = _autoCompleteSession.watchDir(dirToFetch);
    if (!currentDirFiles.hasValue) {
      // Return loading or error.
      return _autoCompleteResultWithoutMatches(currentDirFiles, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix);
    }

    // Check which directories in the current dir match the prefix.
    final List<File> candidates = currentDirFiles.files.filter((e) => e.isDirectory).toList();

    final quickScore = QuickScore<File>(candidates, {"name": (e) => e.name}, sortTieBreaker: File.naturalComparator);
    final results = quickScore.search(nameToMatch ?? "");

    // If prefix matches the name of child directory exactly, add it as the first suggestion and
    // add its children below it in the results.
    final fileInParent = nameToMatch != null ? currentDirFiles.get(nameToMatch) : null;
    if (fileInParent != null && fileInParent.isDirectory) {
      // We have an exact match.
      final childFiles = _autoCompleteSession.watchDir(resolvedPrefix);
      if (!childFiles.hasValue) {
        // return childFiles;
        return _autoCompleteResultWithoutMatches(childFiles, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix);
      }

      int indexOfFileInResults = results.indexWhere((e) => e.item == fileInParent);
      // This should never happen, but if the index of our exact match is not 0, make it 0.
      // Exact matches should always be first.
      if (indexOfFileInResults != 0) {
        if (indexOfFileInResults == -1) {
          // This should definitely never happen.
          assert(
              false,
              "Prefix matches a child exactly but is not in results: prefix=$prefix, "
              "nameToMatch=$nameToMatch, fileInParent=$fileInParent, results=$results");
          results.insert(0, QuickScoreResult<File>(fileInParent, key: "name", score: 1, matches: [Match(start: 0, end: nameToMatch!.length)]));
        } else {
          final result = results.removeAt(indexOfFileInResults);
          results.insert(0, result);
        }
      }

      // If our exact match doesn't end with a path separator, add one.
      // FIXME: Later we add a separator to all suggestions anyway.
      // final path = results[0].item.path;
      // if (!path.endsWith(Path.pathSeparator)) {
      //   results[0] =
      //       results[0].copyWith(item: results[0].item.withPath(path.append(Path.pathSeparator)));
      // }

      // Add the children of the exact match directly below it.
      results.insertAll(
          1,
          childFiles.files.filter((e) => e.isDirectory).map((e) => QuickScoreResult<File>(e,
              key: "name", score: 1, matches: [Match(start: 0, end: nameToMatch!.length + RawPath.pathSeparator.length + e.name.length)])));
    }

    // We only match the last element of the prefix, so we must adjust the match ranges.
    final prefixLength = nameToMatch == null ? unresolvedPrefix.absolutePath.length : unresolvedPrefix.dir.length + RawPath.pathSeparator.length;

    return AsyncValueSnapshot(
        value: AutoCompleteSuggestions(results.map((e) {
      return SuggestionItem(_replacePrefixOfSuggestion(e.item, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix),
          matches: e.matches.map((match) {
            return TextRange(start: match.start + prefixLength, end: match.end + prefixLength);
          }).toList());
    }).toList()));
  }

  AsyncValue<AutoCompleteSuggestions<File>> _autoCompleteResultWithoutMatches(AsyncValue<List<File>> files,
          {required RawPath unresolvedPrefix, required RawPath resolvedPrefix}) =>
      files.snapshot.map((value) {
        return AutoCompleteSuggestions(value
            .filter((e) => e.isDirectory)
            .map((e) => SuggestionItem(_replacePrefixOfSuggestion(e, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix)))
            .toList());
      });

  // Replaces the path of the suggestion except the last element with the unresolved prefix.
  // This is needed because the path resolver will remove special characters like '..' from the
  // prefix, but we want to preserve them and only auto-complete the last path elements.
  File _replacePrefixOfSuggestion(File suggestion, {required RawPath unresolvedPrefix, required RawPath resolvedPrefix}) {
    assert(suggestion.isDirectory, "Suggestions must only contain directories: $suggestion");
    // assert(resolvedPrefix.isAncestorOf(e.path),
    //     "Suggestions must only contain children of the prefix: $e, prefix=$resolvedPrefix");
    if (resolvedPrefix.isParentOf(suggestion.path)) {
      // The prefix points exactly to a directory and 'e' is a child of that directory.
      return suggestion.withPath(unresolvedPrefix.child(suggestion.name + RawPath.pathSeparator));
    }

    // Special case: when unresolvedPrefix is empty (empty input), use the current directory as prefix
    if (unresolvedPrefix.absolutePath.isEmpty) {
      return suggestion.withPath(resolvedPrefix.child(suggestion.name + RawPath.pathSeparator));
    }

    // if (e.elementNames.length == resolvedPrefix.elementNames.length) {
    final parent = unresolvedPrefix.parent;
    if (kDebugMode) {
      logger.finest(
          "$dir _replacePrefixOfSuggestions(suggestion=$suggestion, unresolvedPrefix=$unresolvedPrefix, resolvedPrefix=$resolvedPrefix): ${parent != null ? suggestion.withPath(parent.child(suggestion.name + RawPath.pathSeparator)) : suggestion}");
    }
    // The prefix does not point exactly to a directory.
    // Replace everything in 'e' except the last element with the unresolved prefix.
    // If the prefix ends with a separator, we must add it to the suggestion.
    final child = parent?.child(suggestion.name + RawPath.pathSeparator);
    if (kDebugMode) {
      logger.finest(
          "$dir _replacePrefixOfSuggestions(suggestion=$suggestion, unresolvedPrefix=$unresolvedPrefix, resolvedPrefix=$resolvedPrefix): child=$child, parent=$parent, e.name=${suggestion.name}");
    }
    return parent != null
        ? suggestion.withPath(child!)
        // parent.child(e.name).append(e.endsWith(Path.pathSeparator) ? Path.pathSeparator : ''))
        : suggestion;
    // }

    // suffix = endsWithSeparator
    //     ? e.name
    //     // Special handling is needed when auto completing a name beginning from dot, because
    //     // it will be removed by the path resolver.
    //     : prefixPathUnresolved.name == '.'
    //         ? e.name.startsWith('.')
    //             ? e.name.substring(1)
    //             : null
    //         : Path.pathSeparator + e.name;
    // return e.withPath(unresolvedPrefix.append(suffix));

    // final suggestions = result.suggestions.map((e) {
    //   final String? suffix;
    //   if (e.elementNames.length == unresolvedPrefix.elementNames.length) {
    //     // e is a direct child of prefix and we are auto-completing the last path element.
    //     suffix = e.name;
    //   } else {
    //     suffix = unresolvedPrefix.absolutePath.endsWith(Path.pathSeparator)
    //         ? e.name
    //         // Special handling is needed when auto completing a name beginning from dot, because
    //         // it will be removed by the path resolver.
    //         : unresolvedPrefix.name == '.'
    //             ? e.name.startsWith('.')
    //                 ? e.name.substring(1)
    //                 : null
    //             : Path.pathSeparator + e.name;
    //   }
    //   return suffix != null ? e.withPath(unresolvedPrefix.append(suffix)) : null;
    // }).toList();
    //
    // return CalcSuggestionsResult(suggestions, matches: result.matches);
  }

  @visibleForTesting
  void dispose() {
    if (kDebugMode) {
      logger.fine('$dir dispose()');
    }
    _autoCompleteSession.unwatchAll();
  }

  final logger = loggerFor(NavigationBarStore, Level.FINE);
}
