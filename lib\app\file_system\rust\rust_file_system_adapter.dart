import 'dart:async';

import '../../../rust/.gen/api/domain.dart' as domain;
import '../../../rust/.gen/api/file_system.dart' as rs;
import '../../domain/path.dart';
import '../../task/task.dart';
import '../file_system.dart';

class RustFileSystemAdapter implements FileSystem {
  const RustFileSystemAdapter();

  @override
  Future<Path> stat(RawPath path) async {
    try {
      final stats = await rs.stat(path: path.absolutePath);
      return Path(
        path,
        _convertPathStatsToDart(stats),
      );
    } catch (e) {
      // Never throws, return a path with error
      return Path(
        path,
        PathStats(
          type: domain.PathStatsType.unknown,
          createTime: zeroDate,
          updateTime: zeroDate,
          accessTime: zeroDate,
          size: null,
          error: Exception(e.toString()),
        ),
      );
    }
  }

  @override
  Future<Stream<Path>> list(RawPath path) async {
    final entries = await rs.list(path: path.absolutePath);
    return entries.map((entry) {
      final (entryPath, stats) = entry;
      return Path(
        RawPath(entryPath),
        _convertPathStatsToDart(stats),
      );
    });
  }

  PathStats _convertPathStatsToDart(domain.PathStats stats) {
    return PathStats(
      type: stats.type,
      createTime: stats.createTime,
      updateTime: stats.modifyTime,
      accessTime: stats.accessTime,
      size: stats.size?.toInt(),
      error: stats.error,
    );
  }

  @override
  Future<void> copyFile(RawPath source, RawPath dest, CopyOptions options) {
    return rs.copyFile(
      source: source.absolutePath,
      dest: dest.absolutePath,
      options: options,
    );
  }

  @override
  Future<CopyWithProgressOperation> copyFileWithProgress(
      RawPath source, RawPath dest, CopyOptions options, FutureOr<void> Function(domain.ProgressReport) onProgress,
      {int chunkSize = FileSystem.defaultCopyChunkSize}) async {
    final completer = Completer<void>();
    final operation = await rs.copyFileWithProgress(
      source: source.absolutePath,
      dest: dest.absolutePath,
      options: options,
      chunkSize: chunkSize,
        onProgress: onProgress,
        onDone: (error) {
          if (error != null) {
            if (error is FileSystemError_Cancelled) {
              completer.completeError(CancellationError());
            } else {
              completer.completeError(error);
            }
          } else {
            completer.complete();
          }
        });
    return RustCopyWithProgressOperation(operation, completer);
  }

  @override
  Future<void> rename(RawPath source, RawPath dest) => rs.rename(source: source.absolutePath, dest: dest.absolutePath);

  @override
  Future<void> deleteFile(RawPath path) => rs.deleteFile(path: path.absolutePath);

  @override
  Future<String> readFile(RawPath path) => rs.readFile(path: path.absolutePath);

  @override
  Future<void> writeFile(RawPath path, String content) => rs.writeFile(path: path.absolutePath, content: content);

  @override
  Future<void> setTimestamps(RawPath path, {DateTime? createTime, DateTime? modifyTime, DateTime? accessTime}) {
    return rs.setTimestamps(
      path: path.absolutePath,
      createTime: createTime,
      modifyTime: modifyTime,
      accessTime: accessTime,
    );
  }

  @override
  Future<void> mkdir(RawPath path, {bool recursive = false}) => rs.mkdir(path: path.absolutePath, recursive: recursive);

  @override
  Future<void> rmdir(RawPath path, {bool recursive = false}) => rs.rmdir(path: path.absolutePath, recursive: recursive);
}

class RustCopyWithProgressOperation implements CopyWithProgressOperation {
  RustCopyWithProgressOperation(this._rs, this._completer);

  final domain.CopyWithProgressOperation _rs;
  final Completer<void> _completer;

  @override
  Future<void> finished() => _completer.future;

  @override
  Future<void> cancel() => _rs.cancel();

  @override
  Future<void> pause() => _rs.pause();

  @override
  Future<void> resume() => _rs.resume();
}
