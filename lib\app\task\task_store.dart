import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';

import '../notification/notification_store.dart';
import '../util/log_utils.dart';
import './file_op_executor.dart';
import './progress.dart';
import './task.dart';
import './task_planner.dart';

part '.gen/task_store.g.dart';

/// A store for managing jobs and tasks
class JobStore = JobStoreBase with _$JobStore;

abstract class JobStoreBase with Store {
  JobStoreBase(
    this._executor,
    this._notificationStore,
    this._planner,
  ) {
    // Process waiting job specs and convert them to ready jobs
    _disposers.add(reaction((_) => _waitingJobSpecs.length, (int length) {
      if (length > 0) {
        _planNextWaitingSpec();
      }
    }).call);

    // When a new job is ready, execute it
    _disposers.add(reaction((_) => _readyJobs.length, (int length) async {
      if (length > 0 && _executingJobs.length < maxConcurrentJobs) {
        final job = _readyJobs.first;
        await _executeReadyJob(job);
      }
    }).call);

    // Track total progress
    _disposers.add(reaction((_) => jobs, (List<Job> jobs) {
      _updateTotalProgress();
    }).call);
  }

  final JobPlanner _planner;

  final FileOpExecutor _executor;

  final NotificationStore _notificationStore;

  /// The waiting job specifications - jobs that have been added but not yet processed
  final ObservableList<JobSpec> _waitingJobSpecs = ObservableList<JobSpec>();

  /// The ready jobs - jobs that have been processed and are ready to be executed
  final ObservableList<Job> _readyJobs = ObservableList<Job>();

  /// The executing jobs - jobs that are currently being executed
  final ObservableList<Job> _executingJobs = ObservableList<Job>();

  /// The hidden jobs (performed in parallel without queuing)
  final ObservableList<Job> _hiddenJobs = ObservableList<Job>();

  /// The total progress of all jobs
  final Progress totalProgress = Progress();

  final List<VoidCallback> _disposers = [];

  /// All tasks from all jobs
  @computed
  List<Task> get tasks => [..._readyJobs, ..._executingJobs].expand((job) => job.tasks).toList();

  /// Maximum number of jobs that can be executed concurrently
  /// This is usually 1, but can be increased for certain types of jobs
  int get maxConcurrentJobs => 1;

  /// Gets the current ready job
  @computed
  Job? get currentReadyJob {
    return _readyJobs.isNotEmpty ? _readyJobs.first : null;
  }

  /// Gets all jobs
  @computed
  List<Job> get jobs {
    return [..._readyJobs, ..._executingJobs, ..._hiddenJobs];
  }

  /// Process the next waiting job spec and convert it to a ready job
  @action
  Future<void> _planNextWaitingSpec() async {
    if (_waitingJobSpecs.isEmpty) return;

    // Get the next waiting job spec
    final jobSpec = _waitingJobSpecs.first;

    if (kDebugMode) {
      logger.info('_planNextWaitingSpec(): $jobSpec');
    }

    try {
      // Plan the job using the JobPlanner
      final Job job = await _planner.plan(jobSpec);

      // Add the fully planned job to the ready queue
      _readyJobs.add(job);
    } catch (error) {
      if (kDebugMode) {
        logger.severe('_planNextWaitingSpec() Error: $error');
      }

      // Show an error notification
      _notificationStore.notifyError('Error planning: ${error.toString()}');
    } finally {
      // Remove the job spec from the waiting queue
      _waitingJobSpecs.remove(jobSpec);
    }
  }

  /// Execute a ready job
  @action
  Future<void> _executeReadyJob(Job job) async {
    if (job.cancelled) return;

    // Move the job from ready to executing
    _readyJobs.remove(job);
    _executingJobs.add(job);

    try {
      if (kDebugMode) {
        logger.info('_executeReadyJob($job): Executing...');
      }

      // Execute all tasks in the job
      for (final task in job.tasks) {
        if (job.cancelled) break;

        try {
          // Execute all operations in the task
          await _executeTask(task);
          task.complete();
        } catch (error) {
          if (kDebugMode) {
            logger.severe('_executeReadyJob($job) Error: $error');
          }
          task.error = error;
          continue; // Continue with next task even if this one failed
        }
      }

      // Mark the job as complete if it wasn't cancelled
      if (!job.cancelled) {
        job.complete();
      }
    } finally {
      // Remove the job from the executing queue
      _executingJobs.remove(job);

      // Update total progress
      _updateTotalProgress();
    }
  }

  /// Creates a job buffer for preparing and storing jobs
  /// A buffer for preparing and storing jobs before committing them to the store
  JobBuffer createBuffer() {
    return JobBuffer(this);
  }

  /// Executes a task and all its operations
  @action
  Future<void> _executeTask(Task task) async {
    if (kDebugMode) {
      logger.info('_executeTask($task): Executing...');
    }

    try {
      // Execute the operations for this task
      for (final rootOp in task.rootOps) {
        if (task.cancelled) break;
        // Execute the operation using the executor
        await _executor.execute(rootOp);
      }
    } catch (error) {
      logger.severe('_executeTask($task) Error: $error');
      task.error = error;
      _notificationStore.notifyError('Error executing task: ${error.toString()}');
      rethrow; // Propagate error to job level
    }
  }

  /// Updates the total progress of all jobs
  void _updateTotalProgress() {
    totalProgress.reset();
    for (final job in jobs) {
      final progress = job.progress;
      // Add the totals and processed values from the job's progress
      totalProgress.addTotals(progress);
      totalProgress.addBytesProcessed(progress.bytesProcessed);
      totalProgress.addFilesProcessed(progress.filesProcessed);
    }
  }

  /// Adds a job spec to the waiting queue
  @action
  void addJobSpec(JobSpec spec) {
    _waitingJobSpecs.add(spec);
  }

  /// Adds a job directly to the ready queue
  @action
  void _addJob(Job job) {
    _readyJobs.add(job);
  }

  /// Adds a hidden job that runs in parallel without queuing
  @action
  void addHiddenJob(Job job) {
    _hiddenJobs.add(job);
    _executeJob(job);
  }

  /// Executes a hidden job immediately without queuing
  Future<void> _executeJob(Job job) async {
    try {
      // Plan the job using the JobPlanner
      final Job plannedJob = await _planner.plan(job.spec);

      // Add the fully planned job to the hidden jobs list
      _hiddenJobs.add(plannedJob);

      // Execute all tasks in the job
      for (final task in plannedJob.tasks) {
        await _executeTask(task);
      }

      // Mark the job as complete
      plannedJob.complete();
    } catch (e) {
      job.error = e;
      _notificationStore.notifyError('Error executing job: ${e.toString()}');
    } finally {
      // Remove the job from the hidden jobs list
      _hiddenJobs.remove(job);
    }
  }

  /// Cancels a job
  @action
  void cancelJob(Job job) {
    job.cancel();

    // If it's the current job, it will be removed in executeJob
    // If it's not the current job, remove it from the queue
    if (job != currentReadyJob) {
      _readyJobs.remove(job);
    }

    // Remove from hidden jobs if present
    _hiddenJobs.remove(job);

    // Update total progress
    _updateTotalProgress();
  }

  /// Pauses a job
  @action
  void pauseJob(Job job) {
    job.pause();
  }

  /// Resumes a job
  @action
  void resumeJob(Job job) {
    job.resume();
  }

  @action
  @visibleForTesting
  void dispose() {
    for (final disposer in _disposers) {
      disposer();
    }
    _disposers.clear();

    _waitingJobSpecs.clear();
    _readyJobs.clear();
    _executingJobs.clear();
    _hiddenJobs.clear();

    _planner.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    _executor.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    _notificationStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
  }

  static final logger = loggerFor(JobStore);
}

/// A buffer for preparing and storing jobs before committing them to the store
/// This allows grouping multiple jobs together and committing them as a batch
class JobBuffer {
  final JobStoreBase _store;

  /// Job specifications to add to the waiting jobs queue
  final List<JobSpec> _jobSpecs = [];

  /// Jobs to add directly to the ready jobs queue
  final List<Job> _jobs = [];

  /// Creates a job buffer
  JobBuffer(this._store);

  /// Adds a job specification to the buffer
  /// This will be added to the waiting jobs queue when committed
  void addJobSpec(JobSpec spec) {
    _jobSpecs.add(spec);
  }

  /// Adds a job directly to the buffer
  /// This will be added to the ready jobs queue when committed
  void addJob(Job job) {
    _jobs.add(job);
  }

  /// Commits all jobs in the buffer to the store
  void commit() {
    for (final jobSpec in _jobSpecs) {
      _store.addJobSpec(jobSpec);
    }

    // Add all jobs to the store
    for (final job in _jobs) {
      _store._addJob(job);
    }

    // Clear the buffer
    clear();
  }

  /// Clears all jobs from the buffer without committing them
  void clear() {
    _jobSpecs.clear();
    _jobs.clear();
  }
}
