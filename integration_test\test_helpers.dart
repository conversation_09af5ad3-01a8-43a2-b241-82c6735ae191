import 'dart:io' as io;

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/app/file_system/rust/rust_file_system_adapter.dart';
import 'package:qfiler/presentation/app.dart';
import 'package:qfiler/presentation/directory_view/directory_view_store.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/history/directory_state.dart';
import 'package:qfiler/presentation/history/history.dart';
import 'package:qfiler/presentation/history/history_store.dart';
import 'package:qfiler/presentation/pane/pane_store.dart';
import 'package:qfiler/presentation/root_store.dart';
import 'package:tsid_dart/tsid_dart.dart';

final fs = RustFileSystemAdapter();

Future<TestState> prepareTest(
  WidgetTester tester, {
  String? name,
  List<TestFile>? leftFiles,
  List<TestFile>? rightFiles,
  HistoryStoreStateSnapshot Function(RawPath)? leftHistory,
  HistoryStoreStateSnapshot Function(RawPath)? rightHistory,
}) async {
  // Create temporary directories
  name ??= 'test_${Tsid.getTsid()}';
  final baseDir = io.Directory(p.join(io.Directory.systemTemp.path, name));
  if (await baseDir.exists()) {
    // Despite having a teardown that does this, this can still happen, if we manually stop the test through a debugger.
    await baseDir.delete(recursive: true);
  }
  await baseDir.create();
  addTearDown(() => baseDir.delete(recursive: true));

  final [leftDir, rightDir, dataDir] = await Future.wait([
    io.Directory(p.join(baseDir.path, 'left')).create(),
    io.Directory(p.join(baseDir.path, 'right')).create(),
    io.Directory(p.join(baseDir.path, 'data')).create(),
  ]);

  // Create test files
  await Future.wait([
    _createTestFiles(leftDir, leftFiles ?? [TestFile(name: 'file1.txt'), TestFile(name: 'file2.txt')]),
    _createTestFiles(rightDir, rightFiles ?? [TestFile(name: 'file3.txt')]),
  ]);

  // Set parent directories to a fixed timestamp
  final fixedDate = DateTime(2025, 6, 1, 10, 0, 0);
  await Future.wait([
    fs.setTimestamps(leftDir.path.asPath(), createTime: fixedDate, modifyTime: fixedDate, accessTime: fixedDate),
    fs.setTimestamps(rightDir.path.asPath(), createTime: fixedDate, modifyTime: fixedDate, accessTime: fixedDate),
  ]);

  // Create RootStore
  final initialLeftHistory = leftHistory?.call(leftDir.path.asPath()) ??
      HistoryStoreStateSnapshot([
        History([DirectoryState(leftDir.path.asPath())])
      ]);
  final initialRightHistory = rightHistory?.call(rightDir.path.asPath()) ??
      HistoryStoreStateSnapshot([
        History([DirectoryState(rightDir.path.asPath())])
      ]);
  final rootStore = await RootStore.create(
    dataDir: dataDir.path.asPath(),
    leftHistory: initialLeftHistory,
    rightHistory: initialRightHistory,
  );
  addTearDown(rootStore.dispose);

  // Pump the app
  await tester.pumpWidget(App(rootStore));
  await tester.pumpAndSettle();

  final test = TestState(
    debugLabel: name,
    baseDir: baseDir.path.asPath(),
    leftDir: leftDir.path.asPath(),
    rightDir: rightDir.path.asPath(),
    dataDir: dataDir.path.asPath(),
    rootStore: rootStore,
    tester: tester,
  );
  test.assertReady();
  return test;
}

/// Pump app and wait for it to settle
Future<void> pumpApp(WidgetTester tester, RootStore rootStore, {Duration? settleTimeout}) async {
  await tester.pumpWidget(App(rootStore));
  await tester.pumpAndSettle(settleTimeout ?? const Duration(seconds: 3));
}

/// Send key and wait for processing
Future<void> sendKeyAndWait(WidgetTester tester, LogicalKeyboardKey key, {Duration? waitTime}) async {
  await tester.sendKeyEvent(key);
  if (waitTime != null) {
    await tester.pumpAndSettle(waitTime);
  } else {
    await tester.pump();
  }
}

/// Debug current state
void debugCurrentState(TestState test, {String? label}) {
  if (kDebugMode) {
    final prefix = label != null ? '[$label] ' : '';
    print('$prefix=== DEBUG STATE ===');
    print('${prefix}Left pane is source: ${test.rootStore.allPanesStore.left.isSource}');
    print('${prefix}Right pane is source: ${test.rootStore.allPanesStore.right.isSource}');
    print('${prefix}Source pane side: ${test.rootStore.allPanesStore.sourcePaneSide}');
    print('${prefix}Left pane files: ${test.rootStore.allPanesStore.left.directoryViewStore.files.length}');
    print('${prefix}Left pane focused index: ${test.rootStore.allPanesStore.left.directoryViewStore.focusedRowIndex}');
    print('${prefix}Right pane files: ${test.rootStore.allPanesStore.right.directoryViewStore.files.length}');
    print('${prefix}Right pane focused index: ${test.rootStore.allPanesStore.right.directoryViewStore.focusedRowIndex}');
  }
}

/// Create hierarchical test file structure
Future<void> _createTestFiles(io.Directory baseDir, List<TestFile> testFiles) async {
  for (int i = 0; i < testFiles.length; i++) {
    final testFile = testFiles[i];
    final fullPath = p.join(baseDir.path, testFile.name);

    if (testFile.isDirectory == true) {
      // Create directory
      final dir = await io.Directory(fullPath).create(recursive: true);

      // Set timestamps if provided
      if (testFile.createTime != null || testFile.modifyTime != null || testFile.accessTime != null) {
        final createTime = testFile.createTime ?? DateTime(2025, 6, 1);
        final modifyTime = testFile.modifyTime ?? DateTime(2025, 6, 1);
        final accessTime = testFile.accessTime ?? DateTime(2025, 6, 1);

        await fs.setTimestamps(
          dir.path.asPath(),
          createTime: createTime,
          modifyTime: modifyTime,
          accessTime: accessTime,
        );
      }

      // Create children if any
      if (testFile.children != null && testFile.children!.isNotEmpty) {
        await _createTestFiles(dir, testFile.children!);
      }
    } else {
      // Create file
      final file = await io.File(fullPath).writeAsString('Test file content for ${testFile.name}');

      // Set timestamps if provided
      if (testFile.createTime != null || testFile.modifyTime != null || testFile.accessTime != null) {
        await fs.setTimestamps(
          file.path.asPath(),
          createTime: testFile.createTime,
          modifyTime: testFile.modifyTime,
          accessTime: testFile.accessTime,
        );
      }
    }
  }
}

/// Test setup data
class TestState {
  final String debugLabel;
  final RawPath baseDir;
  final RawPath leftDir;
  final RawPath rightDir;
  final RawPath dataDir;
  final RootStore rootStore;
  final WidgetTester tester;

  TestState({
    required this.debugLabel,
    required this.baseDir,
    required this.leftDir,
    required this.rightDir,
    required this.dataDir,
    required this.rootStore,
    required this.tester,
  });

  PaneStore paneStore(Side side) => side == Side.left ? rootStore.allPanesStore.left : rootStore.allPanesStore.right;
  DirectoryViewStore directoryViewStore(Side side) => paneStore(side).directoryViewStore;
  HistoryStore historyStore(Side side) => paneStore(side).historyStore;

  Future<void> sendKeyAndWait(LogicalKeyboardKey key, {Duration? waitTime}) async {
    await tester.sendKeyEvent(key);
    if (waitTime != null) {
      await tester.pumpAndSettle(waitTime);
    } else {
      await tester.pump();
    }
    assertReady();
  }

  void assertReady({Side? side}) {
    final sides = side == null ? const [Side.left, Side.right] : [side];
    for (final side in sides) {
      expect(directoryViewStore(side).isReady, isTrue, reason: '${side.name} pane directory view is not ready');
    }
  }
}

/// Represents a test file or directory with optional metadata
class TestFile {
  final String name;
  final bool? isDirectory;
  final DateTime? createTime;
  final DateTime? modifyTime;
  final DateTime? accessTime;
  final List<TestFile>? children;

  const TestFile({
    required this.name,
    this.isDirectory,
    this.createTime,
    this.modifyTime,
    this.accessTime,
    this.children,
  });

  bool get isDir => isDirectory ?? false;
  bool get isFile => !isDir;
}
