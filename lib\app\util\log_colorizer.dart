import 'package:logging/logging.dart';

/// Maps log levels to ANSI color codes for colorized output.
final Map<Level, String> levelColors = {
  Level.FINEST: '\x1B[38;5;244m', // Gray
  Level.FINER: '\x1B[38;5;244m',
  Level.FINE: '\x1B[38;5;244m',
  Level.CONFIG: '\x1B[38;5;39m', // Blue
  Level.INFO: '\x1B[38;5;34m', // Green
  Level.WARNING: '\x1B[38;5;214m', // Orange
  Level.SEVERE: '\x1B[38;5;196m', // Red
  Level.SHOUT: '\x1B[48;5;196m\x1B[38;5;15m', // Red background, white text
};

abstract class LogAstNode {
  const LogAstNode();
}

/// A root node that contains multiple nodes without adding any brackets
class RootNode extends LogAstNode {
  final List<LogAstNode> nodes;
  RootNode(this.nodes);
}

class FunctionCallNode extends LogAstNode {
  final String name;
  final List<LogAstNode> params;
  FunctionCallNode(this.name, this.params);
}

class ParameterNode extends LogAstNode {
  final String key;
  final String separator; // '=' or ':'
  final LogAstNode value;
  ParameterNode(this.key, this.separator, this.value);
}

class BracketGroupNode extends LogAstNode {
  final String open;
  final String close;
  final List<LogAstNode> content;
  BracketGroupNode(this.open, this.close, this.content);
}

class ArrowNode extends LogAstNode {
  final LogAstNode? source; // Make source nullable
  final LogAstNode target;
  ArrowNode(this.source, this.target);
}

class LiteralNode extends LogAstNode {
  final String text;
  LiteralNode(this.text);
}

enum TokenType {
  identifier,
  lParen,
  rParen,
  lBracket,
  rBracket,
  lBrace,
  rBrace,
  comma,
  colon,
  equals,
  arrow,
  string,
  whitespace,
  unknown,
  eof,
}

class Token {
  final TokenType type;
  final String value;
  final int position;
  Token(this.type, this.value, this.position);
  @override
  String toString() => 'Token($type, "$value", pos=$position)';
}

class LogTokenizer {
  final String input;
  int pos = 0;
  LogTokenizer(this.input);

  // Include space and common characters in identifiers to handle filenames with spaces and special patterns
  // Include colon and backslash for Windows paths
  static final _idReg = RegExp(r"[a-zA-Z0-9_.$\-':\ \\]+");

  Token nextToken() {
    if (pos >= input.length) return Token(TokenType.eof, '', pos);
    final c = input[pos];
    if (c == '(') return _advance(TokenType.lParen, c);
    if (c == ')') return _advance(TokenType.rParen, c);
    if (c == '[') return _advance(TokenType.lBracket, c);
    if (c == ']') return _advance(TokenType.rBracket, c);
    if (c == '{') return _advance(TokenType.lBrace, c);
    if (c == '}') return _advance(TokenType.rBrace, c);
    if (c == ',') return _advance(TokenType.comma, c);
    if (c == ':') return _advance(TokenType.colon, c);
    if (c == '=') return _advance(TokenType.equals, c);
    // Handle arrow (->) as a special token type
    if (c == '-' && pos + 1 < input.length && input[pos + 1] == '>') {
      final t = Token(TokenType.arrow, '->', pos);
      pos += 2;
      return t;
    }
    if (c == '"' || c == '\'') {
      final start = pos;
      final quote = c;
      pos++;
      while (pos < input.length && input[pos] != quote) {
        pos++;
      }
      if (pos < input.length) pos++; // skip closing quote
      return Token(TokenType.string, input.substring(start, pos), start);
    }
    if (c.trim().isEmpty) {
      final start = pos;
      while (pos < input.length && input[pos].trim().isEmpty) {
        pos++;
      }
      return Token(TokenType.whitespace, input.substring(start, pos), start);
    }
    final idMatch = _idReg.matchAsPrefix(input, pos);
    if (idMatch != null) {
      final value = idMatch.group(0)!;
      final start = pos;
      pos += value.length;
      return Token(TokenType.identifier, value, start);
    }
    // Unknown char
    return _advance(TokenType.unknown, c);
  }

  Token _advance(TokenType type, String value) {
    final t = Token(type, value, pos);
    pos++;
    return t;
  }
}

class LogParser {
  final LogTokenizer tokenizer;
  late Token _current;

  LogParser(String input) : tokenizer = LogTokenizer(input) {
    _current = tokenizer.nextToken();
  }

  // Helper method to check if a string is a Windows drive letter (e.g., "C:", "D:")
  bool _isWindowsDriveLetter(String text) {
    if (text.isEmpty) return false;
    final lastChar = text[text.length - 1];
    // Check if the last character is a drive letter
    return (lastChar == 'C' ||
        lastChar == 'D' ||
        lastChar == 'E' ||
        lastChar == 'F' ||
        lastChar == 'c' ||
        lastChar == 'd' ||
        lastChar == 'e' ||
        lastChar == 'f');
  }

  void _advance() {
    _current = tokenizer.nextToken();
  }

  // Look ahead at the next token without consuming it
  Token _peek() {
    final currentPos = tokenizer.pos;
    final currentToken = _current;

    // Get the next token
    final nextToken = tokenizer.nextToken();

    // Reset the tokenizer position
    tokenizer.pos = currentPos;
    _current = currentToken;

    return nextToken;
  }

  bool _eat(TokenType type) {
    if (_current.type == type) {
      _advance();
      return true;
    }
    return false;
  }

  LogAstNode parse() {
    final nodes = <LogAstNode>[];

    // Special handling for standalone result statements at the beginning of the input
    if (_current.type == TokenType.identifier) {
      final name = _current.value;
      if ((name.toLowerCase() == 'result' || name.toLowerCase() == 'results') && _peek().type == TokenType.colon) {
        final resultKey = name;
        _advance(); // consume 'result'
        final separator = _current.value;
        _advance(); // consume ':'

        // Skip any whitespace after the colon
        if (_current.type == TokenType.whitespace) {
          _advance();
        }

        // Parse the value (could be a complex expression)
        final value = _parseNode();

        // Create a parameter node for the result
        nodes.add(ParameterNode(resultKey, separator, value));
      }
    }

    // Parse the rest of the input
    while (_current.type != TokenType.eof) {
      nodes.add(_parseNode());
    }

    // Return the single node directly if only one
    if (nodes.length == 1) {
      return nodes[0];
    }
    // If all nodes are whitespace, join as a single LiteralNode
    if (nodes.every((n) => n is LiteralNode && (n).text.trim().isEmpty)) {
      return LiteralNode(nodes.map((n) => (n as LiteralNode).text).join());
    }
    // Use RootNode instead of BracketGroupNode to avoid adding empty brackets
    return RootNode(nodes);
  }

  LogAstNode _parseNode() {
    // Function call: identifier + '('
    if (_current.type == TokenType.identifier) {
      final name = _current.value;

      // Special handling for standalone result statements (e.g., "Result: success")
      if ((name.toLowerCase() == 'result' || name.toLowerCase() == 'results') && _peek().type == TokenType.colon) {
        final resultKey = name;
        _advance(); // consume 'result'
        final separator = _current.value;
        _advance(); // consume ':'

        // Skip any whitespace after the colon
        if (_current.type == TokenType.whitespace) {
          _advance();
        }

        // Parse the value (could be a complex expression)
        final value = _parseNode();

        // Create a parameter node for the result
        return ParameterNode(resultKey, separator, value);
      }

      _advance();

      if (_eat(TokenType.lParen)) {
        // Parse params until ')'
        final params = <LogAstNode>[];
        if (_current.type != TokenType.rParen) {
          do {
            // Skip any whitespace before the parameter
            if (_current.type == TokenType.whitespace) {
              _advance();
            }

            // Add the parameter
            params.add(_parseParamOrExpr());

            // Skip any whitespace after the parameter
            if (_current.type == TokenType.whitespace) {
              _advance();
            }
          } while (_eat(TokenType.comma));
        }
        _eat(TokenType.rParen);

        // Check if there are curly braces immediately after the function call
        // but don't add them as a separate parameter with a comma
        if (_current.type == TokenType.lBrace) {
          // Capture the braces content
          final startPos = tokenizer.pos;
          _advance(); // Consume the opening brace

          // Capture everything until the closing brace
          int braceDepth = 1;
          while (braceDepth > 0 && _current.type != TokenType.eof) {
            if (_current.type == TokenType.lBrace) {
              braceDepth++;
            } else if (_current.type == TokenType.rBrace) {
              braceDepth--;
            }

            if (braceDepth > 0) {
              _advance();
            }
          }

          // Get the content between braces
          final endPos = _current.type == TokenType.rBrace ? tokenizer.pos : tokenizer.input.length;
          final bracesContent = tokenizer.input.substring(startPos - 1, endPos + 1); // Include the braces

          // Consume the closing brace
          _eat(TokenType.rBrace);

          // If we have parameters, append the braces to the last parameter
          if (params.isNotEmpty) {
            final lastParam = params.last;
            if (lastParam is LiteralNode) {
              // Replace the last parameter with the combined content
              params[params.length - 1] = LiteralNode(lastParam.text + bracesContent);
            } else {
              // Create a root node with both the last parameter and the braces
              final lastParamNode = params.removeLast();
              final combinedNode = RootNode([lastParamNode, LiteralNode(bracesContent)]);
              params.add(combinedNode);
            }
          } else {
            // If there are no parameters, add the braces content as a parameter
            params.add(LiteralNode(bracesContent));
          }
        }

        return FunctionCallNode(name, params);
      }
      // Not a function call, just an identifier
      return LiteralNode(name);
    }
    // Bracket groups - parse the content properly
    if (_eat(TokenType.lBracket)) {
      final content = <LogAstNode>[];

      // Capture all content between brackets as a single string
      final startPos = tokenizer.pos;
      int bracketDepth = 0;

      while (_current.type != TokenType.rBracket && _current.type != TokenType.eof) {
        if (_current.type == TokenType.lBracket) {
          bracketDepth++;
        } else if (_current.type == TokenType.rBracket) {
          bracketDepth--;
          if (bracketDepth < 0) break; // We've reached the closing bracket
        }
        _advance();
      }

      // Get the raw content between brackets
      final endPos = tokenizer.pos;
      if (endPos > startPos) {
        final rawContent = tokenizer.input.substring(startPos, endPos);
        content.add(LiteralNode(rawContent));
      }

      _eat(TokenType.rBracket);

      // Check if there's a function call immediately after the bracket group
      // This handles cases like [ID]Job(TaskType.rename)
      if (_current.type == TokenType.identifier) {
        // Store the current position and the identifier
        final currentPos = tokenizer.pos;
        final identifier = _current.value;
        _advance();

        if (_current.type == TokenType.lParen) {
          // This is a function call after a bracket group
          // Create the bracket node
          final bracketNode = BracketGroupNode('[', ']', content);

          // Parse the function call parameters
          final params = <LogAstNode>[];
          _advance(); // Consume the opening parenthesis

          if (_current.type != TokenType.rParen) {
            do {
              // Skip any whitespace before the parameter
              if (_current.type == TokenType.whitespace) {
                _advance();
              }

              // Add the parameter
              params.add(_parseParamOrExpr());

              // Skip any whitespace after the parameter
              if (_current.type == TokenType.whitespace) {
                _advance();
              }
            } while (_eat(TokenType.comma));
          }
          _eat(TokenType.rParen);

          // Create the function call node
          final functionCallNode = FunctionCallNode(identifier, params);

          // Create a root node with both parts
          return RootNode([bracketNode, functionCallNode]);
        } else {
          // Not a function call, just return the bracket group
          // Reset the position to before we consumed the identifier
          tokenizer.pos = currentPos;
          _current = tokenizer.nextToken(); // Reset the current token
          return BracketGroupNode('[', ']', content);
        }
      }

      // Check if there are curly braces immediately after the bracket group
      if (_current.type == TokenType.lBrace) {
        // Create the bracket node
        final bracketNode = BracketGroupNode('[', ']', content);

        // Capture the braces content
        final startPos = tokenizer.pos;
        _advance(); // Consume the opening brace

        // Capture everything until the closing brace
        int braceDepth = 1;
        while (braceDepth > 0 && _current.type != TokenType.eof) {
          if (_current.type == TokenType.lBrace) {
            braceDepth++;
          } else if (_current.type == TokenType.rBrace) {
            braceDepth--;
          }

          if (braceDepth > 0) {
            _advance();
          }
        }

        // Get the content between braces
        final endPos = _current.type == TokenType.rBrace ? tokenizer.pos : tokenizer.input.length;
        final bracesContent = tokenizer.input.substring(startPos - 1, endPos + 1); // Include the braces

        // Consume the closing brace
        _eat(TokenType.rBrace);

        // Create a literal node for the braces content
        final bracesNode = LiteralNode(bracesContent);

        // Create a root node with both parts
        return RootNode([bracketNode, bracesNode]);
      }

      return BracketGroupNode('[', ']', content);
    }

    if (_eat(TokenType.lBrace)) {
      final content = <LogAstNode>[];

      // Parse content until closing brace
      while (_current.type != TokenType.rBrace && _current.type != TokenType.eof) {
        content.add(_parseNode());
      }

      _eat(TokenType.rBrace);
      return BracketGroupNode('{', '}', content);
    }

    // Parenthesis as group (not function call)
    if (_eat(TokenType.lParen)) {
      // Capture the raw content between parentheses
      final startPos = tokenizer.pos;
      int parenDepth = 1;

      while (parenDepth > 0 && _current.type != TokenType.eof) {
        if (_current.type == TokenType.lParen) {
          parenDepth++;
        } else if (_current.type == TokenType.rParen) {
          parenDepth--;
        }

        if (parenDepth > 0) {
          _advance();
        }
      }

      // Get the raw content
      final endPos = _current.type == TokenType.rParen ? tokenizer.pos : tokenizer.input.length;
      final rawContent = tokenizer.input.substring(startPos, endPos);

      // Parse the content
      final content = <LogAstNode>[];
      if (rawContent.trim().isNotEmpty) {
        content.add(LiteralNode(rawContent));
      }

      _eat(TokenType.rParen);
      return BracketGroupNode('(', ')', content);
    }
    // String literal
    if (_current.type == TokenType.string) {
      final value = _current.value;
      _advance();
      return LiteralNode(value);
    }
    // Whitespace (preserve for now)
    if (_current.type == TokenType.whitespace) {
      final value = _current.value;
      _advance();
      return LiteralNode(value);
    }
    // Special tokens that should be preserved as literals
    if (_current.type == TokenType.colon || _current.type == TokenType.equals) {
      final value = _current.value;
      _advance();
      return LiteralNode(value);
    }

    // Special handling for arrow tokens
    if (_current.type == TokenType.arrow) {
      // Consume the arrow token
      _advance();

      // Skip any whitespace after the arrow
      if (_current.type == TokenType.whitespace) {
        _advance();
      }

      // Parse the target (right side of the arrow)
      final target = _parseNode();

      // Create an ArrowNode with the previous node as source and the current node as target
      // We don't have the source node here, so we'll return a special ArrowNode with null source
      // The source will be set by the caller if available
      return ArrowNode(null, target);
    }

    // Fallback: literal for unknown/other tokens
    final value = _current.value;
    _advance();
    return LiteralNode(value);
  }

  LogAstNode _parseParamOrExpr() {
    // Special handling for parameters with spaces and parentheses
    if (_current.type == TokenType.identifier) {
      final key = _current.value;
      _advance();

      // Handle parameters with equals or colon as key-value pairs
      if (_current.type == TokenType.equals || (_current.type == TokenType.colon && !_isWindowsDriveLetter(key))) {
        final sep = _current.value;
        _advance();

        // Skip any whitespace after the separator
        if (_current.type == TokenType.whitespace) {
          _advance();
        }

        final value = _parseNode();
        return ParameterNode(key, sep, value);
      }

      // Handle parameters with parentheses, spaces, etc.
      // Keep consuming tokens until we hit a comma or closing parenthesis
      final paramBuffer = StringBuffer(key);
      int parenthesisDepth = 0;
      int braceDepth = 0;
      int bracketDepth = 0;

      while (
          (_current.type != TokenType.comma && (_current.type != TokenType.rParen || parenthesisDepth > 0) && _current.type != TokenType.eof) ||
              braceDepth > 0 ||
              bracketDepth > 0) {
        // Track parenthesis nesting to handle filenames with parentheses
        if (_current.type == TokenType.lParen) {
          parenthesisDepth++;
          paramBuffer.write(_current.value);
        } else if (_current.type == TokenType.rParen) {
          parenthesisDepth--;
          if (parenthesisDepth >= 0) {
            paramBuffer.write(_current.value);
          } else {
            // We've reached the end of the parameter list
            break;
          }
        }
        // Track brace nesting to handle curly braces in parameters
        else if (_current.type == TokenType.lBrace) {
          braceDepth++;
          paramBuffer.write(_current.value);
        } else if (_current.type == TokenType.rBrace) {
          braceDepth--;
          paramBuffer.write(_current.value);
        }
        // Track bracket nesting to handle square brackets in parameters
        else if (_current.type == TokenType.lBracket) {
          bracketDepth++;
          paramBuffer.write(_current.value);
        } else if (_current.type == TokenType.rBracket) {
          bracketDepth--;
          paramBuffer.write(_current.value);
        } else if (_current.type == TokenType.colon) {
          // Handle colons in paths like D:\path
          paramBuffer.write(_current.value);
        } else {
          paramBuffer.write(_current.value);
        }

        _advance();
      }

      return LiteralNode(paramBuffer.toString());
    }

    // Otherwise, just parse as expression
    return _parseNode();
  }
}

class LogColorizer {
  final bool debug;
  final int parenLevel;
  final int bracketLevel;
  final int braceLevel;

  LogColorizer({
    this.debug = false,
    this.parenLevel = 0,
    this.bracketLevel = 0,
    this.braceLevel = 0,
  });

  static const _resetColor = '\x1B[0m';
  static const _functionCallColor = '\x1B[38;5;105m'; // Purple for function calls
  static const _paramKeyColor = '\x1B[38;5;208m'; // Orange for parameter keys
  static const _paramEqualsColor = '\x1B[38;5;244m'; // Gray for equals signs
  static const _paramValueColor = '\x1B[38;5;45m'; // Cyan for parameter values
  static const _functionParamColor = '\x1B[38;5;221m'; // Yellow for function parameters (not too bright)
  static const _nullValueColor = '\x1B[38;5;160m'; // Darker red for null values
  static const _resultValueColor = '\x1B[38;5;34m'; // Green for result values

  // Rainbow colors for nested brackets
  static const _bracketColors = [
    '\x1B[38;5;141m', // Purple
    '\x1B[38;5;208m', // Orange
    '\x1B[38;5;118m', // Green
    '\x1B[38;5;81m', // Cyan
    '\x1B[38;5;203m', // Pink
  ];

  static const _parenColor = '\x1B[38;5;117m';

  String colorize(LogAstNode node) {
    if (node is FunctionCallNode) {
      return _colorizeFunctionCall(node);
    } else if (node is ParameterNode) {
      return _colorizeParameter(node);
    } else if (node is BracketGroupNode) {
      return _colorizeBracketGroup(node);
    } else if (node is ArrowNode) {
      return _colorizeArrow(node);
    } else if (node is LiteralNode) {
      return _colorizeLiteral(node);
    } else if (node is RootNode) {
      // For RootNode, just concatenate the colorized content of each node
      final buffer = StringBuffer();
      for (final n in node.nodes) {
        buffer.write(colorize(n));
      }
      return buffer.toString();
    } else {
      return node.toString();
    }
  }

  String _colorizeArrow(ArrowNode node) {
    final buffer = StringBuffer();

    // Add source if it exists
    if (node.source != null) {
      buffer.write(colorize(node.source!));
    }

    // Add arrow with symbol tag - preserve original spacing
    buffer.write(_wrap('->', ansi: _paramEqualsColor, debugTag: 'symbol'));

    // Add target
    buffer.write(colorize(node.target));

    return buffer.toString();
  }

  String _wrap(String value, {required String ansi, required String debugTag}) {
    if (debug) {
      return '[c:$debugTag]$value[/c:$debugTag]';
    } else {
      return '$ansi$value$_resetColor';
    }
  }

  String _colorizeFunctionCall(FunctionCallNode node) {
    final b = StringBuffer();

    // Function name
    b.write(_wrap(node.name, ansi: _functionCallColor, debugTag: 'func'));

    // Opening parenthesis with level
    final parenTag = 'paren_$parenLevel';
    b.write(_wrap('(', ansi: _parenColor, debugTag: parenTag));

    // Parameters
    for (int i = 0; i < node.params.length; i++) {
      final param = node.params[i];

      // Create new colorizer with incremented levels for nested structures
      final nestedColorizer = LogColorizer(
        debug: debug,
        parenLevel: parenLevel + 1,
        bracketLevel: bracketLevel + 1,
        braceLevel: braceLevel + 1,
      );

      b.write(nestedColorizer.colorize(param));

      if (i < node.params.length - 1) {
        b.write(_wrap(',', ansi: _paramEqualsColor, debugTag: 'symbol'));
        // Don't add space - preserve original spacing
      }
    }

    // Closing parenthesis
    b.write(_wrap(')', ansi: _parenColor, debugTag: parenTag));

    return b.toString();
  }

  String _colorizeParameter(ParameterNode node) {
    final b = StringBuffer();
    final key = node.key.toLowerCase();

    // Check if this is a result parameter (case-insensitive)
    final isResultParam = key == 'result' || key == 'results';

    if (isResultParam) {
      b.write(_wrap(node.key, ansi: _paramKeyColor, debugTag: 'resultKey'));
    } else {
      b.write(_wrap(node.key, ansi: _paramKeyColor, debugTag: 'paramKey'));
    }

    b.write(_wrap(node.separator, ansi: _paramEqualsColor, debugTag: 'symbol'));

    // Create nested colorizer for the value
    final nestedColorizer = LogColorizer(
      debug: debug,
      parenLevel: parenLevel,
      bracketLevel: bracketLevel,
      braceLevel: braceLevel,
    );

    if (node.value is LiteralNode) {
      final literalNode = node.value as LiteralNode;
      final text = literalNode.text.trim();

      // Special handling for null values
      if (text == 'null') {
        b.write(_wrap(literalNode.text, ansi: _nullValueColor, debugTag: 'null'));
      } else if (isResultParam) {
        // Special handling for result values
        b.write(_wrap(literalNode.text, ansi: _resultValueColor, debugTag: 'resultVal'));
      } else {
        b.write(_wrap(literalNode.text, ansi: _paramValueColor, debugTag: 'paramVal'));
      }
    } else if (isResultParam && node.value is BracketGroupNode) {
      // For result arrays, we need special handling
      final bracketNode = node.value as BracketGroupNode;
      b.write(_colorizeResultBracketGroup(bracketNode));
    } else {
      b.write(nestedColorizer.colorize(node.value));
    }

    return b.toString();
  }

  String _colorizeResultBracketGroup(BracketGroupNode node) {
    final b = StringBuffer();
    final levelTag = '${node.open == '[' ? 'bracket' : node.open == '{' ? 'brace' : 'paren'}_$bracketLevel';

    b.write(_wrap(node.open, ansi: _bracketColors[bracketLevel % _bracketColors.length], debugTag: levelTag));

    if (node.content.isNotEmpty) {
      for (int i = 0; i < node.content.length; i++) {
        final content = node.content[i];
        if (content is LiteralNode) {
          // Split by commas and handle each part
          final parts = content.text.split(',');
          for (int j = 0; j < parts.length; j++) {
            final part = parts[j].trim();
            if (part.isNotEmpty) {
              b.write(_wrap(j == 0 ? parts[j] : ' ${parts[j]}', ansi: _resultValueColor, debugTag: 'resultVal'));
            }
            if (j < parts.length - 1) {
              b.write(_wrap(',', ansi: _paramEqualsColor, debugTag: 'symbol'));
            }
          }
        } else {
          final nestedColorizer = LogColorizer(
            debug: debug,
            parenLevel: parenLevel,
            bracketLevel: bracketLevel + 1,
            braceLevel: braceLevel,
          );
          b.write(nestedColorizer.colorize(content));
        }
      }
    }

    b.write(_wrap(node.close, ansi: _bracketColors[bracketLevel % _bracketColors.length], debugTag: levelTag));
    return b.toString();
  }

  String _colorizeBracketGroup(BracketGroupNode node) {
    final b = StringBuffer();
    String levelTag;
    String ansi;

    // Determine the tag and color based on bracket type and level
    if (node.open == '[') {
      levelTag = 'bracket_$bracketLevel';
      ansi = _bracketColors[bracketLevel % _bracketColors.length];
    } else if (node.open == '(') {
      levelTag = 'paren_$parenLevel';
      ansi = _parenColor;
    } else if (node.open == '{') {
      levelTag = 'brace_$braceLevel';
      ansi = _paramEqualsColor;
    } else {
      levelTag = 'bracket_$bracketLevel';
      ansi = _bracketColors[bracketLevel % _bracketColors.length];
    }

    b.write(_wrap(node.open, ansi: ansi, debugTag: levelTag));

    // Process the content
    if (node.content.isNotEmpty) {
      for (int i = 0; i < node.content.length; i++) {
        final content = node.content[i];

        if (content is LiteralNode) {
          final text = content.text;

          // Check if this is a simple value or contains structure
          if (_containsStructure(text)) {
            // Re-parse and colorize with nested levels
            final parser = LogParser(text);
            final ast = parser.parse();

            final nestedColorizer = LogColorizer(
              debug: debug,
              parenLevel: node.open == '(' ? parenLevel + 1 : parenLevel,
              bracketLevel: node.open == '[' ? bracketLevel + 1 : bracketLevel,
              braceLevel: node.open == '{' ? braceLevel + 1 : braceLevel,
            );
            b.write(nestedColorizer.colorize(ast));
          } else {
            // Simple content - check if it contains commas for parameter lists
            if (text.contains(',') && node.open == '[') {
              _colorizeCommaSeparatedValues(b, text);
            } else {
              // For standalone brackets at top level (level 0), don't wrap content in paramVal
              if (bracketLevel == 0 && node.open == '[') {
                b.write(text); // Plain text for standalone brackets
              } else {
                // Single value for nested brackets - only wrap if we're in a parameter context
                // Check if we're inside function parameters (parenLevel > 0)
                if (parenLevel > 0) {
                  b.write(_wrap(text, ansi: _paramValueColor, debugTag: 'paramVal'));
                } else {
                  b.write(text); // Plain text for top-level brackets
                }
              }
            }
          }
        } else {
          // Not a literal, colorize with nested levels
          final nestedColorizer = LogColorizer(
            debug: debug,
            parenLevel: node.open == '(' ? parenLevel + 1 : parenLevel,
            bracketLevel: node.open == '[' ? bracketLevel + 1 : bracketLevel,
            braceLevel: node.open == '{' ? braceLevel + 1 : braceLevel,
          );
          b.write(nestedColorizer.colorize(content));
        }
      }
    }

    b.write(_wrap(node.close, ansi: ansi, debugTag: levelTag));
    return b.toString();
  }

  bool _containsStructure(String text) {
    return text.contains('[') || text.contains('(') || text.contains('{') || text.contains('=') || text.contains(':');
  }

  void _colorizeCommaSeparatedValues(StringBuffer buffer, String text) {
    final parts = text.split(',');
    for (int i = 0; i < parts.length; i++) {
      final part = parts[i];

      // Handle the part - don't create separate paramVal for leading spaces
      if (i == 0) {
        // First part - wrap as paramVal
        buffer.write(_wrap(part, ansi: _paramValueColor, debugTag: 'paramVal'));
      } else {
        // Subsequent parts - include leading space in the paramVal
        buffer.write(_wrap(part, ansi: _paramValueColor, debugTag: 'paramVal'));
      }

      if (i < parts.length - 1) {
        buffer.write(_wrap(',', ansi: _paramEqualsColor, debugTag: 'symbol'));
      }
    }
  }

  String _colorizeLiteral(LiteralNode node) {
    final text = node.text.trim();

    // Special coloring for null values
    if (text == 'null') {
      return _wrap(node.text, ansi: _nullValueColor, debugTag: 'null');
    }

    // Check if this is a standalone result statement
    if (_isStandaloneResult(node.text)) {
      return _colorizeStandaloneResult(node.text);
    }

    // Only wrap with paramVal if we're inside a function parameter context
    // For top-level content (bracketLevel == 0, parenLevel == 0, braceLevel == 0),
    // don't wrap with paramVal unless it's clearly a parameter value
    if (bracketLevel == 0 && parenLevel == 0 && braceLevel == 0) {
      // Top-level content should not be wrapped with paramVal
      return node.text;
    }

    // For bracket content at level 0, don't wrap with paramVal
    if (bracketLevel == 0 && node.text.trim().isNotEmpty) {
      return node.text;
    }

    // Check if this looks like a parameter value (not whitespace or symbols)
    if (text.isNotEmpty && !_isSymbolOrWhitespace(text)) {
      // Only wrap if we're actually inside function parameters (parenLevel > 0)
      if (parenLevel > 0) {
        return _wrap(node.text, ansi: _paramValueColor, debugTag: 'paramVal');
      }
    }

    return node.text;
  }

  bool _isStandaloneResult(String text) {
    final trimmed = text.trim();
    final lowerTrimmed = trimmed.toLowerCase();
    return (lowerTrimmed.startsWith('result:') || lowerTrimmed.startsWith('results:')) && trimmed.contains(':');
  }

  String _colorizeStandaloneResult(String text) {
    final colonIndex = text.indexOf(':');
    if (colonIndex == -1) return text;

    final key = text.substring(0, colonIndex);
    final colon = ':';
    final value = text.substring(colonIndex + 1);

    final buffer = StringBuffer();
    buffer.write(_wrap(key, ansi: _paramKeyColor, debugTag: 'resultKey'));
    buffer.write(_wrap(colon, ansi: _paramEqualsColor, debugTag: 'symbol'));
    buffer.write(_wrap(value, ansi: _resultValueColor, debugTag: 'resultVal'));

    return buffer.toString();
  }

  bool _isSymbolOrWhitespace(String text) {
    final trimmed = text.trim();
    return trimmed.isEmpty ||
        trimmed == ',' ||
        trimmed == ':' ||
        trimmed == '=' ||
        trimmed == '->' ||
        trimmed == '(' ||
        trimmed == ')' ||
        trimmed == '[' ||
        trimmed == ']' ||
        trimmed == '{' ||
        trimmed == '}';
  }

  static String colorizeRecord(LogRecord record, {bool debug = false}) {
    final time = record.time.toIso8601String().substring(11, 19);
    final level = record.level.name;
    final loggerName = record.loggerName;
    final message = colorString(record.message, debug: debug);

    final levelColor = levelColors[record.level] ?? _resetColor;

    final sb = StringBuffer()
      ..write(time)
      ..write(" ")
      ..write(levelColor)
      ..write(level)
      ..write(_resetColor)
      ..write(" [")
      ..write(loggerName)
      ..write("] ")
      ..write(message);

    if (record.error != null) {
      sb
        ..write("\n")
        ..write(levelColor)
        ..write(record.error)
        ..write(_resetColor);
    }

    if (record.stackTrace != null) {
      sb
        ..write("\n")
        ..write(record.stackTrace);
    }

    return sb.toString();
  }

  static String colorString(String input, {bool debug = false}) {
    // Parse the input
    final parser = LogParser(input);
    final ast = parser.parse();

    // Colorize the AST
    String result = LogColorizer(debug: debug).colorize(ast);

    return result;
  }
}
