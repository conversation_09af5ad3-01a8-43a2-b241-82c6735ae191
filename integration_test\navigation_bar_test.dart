import 'dart:io' as io;

import 'package:flutter/material.dart' hide NavigationBar;
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // Test files with explicit timestamps for consistent golden files
  final testFiles = [
    TestFile(
      name: 'documents',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
      children: [
        TestFile(
          name: 'file1.txt',
          modifyTime: DateTime(2024, 1, 1, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'downloads',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
      children: [
        TestFile(
          name: 'file2.txt',
          modifyTime: DateTime(2024, 1, 2, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'readme.txt',
      modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
    ),
  ];

  group('Navigation Bar Input Mode Tests', () {
    testWidgets('F1 key should open navigation bar input mode', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_f1_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Open the navigation bar using F1 key
      await tester.sendKeyEvent(LogicalKeyboardKey.f1);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after pressing F1');

      // Verify that a text field is present
      final textFieldFinder = find.byType(TextField);
      expect(textFieldFinder, findsOneWidget, reason: 'Should find a text field when navigation bar is open');

      // Verify that we can type in the text field
      await tester.enterText(textFieldFinder, 'test');
      await tester.pumpAndSettle();

      final textField = tester.widget<TextField>(textFieldFinder);
      expect(textField.controller?.text, contains('test'), reason: 'Text field should contain typed text');
    });

    testWidgets('Navigation bar can be opened and closed', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_open_close_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Verify navigation bar is initially closed
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse, reason: 'Navigation bar should initially be closed');

      // Open using F1 key
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue,
          reason: 'Navigation bar should be open after F1');

      // Close using Escape key
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse, reason: 'Navigation bar should be closed after Escape');
    });

    testWidgets('Clicking outside should close navigation bar input mode', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_outside_click_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // First open the navigation bar
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after opening');

      // Close it by clicking outside - click somewhere that will take focus
      await tester.tapAt(const Offset(400, 300));
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse,
          reason: 'Navigation bar should not be in input mode after clicking outside');
    });

    testWidgets('Commands should not react when navigation bar is focused', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_command_isolation_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // First, select a file using arrow keys to ensure we have a focused file
      await testState.sendKeyAndWait(LogicalKeyboardKey.arrowDown);

      // Verify the exact focused index - should be 1 (first file after parent directory)
      expect(testState.directoryViewStore(Side.left).focusedRowIndex, equals(1),
          reason: 'Should focus on first file (index 1) after arrow down');

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Try F2 (rename) - it should not work when navigation bar is focused
      await tester.sendKeyEvent(LogicalKeyboardKey.f2);
      await tester.pumpAndSettle();

      // Verify that rename mode was not triggered (navigation bar should still be in input mode)
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue,
          reason: 'Navigation bar should remain in input mode - F2 should not work');

      // Also verify that the file is not in rename mode
      expect(testState.directoryViewStore(Side.left).renameStore.isRenaming, isFalse,
          reason: 'File should not be in rename mode when navigation bar is focused');
    });

    testWidgets('After closing nav bar, file list has focus and pressing any key works', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_focus_return_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // First, select a file using arrow keys to ensure we have a focused file
      await testState.sendKeyAndWait(LogicalKeyboardKey.arrowDown);
      expect(testState.directoryViewStore(Side.left).focusedRowIndex, equals(1),
          reason: 'Should focus on first file (index 1) after arrow down');

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Close it with Escape key
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse,
          reason: 'Navigation bar should not be in input mode after pressing Escape');

      // Test that file list navigation works again - should move to next file (index 2)
      await testState.sendKeyAndWait(LogicalKeyboardKey.arrowDown);
      expect(testState.directoryViewStore(Side.left).focusedRowIndex, equals(2),
          reason: 'File list should respond to arrow keys after navigation bar is closed, moving to index 2');
    });
  });

  group('Navigation Bar Breadcrumb Tests', () {
    testWidgets('Breadcrumb widgets are rendered', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_breadcrumb_test',
        leftFiles: [
          TestFile(
            name: 'parent',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
            children: [
              TestFile(
                name: 'file.txt',
                modifyTime: DateTime(2024, 1, 1, 11, 0, 0),
              ),
            ],
          ),
        ],
        rightFiles: testFiles,
      );

      // Verify we're in the base directory
      final currentPath = testState.directoryViewStore(Side.left).dir.absolutePath;
      expect(currentPath.endsWith('left'), isTrue, reason: 'Should be in left directory');

      // Find breadcrumb widgets - this verifies that breadcrumbs are rendered
      final breadcrumbFinder = find.byType(RichText);
      expect(breadcrumbFinder, findsWidgets, reason: 'Should find breadcrumb widgets');

      // Verify that the navigation bar contains breadcrumb content
      // This is a basic test that the breadcrumb system is working
      final navigationBarFinder = find.byKey(Key('navigation-bar-empty-space-left'));
      expect(navigationBarFinder, findsOneWidget, reason: 'Should find navigation bar');
    });
  });

  group('Navigation Bar Autocomplete Tests', () {
    testWidgets('Empty prefix shows relative paths with current dir prefix', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_empty_prefix_test',
        leftFiles: [
          TestFile(
            name: 'documents',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
          TestFile(
            name: 'downloads',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
          ),
          TestFile(
            name: 'desktop',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path that the left pane is showing
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Clear the text field to simulate empty prefix
      final textFieldFinder = find.byType(TextField);
      await tester.enterText(textFieldFinder, '');
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact autocomplete suggestions with no highlighting for empty prefix
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion('$currentDir${io.Platform.pathSeparator}desktop', []),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}documents', []),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}downloads', []),
      ];
      expect(actualSuggestions, equals(expectedSuggestions),
          reason: 'Must have exactly these 3 suggestions with no highlighting for empty prefix');
    });

    testWidgets('Partial prefix shows matching directories with highlighting', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_partial_prefix_test',
        leftFiles: [
          TestFile(
            name: 'documents',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
          TestFile(
            name: 'downloads',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
          ),
          TestFile(
            name: 'desktop',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path that the left pane is showing
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Type 'd' to trigger autocomplete suggestions for directories starting with 'd'
      final textFieldFinder = find.byType(TextField);
      await tester.enterText(textFieldFinder, 'd');
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact autocomplete suggestions with 'd' highlighted
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion('$currentDir${io.Platform.pathSeparator}desktop', ['d']),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}documents', ['d']),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}downloads', ['d']),
      ];
      expect(actualSuggestions, equals(expectedSuggestions), reason: 'Must have exactly these 3 suggestions with "d" highlighted');
    });

    testWidgets('Autocomplete suggestions appear when typing', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_suggestions_test',
        leftFiles: [
          TestFile(
            name: 'documents',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path that the left pane is showing
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Type 'doc' to trigger autocomplete suggestions
      final textFieldFinder = find.byType(TextField);
      await tester.enterText(textFieldFinder, 'doc');
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact autocomplete suggestion with 'doc' highlighted
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion('$currentDir${io.Platform.pathSeparator}documents', ['doc']),
      ];
      expect(actualSuggestions, equals(expectedSuggestions), reason: 'Must have exactly 1 suggestion with "doc" highlighted');
    });

    testWidgets('Enter key closes navigation bar and attempts navigation', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_enter_test',
        leftFiles: [
          TestFile(
            name: 'documents',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Type a path to navigate to
      final textFieldFinder = find.byType(TextField);
      final currentPath = testState.directoryViewStore(Side.left).dir.absolutePath;
      final documentsPath = '$currentPath${RawPath.pathSeparator}documents';
      await tester.enterText(textFieldFinder, documentsPath);
      await tester.pumpAndSettle();

      // Press Enter to navigate
      await tester.sendKeyEvent(LogicalKeyboardKey.enter);
      await tester.pumpAndSettle();

      // Verify that navigation bar is closed
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse, reason: 'Navigation bar should be closed after Enter');

      // Verify that navigation was attempted (the path should have changed)
      final newPath = testState.directoryViewStore(Side.left).dir.absolutePath;
      expect(newPath.endsWith('documents'), isTrue, reason: 'Should have navigated to documents directory');
    });

    testWidgets('Empty directory offers current dir as single suggestion', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_empty_dir_test',
        leftFiles: [
          // No directories, only files
          TestFile(
            name: 'readme.txt',
            isDirectory: false,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
          TestFile(
            name: 'config.json',
            isDirectory: false,
            modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path that the left pane is showing
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Clear the text field to trigger autocomplete for empty prefix
      final textFieldFinder = find.byType(TextField);
      await tester.enterText(textFieldFinder, '');
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact suggestion - current directory with no highlighting for empty prefix
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion(currentDir, []),
      ];
      expect(actualSuggestions, equals(expectedSuggestions),
          reason: 'Must have exactly current directory as single suggestion with no highlighting');
    });

    testWidgets('Highlighted text matches search query', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_highlighting_test',
        leftFiles: [
          TestFile(
            name: 'documents',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
          TestFile(
            name: 'downloads',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
          ),
          TestFile(
            name: 'desktop',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path that the left pane is showing
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Type 'do' to trigger autocomplete suggestions for directories starting with 'do'
      final textFieldFinder = find.byType(TextField);
      await tester.enterText(textFieldFinder, 'do');
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact autocomplete suggestions with 'do' highlighted
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion('$currentDir${io.Platform.pathSeparator}documents', ['do']),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}downloads', ['do']),
      ];
      expect(actualSuggestions, equals(expectedSuggestions), reason: 'Must have exactly these 2 suggestions with "do" highlighted');
    });

    testWidgets('Exact prefix match highlighting', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_exact_match_test',
        leftFiles: [
          TestFile(
            name: 'test',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
          TestFile(
            name: 'testing',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
          ),
          TestFile(
            name: 'tests',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path that the left pane is showing
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Type 'test' to trigger autocomplete suggestions
      final textFieldFinder = find.byType(TextField);
      await tester.enterText(textFieldFinder, 'test');
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact autocomplete suggestions with 'test' highlighted
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion('$currentDir${io.Platform.pathSeparator}test', ['test']),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}testing', ['test']),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}tests', ['test']),
      ];
      expect(actualSuggestions, equals(expectedSuggestions), reason: 'Must have exactly these 3 suggestions with "test" highlighted');
    });

    testWidgets('Empty prefix autocomplete behavior', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_empty_prefix_behavior_test',
        leftFiles: [
          TestFile(
            name: 'documents',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
          TestFile(
            name: 'downloads',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Clear the text field to simulate empty prefix
      final textFieldFinder = find.byType(TextField);
      await tester.enterText(textFieldFinder, '');
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact autocomplete suggestions for empty prefix
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion('$currentDir${io.Platform.pathSeparator}documents', []),
        TestSuggestion('$currentDir${io.Platform.pathSeparator}downloads', []),
      ];
      expect(actualSuggestions, equals(expectedSuggestions),
          reason: 'Must have exactly these 2 suggestions with no highlighting for empty prefix');
    });

    testWidgets('Non-empty prefix autocomplete behavior', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_nonempty_prefix_behavior_test',
        leftFiles: [
          TestFile(
            name: 'documents',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
          ),
          TestFile(
            name: 'downloads',
            isDirectory: true,
            modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
          ),
        ],
        rightFiles: testFiles,
      );

      // Get the current directory path
      final currentDir = testState.directoryViewStore(Side.left).dir.absolutePath;

      // Open the navigation bar input mode
      await openNavigationBar(tester, side: Side.left);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Type a partial path prefix
      final textFieldFinder = find.byType(TextField);
      final partialPrefix = '$currentDir${RawPath.pathSeparator}doc';
      await tester.enterText(textFieldFinder, partialPrefix);
      await tester.pumpAndSettle();

      // Wait for suggestions to appear
      await tester.pump(const Duration(milliseconds: 500));

      // Verify exact autocomplete suggestions for partial prefix
      final actualSuggestions = getTestSuggestions(tester);
      final expectedSuggestions = [
        TestSuggestion('$currentDir${io.Platform.pathSeparator}doc${io.Platform.pathSeparator}documents', ['doc']),
      ];
      expect(actualSuggestions, equals(expectedSuggestions), reason: 'Must have exactly this 1 suggestion with "doc" highlighted');
    });
  });
}

/// Helper function to open navigation bar input mode using F1 key
Future<void> openNavigationBar(WidgetTester tester, {Side side = Side.left}) async {
  // Use F1 key to open navigation bar as it's more reliable than clicking
  await tester.sendKeyEvent(LogicalKeyboardKey.f1);
  await tester.pumpAndSettle();
}

/// Helper function to check if a navigation bar is in input mode
bool isNavigationBarInInputMode(WidgetTester tester, {Side side = Side.left}) {
  // When in input mode, the navigation bar shows an AutoCompleteTextField instead of the empty space GestureDetector
  final navigationBarKey = Key('navigation-bar-empty-space-${side.name}');
  final gestureDetectorFinder = find.byKey(navigationBarKey);

  // If empty space is not found, we're in input mode
  return gestureDetectorFinder.evaluate().isEmpty;
}

/// Helper function to get autocomplete suggestions from the overlay
List<Widget> getAutocompleteSuggestions(WidgetTester tester) {
  // Find the overlay that contains the suggestions
  final overlayFinder = find.byType(Overlay);
  if (overlayFinder.evaluate().isEmpty) {
    return [];
  }

  // Look for InkWell widgets in the overlay which represent suggestion items
  final suggestionFinder = find.descendant(
    of: overlayFinder,
    matching: find.byType(InkWell),
  );

  return suggestionFinder.evaluate().map((e) => e.widget).toList();
}

/// Helper function to get suggestion texts from the overlay
List<String> getSuggestionTexts(WidgetTester tester) {
  final overlayFinder = find.byType(Overlay);
  if (overlayFinder.evaluate().isEmpty) {
    return [];
  }

  // Look for ListView which contains the suggestions
  final listViewFinder = find.descendant(
    of: overlayFinder,
    matching: find.byType(ListView),
  );

  if (listViewFinder.evaluate().isEmpty) {
    return [];
  }

  // Look for RichText widgets inside the ListView which contain the suggestion text
  final richTextFinder = find.descendant(
    of: listViewFinder,
    matching: find.byType(RichText),
  );

  final texts = <String>[];
  for (final element in richTextFinder.evaluate()) {
    final richText = element.widget as RichText;
    final text = _extractTextFromSpan(richText.text);
    if (text.isNotEmpty) {
      texts.add(text);
    }
  }

  return texts;
}

/// Helper function to extract text from a TextSpan
String _extractTextFromSpan(InlineSpan? span) {
  if (span is TextSpan) {
    final buffer = StringBuffer();

    // Add this span's text
    if (span.text != null) {
      buffer.write(span.text);
    }

    // Add children's text recursively
    if (span.children != null) {
      for (final child in span.children!) {
        buffer.write(_extractTextFromSpan(child));
      }
    }

    return buffer.toString();
  }
  return '';
}

/// Helper function to get highlighted suggestions (suggestions with bold text)
List<RichText> getHighlightedSuggestions(WidgetTester tester) {
  final overlayFinder = find.byType(Overlay);
  if (overlayFinder.evaluate().isEmpty) {
    return [];
  }

  // Look for RichText widgets that contain highlighted (bold) text
  final richTextFinder = find.descendant(
    of: overlayFinder,
    matching: find.byType(RichText),
  );

  final highlightedTexts = <RichText>[];
  for (final element in richTextFinder.evaluate()) {
    final richText = element.widget as RichText;
    final textSpan = richText.text;

    // Check if the text span contains bold formatting (highlighting)
    if (_containsBoldText(textSpan)) {
      highlightedTexts.add(richText);
    }
  }

  return highlightedTexts;
}

/// Helper function to check if a TextSpan contains bold text
bool _containsBoldText(InlineSpan? span) {
  if (span is TextSpan) {
    // Check if this span itself is bold
    if (span.style?.fontWeight == FontWeight.bold) {
      return true;
    }

    // Check children recursively
    if (span.children != null) {
      for (final child in span.children!) {
        if (_containsBoldText(child)) {
          return true;
        }
      }
    }
  }
  return false;
}

/// Helper function to get the currently selected suggestion
Widget? getSelectedSuggestion(WidgetTester tester) {
  final overlayFinder = find.byType(Overlay);
  if (overlayFinder.evaluate().isEmpty) {
    return null;
  }

  // Look for Container widgets with background color (selected state)
  final containerFinder = find.descendant(
    of: overlayFinder,
    matching: find.byType(Container),
  );

  for (final element in containerFinder.evaluate()) {
    final container = element.widget as Container;
    // Check if the container has a background color (indicating selection)
    if (container.color != null) {
      return container;
    }
  }

  return null;
}

/// Helper function to extract highlighted text from autocomplete suggestions
/// This looks for Text widgets with bold font weight within suggestions
List<String> getHighlightedTexts(WidgetTester tester) {
  final highlightedTexts = <String>[];

  // Find ListView in the overlay (where suggestions appear)
  final overlayFinder = find.byType(Overlay);
  if (overlayFinder.evaluate().isEmpty) {
    return highlightedTexts;
  }

  // Look for ListView which contains the suggestions
  final listViewFinder = find.descendant(
    of: overlayFinder,
    matching: find.byType(ListView),
  );

  if (listViewFinder.evaluate().isEmpty) {
    return highlightedTexts;
  }

  // Look for Text widgets with bold font weight inside ListView
  final textWidgets = find.descendant(
    of: listViewFinder,
    matching: find.byType(Text),
  );

  for (final textFinder in textWidgets.evaluate()) {
    final textWidget = textFinder.widget as Text;

    // Check if this text has bold font weight (indicating highlighting)
    if (textWidget.style?.fontWeight == FontWeight.bold) {
      final textData = textWidget.data;
      if (textData != null && textData.isNotEmpty) {
        highlightedTexts.add(textData);
      }
    }

    // Also check TextSpan for RichText widgets
    if (textWidget.textSpan != null) {
      _extractHighlightedFromInlineSpan(textWidget.textSpan!, highlightedTexts);
    }
  }

  // Also look for RichText widgets directly inside ListView
  final richTextWidgets = find.descendant(
    of: listViewFinder,
    matching: find.byType(RichText),
  );

  for (final richTextFinder in richTextWidgets.evaluate()) {
    final richTextWidget = richTextFinder.widget as RichText;
    _extractHighlightedFromInlineSpan(richTextWidget.text, highlightedTexts);
  }

  return highlightedTexts;
}

/// Helper function to recursively extract highlighted text from InlineSpan
void _extractHighlightedFromInlineSpan(InlineSpan span, List<String> highlightedTexts) {
  if (span is TextSpan) {
    // Check if this span has bold font weight
    if (span.style?.fontWeight == FontWeight.bold) {
      final text = span.text;
      if (text != null && text.isNotEmpty) {
        highlightedTexts.add(text);
      }
    }

    // Recursively check children
    if (span.children != null) {
      for (final child in span.children!) {
        _extractHighlightedFromInlineSpan(child, highlightedTexts);
      }
    }
  }
}


/// Data class representing an autocomplete suggestion with its text and highlighted parts
class TestSuggestion {
  final String fullText;
  final List<String> boldParts;

  const TestSuggestion(this.fullText, this.boldParts);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! TestSuggestion) return false;
    return fullText == other.fullText && boldParts.length == other.boldParts.length && boldParts.every((part) => other.boldParts.contains(part));
  }

  @override
  int get hashCode => Object.hash(fullText, Object.hashAll(boldParts));

  @override
  String toString() => 'TestSuggestion("$fullText", bold: $boldParts)';
}

/// Helper function to extract TestSuggestion objects from the UI
List<TestSuggestion> getTestSuggestions(WidgetTester tester) {
  final overlayFinder = find.byType(Overlay);
  if (overlayFinder.evaluate().isEmpty) {
    return [];
  }

  final listViewFinder = find.descendant(
    of: overlayFinder,
    matching: find.byType(ListView),
  );

  if (listViewFinder.evaluate().isEmpty) {
    return [];
  }

  final richTextFinder = find.descendant(
    of: listViewFinder,
    matching: find.byType(RichText),
  );

  final suggestions = <TestSuggestion>[];
  for (final element in richTextFinder.evaluate()) {
    final richText = element.widget as RichText;
    final fullText = _extractTextFromSpan(richText.text);
    final boldParts = _extractBoldTextFromSpan(richText.text);
    if (fullText.isNotEmpty) {
      suggestions.add(TestSuggestion(fullText, boldParts));
    }
  }

  return suggestions;
}

/// Helper function to extract bold text parts from a TextSpan
List<String> _extractBoldTextFromSpan(InlineSpan? span) {
  final boldParts = <String>[];
  if (span is TextSpan) {
    // Check if this span itself is bold
    if (span.style?.fontWeight == FontWeight.bold && span.text != null && span.text!.isNotEmpty) {
      boldParts.add(span.text!);
    }

    // Check children recursively
    if (span.children != null) {
      for (final child in span.children!) {
        boldParts.addAll(_extractBoldTextFromSpan(child));
      }
    }
  }
  return boldParts;
}
