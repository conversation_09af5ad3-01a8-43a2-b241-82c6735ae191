# File System Abstraction Product Requirements Document (PRD)

## 1. Overview

The File System Abstraction is a foundational component of QFiler, designed to provide a clean, consistent, and high-performance interface for all file and directory manipulations. Its primary purpose is to decouple the application's business logic from the underlying physical file system. This separation is critical for achieving testability, platform consistency, and performance through specialized backends.

## 2. Core Architectural Principles

The architecture is built on three key principles:

1.  **Interface-Driven Design**: All file system interactions are defined by a single, abstract Dart interface: `FileSystem`. This contract is the single source of truth for what file operations are supported, ensuring that any component interacting with the file system does so in a predictable and standardized way.

2.  **Pluggable Implementations**: The abstraction supports multiple, interchangeable backends that fulfill the `FileSystem` contract.
    -   **`RustFileSystemAdapter` (Production)**: This is the default implementation for the live application. It acts as a bridge to a highly optimized Rust core, which performs the actual file system operations. This approach leverages Rust's performance and safety for demanding tasks.
    -   **`InMemoryFileSystem` (Testing)**: A mock implementation that simulates a file system entirely in memory. It allows for fast, reliable, and isolated unit and integration tests for any component that depends on the file system, without touching the actual disk.

3.  **Performance through Specialization**: By delegating to a Rust core, the application can handle intensive I/O operations asynchronously and efficiently, with platform-specific optimizations (e.g., for Windows directory listing) handled at a lower level, transparent to the Dart application logic.

## 3. Key Capabilities

The `FileSystem` interface provides a comprehensive set of capabilities for file management:

- **Metadata and Inspection**:
  - **`stat`**: Retrieve detailed metadata about a file or directory, such as its size, type (file, directory, link), and timestamps (creation, modification, access).
  - **`list`**: Enumerate the contents of a directory, returning a stream of entries with their corresponding metadata.

- **File Content Manipulation**:
  - **`readFile` / `writeFile`**: Read the entire contents of a text file into a string or write a string to a file, overwriting it if it exists.

- **File and Directory Lifecycle**:
  - **`mkdir` / `rmdir`**: Create or remove directories, with support for recursive operations.
  - **`deleteFile`**: Permanently delete a single file.
  - **`rename`**: Move or rename a file or directory. The system intelligently handles moves within the same location (rename) and across different locations.

- **Advanced Copy Operations**:
  - **`copyFile`**: Perform a basic file copy, with options to control overwriting and preserve timestamps.
  - **`copyFileWithProgress`**: A specialized operation for large files that provides real-time progress updates. This operation is designed to be interactive, allowing the user to **pause**, **resume**, or **cancel** the transfer at any time.

## 4. Error Handling Philosophy

The file system abstraction provides a predictable and robust error handling model. Low-level errors originating from the operating system or the Rust backend are caught and translated into a well-defined set of `FileSystemError` exceptions in Dart. 

This ensures that the application logic can anticipate and gracefully handle specific, common issues such as:
- `FileSystemError_NotFound`: The requested file or directory does not exist.
- `FileSystemError_AlreadyExists`: An attempt was made to create a file or directory that already exists.
- `FileSystemError_NotADirectory` / `FileSystemError_IsADirectory`: An operation was attempted on the wrong type of path (e.g., listing a file).
- `FileSystemError_PermissionDenied`: The application lacks the necessary permissions.
- `FileSystemError_Cancelled`: An operation was successfully cancelled by the user.

This approach prevents unexpected crashes and allows the UI to present clear, actionable feedback to the user.

## 5. Core Data Models

- **`Path` / `RawPath`**: Represents a location in the file system, abstracting away platform-specific path formats.
- **`PathStats`**: A container for all metadata associated with a `Path`, such as size, type, and timestamps.
- **`CopyOptions`**: A configuration object used to control the behavior of copy operations (e.g., `overwriteIfExists`, `preserveTimestamps`).
