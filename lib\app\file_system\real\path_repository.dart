import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';
import 'package:queue/queue.dart';

import '../../domain/config.dart';
import '../../domain/errors.dart';
import '../../domain/path.dart';
import '../../util/duration_utils.dart';
import '../../util/log_utils.dart';
import '../file_system.dart';

part '.gen/path_repository.g.dart';

class PathRepository = PathRepositoryBase with _$PathRepository;

abstract class PathRepositoryBase with Store {
  final FileSystem _fileSystem;

  late final emptyPaths = _createPaths(RawPath.Null, watch: false, listDir: (_) => Future.value(Stream.empty()));

  PathRepositoryBase(this._fileSystem);

  // Fetches a directory from the file system.
  // The directory is not watched, so it will not be synced with file system updates.
  Paths fetchDir(RawPath dir) => _createPaths(dir, watch: false);

  // Fetches & watches a directory from the file system.
  // The directory will be synced with file system updates.
  Paths watchDir(RawPath dir) => _createPaths(dir, watch: true);

  Paths _createPaths(RawPath dir, {required bool watch, Future<Stream<Path>> Function(RawPath dir)? listDir}) {
    return Paths._(this, dir, listDir ?? _fileSystem.list, watch: watch)..reload();
  }

  // TODO: Give Paths a better name.

  @visibleForTesting
  void dispose() {}

  static final logger = loggerFor(PathRepository);
}

class Paths = PathsBase with _$Paths;

abstract class PathsBase with Store {
  final PathRepositoryBase _pathRepository;
  final RawPath dir;
  final bool watch;
  final Future<Stream<Path>> Function(RawPath dir) _listDir;
  final ObservableMap<String, Path> _pathsByName = ObservableMap();

  @readonly
  bool _isLoading = false;

  @readonly
  Exception? _error;

  @readonly
  Exception? _watchError;

  // Nullable because it may never be needed
  Queue? __queue;
  StreamSubscription<FileSystemEvent>? _watchSubscription;
  StreamSubscription<Path>? _fetchSubscription;

  PathsBase._(this._pathRepository, this.dir, this._listDir, {required this.watch});

  bool get isLoading => _isLoading;

  Exception? get error => _error ?? _watchError;

  Path? get(String fileName) => _pathsByName[fileName];

  Future<void> reload() async {
    if (watch) {
      await _stopWatching();
      _startWatching();
    }

    final stopwatch = kDebugMode ? (Stopwatch()..start()) : null;
    try {
      _setLoading(true);

      Stream<Path> paths = await _listDir(dir);
      if (Config.streamDirectoryFiles) {
        _fetchSubscription?.cancel();
        _fetchSubscription = paths.listen(_setPath, onError: _setError, onDone: () {
          _fetchSubscription?.cancel();
          if (kDebugMode) {
            logger.finer("$dir Fetching... ${_pathsByName.length} children [${stopwatch!.elapsed.humanReadable}]");
          }
        });
      } else {
        final allPaths = await paths.toList();
        _setAllPaths(allPaths);
        if (kDebugMode) {
          logger.finer("$dir Fetching... ${_pathsByName.length} children [${stopwatch!.elapsed.humanReadable}]");
        }
      }
      _setError(null);
    } on Exception catch (e) {
      _setError(e);
    } finally {
      _setLoading(false);
    }
  }

  @action
  void _setAllPaths(List<Path> paths) {
    for (final path in paths) {
      _pathsByName[path.name] = path;
    }
  }

  @action
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
    }
  }

  @action
  void _setError(dynamic error) {
    if (_error != error) {
      _error = error;
    }
  }

  void _startWatching() {
    if (kDebugMode) {
      logger.fine("$dir _startWatching()");
    }
    if (_watchSubscription != null) {
      assert(false, "_startWatchingDir($dir): Already watching!");
      return;
    }

    // FIXME: Move watching to the file system.
    _setWatchError(null);
    final Stream<FileSystemEvent> stream =
        File(dir.absolutePath).watch(events: FileSystemEvent.create | FileSystemEvent.delete | FileSystemEvent.move, recursive: false);

    // We use a queue to execute events in order, because their execution is async.
    _watchSubscription = stream.listen(
      (event) async {
        if (kDebugMode) {
          logger.fine("$dir Received $event");
        }
        assert(dir.isParentOf(event.path.asPath()), "$dir fileSystemEvent($event): Not watching dir of event!");
        switch (event.type) {
          case FileSystemEvent.create:
            return _queue.add(() => _onFileAdded(event.path, event.isDirectory));
          case FileSystemEvent.move:
            final moveEvent = event as FileSystemMoveEvent;
            return _queue.add(
              () => _onFileMoved(
                sourceAbsolutePath: moveEvent.path,
                destinationAbsolutePath: moveEvent.destination,
                isDirectory: event.isDirectory,
              ),
            );
          case FileSystemEvent.delete:
            return _queue.add(() async => _onFileRemoved(event.path, event.isDirectory));
        }
      },
      onError: _setWatchError,
    );
    // File("C:\\").watch(recursive: false).listen((event) {
    //   logger.fine("C:\\ $event");
    // });
    //
    // File("C:").watch(recursive: false).listen((event) {
    //   logger.fine("C: $event");
    // });
    //
    // File("C:\\adb").watch(recursive: false).listen((event) {
    //   logger.fine(event);
    // });
  }

  // TODO: Queue might not be needed at all, adding anything to it immediately processes it.
  Queue get _queue => __queue ??= Queue();

  @action
  Future<void> _onFileAdded(String absolutePath, bool isDirectory) async {
    final path = await _pathRepository._fileSystem.stat(absolutePath.asPath());
    if (kDebugMode) {
      logger.fine("$dir _onFileAdded($absolutePath, isDirectory=$isDirectory) File added to FS: $path");
    }
    _setPath(path);
  }

  @action
  Future<void> _onFileMoved({required String sourceAbsolutePath, String? destinationAbsolutePath, required bool isDirectory}) async {
    // Handle the add first. This allows the file list to re-focus on the new file if this is a rename.
    // If we remove first, the file list will receive the removed event but will not have anything to re-focus on,
    // so it will fall back to nearest match.
    if (destinationAbsolutePath != null) {
      await _onFileAdded(destinationAbsolutePath, isDirectory);
    }
    _onFileRemoved(sourceAbsolutePath, isDirectory);
  }

  @action
  void _onFileRemoved(String absolutePath, bool isDirectory) {
    final path = absolutePath.asPath();
    if (kDebugMode) {
      logger.fine("$dir _onFileRemoved($absolutePath, isDirectory=$isDirectory) File removed from FS: $path");
    }
    // TODO: There could be a stupid race condition here, where this event is called before fetching all the files finished,
    // TODO: and so we would delete a currently empty entry, that will get re-added once fetching the files finished.
    // TODO: Possible solution is to await until the async operation status changes and only then do it, but that seems like a lot of work for a rare problem.
    _removePath(path);
  }

  @action
  void _setWatchError(dynamic e) {
    if (_watchError == e) {
      return;
    }

    if (e != null) {
      logger.severe("$dir Watch error:", e);
      _watchError = AppException.from(e);
    } else {
      _watchError = null;
    }
  }

  Future<void> _stopWatching() async {
    final subscription = _watchSubscription;
    if (subscription != null) {
      if (kDebugMode) {
        logger.finer("$dir _stopWatching()");
      }
      await subscription.cancel();
    }
    _watchSubscription = null;
    // TODO: Test this for deeply nested directories that don't exist.
  }

  // This is called in the event of a pending FileOp, that would've added a file, when that FileOp
  // is finished. This is a way of optimistically updating the UI without having to wait for the
  // file system event from the watcher.
  void forceSet(Path path) {
    if (kDebugMode) {
      logger.finest("$dir forceSet($path)");
    }
    if (_setPath(path)) {
      _reloadPath(path, expectSuccess: true);
    }
  }

  // This is called in the event of a pending FileOp, that would've removed a file, when that FileOp
  // is finished. This is a way of optimistically updating the UI without having to wait for the
  // file system event from the watcher.
  void forceRemove(Path path) {
    if (kDebugMode) {
      logger.finest("$dir forceRemove($path)");
    }
    if (_removePath(path.path)) {
      _reloadPath(path, expectSuccess: false);
    }
  }

  void _reloadPath(Path path, {required bool expectSuccess}) async {
    if (kDebugMode) {
      logger.finest("$dir _reloadPath($path, expectSuccess: $expectSuccess)");
    }
    final newPath = await _pathRepository._fileSystem.stat(path.path);
    if (newPath.error == null) {
      if (expectSuccess) {
        // Pass-through previous size if new size is unknown for any reason.
        final updatedPath = (newPath.size ?? 0) == 0 && path.size != null ? newPath.withSize(path.size!) : newPath;
        if (kDebugMode) {
          logger.finest("$dir _reloadPath($path, expectSuccess: $expectSuccess): Path reloaded: $updatedPath");
        }
        _setPath(updatedPath);
      } else {
        if (kDebugMode) {
          logger.warning("$dir _reloadPath($path): Expected path to not exist, but it did!");
        }
      }
    } else {
      if (expectSuccess) {
        if (kDebugMode) {
          logger.warning("$dir _reloadPath($path): Expected path to be added, but it did not exist!");
        }
      } else {
        // Trying to remove again will fail with an assert, because we probably already removed this.
        // _removePath(path.path);
        if (kDebugMode) {
          logger.finest("$dir _reloadPath($path): Path removed");
        }
      }
    }
  }

  @action
  bool _setPath(Path path) {
    if (kDebugMode) {
      logger.finest("$dir _setPath($path)");
    }
    assert(dir.isParentOf(path.path), "$dir _setPath($path): Path is not a child of this dir!");
    if (!dir.isParentOf(path.path)) {
      return false;
    }

    _pathsByName[path.name] = path;
    return true;
  }

  @action
  bool _removePath(RawPath path) {
    if (kDebugMode) {
      logger.finest("$dir _removePath($path)");
    }
    if (!dir.isParentOf(path)) {
      assert(false, "$dir _removePath($path): Path is not a child of this dir!");
      return false;
    }

    final prev = _pathsByName.remove(path.name);
    if (kDebugMode && prev == null) {
      logger.fine("$dir _removePath($path): Not found in this dir, probably already removed.");
    }
    return prev != null;
  }

  Dispose observe(MapChangeListener<String, Path> listener, {bool fireImmediately = true}) {
    return _pathsByName.observe(listener, fireImmediately: fireImmediately);
  }

  @action
  void dispose() {
    _stopWatching();
    _fetchSubscription?.cancel();
    __queue?.dispose();
    _pathsByName.clear();
  }

  static final logger = loggerFor(PathsBase);
}
