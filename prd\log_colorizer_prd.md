# LogColorizer Product Requirements Document (PRD)

## 1. Overview

`LogColorizer` is a utility designed to parse and apply syntax highlighting to log strings. It recognizes specific structural patterns—such as function calls, parameters, bracketed/braced groups, and keywords—to segment a string and assign distinct styles to each part. This enhances log readability by visually distinguishing different components of a log message.

## 2. Core Concepts

The colorizer operates on a set of rules to identify and classify segments of the input string. The main components it recognizes are:

- **Functions**: Text followed by an opening parenthesis `(` is treated as a function call.
- **Parameters**: The content within the parentheses of a function call is treated as parameters.
- **Parameter Keys & Values**: Parameters can be key-value pairs (e.g., `key=value`) or standalone values (e.g., `value1, value2`).
- **Brackets & Braces**: `[]` and `{}` create nested contexts. The level of nesting determines the style, allowing for up to 4 levels of distinct highlighting for each bracket type.
- **Keywords**: Special words like `null` and `result` (and its variations) are given unique styling.
- **Symbols**: Characters like `=`, `(`, `)`, `[`, `]`, `{`, `}`, `->`, `,`, and `:` are identified as symbols.
- **Plaintext**: Any text that doesn't fit the patterns above is treated as plaintext.

## 3. Detailed Behavior and Syntax Rules

### 3.1. Function Calls

- A word followed immediately by `(` is a function (e.g., `myFunc(`).
- Function calls can be nested. The parenthesis nesting level (up to 4 levels) determines the color.
- **Example**: `complexFunc(param1=[1, 2, [3, 4]], param2=test[5])`

### 3.2. Parameters

- **Key-Value Parameters**: The format `key=value` is recognized. `key` is styled as `paramKey`.
- **Value-Only Parameters**: Values can be provided without a key, separated by commas (e.g., `_calcFile(0_temp12, doesn't exist)`).
- **Parameter Values (`paramVal`)**: The value part can be a simple string, a number, a quoted string, or a complex structure containing nested brackets/braces.
- **Internal Structures**: A parameter's value can contain its own nested structures, like function calls or bracketed lists, which are parsed recursively.
  - **Example**: `plan([ID]Job(TaskType.rename))` - The parameter to `plan` is `[ID]Job(TaskType.rename)`, which contains a bracketed value and another function call.

### 3.3. Brackets `[]` and Braces `{}`

- Brackets and braces can be used for grouping, typically inside parameter values or as standalone decorators.
- Nesting is supported up to 4 levels deep for each type (`bracket_0` to `bracket_3`, `brace_0` to `brace_3`), with each level having a distinct style.
- **Standalone Example**: `[DirectoryViewStore] [D:\Temp] Fetching...`
- **Nested Example**: `nestedBrackets(data=[[1, 2], [3, [4, 5]]])`

### 3.4. Arrow Operator `->`

- The `->` operator is treated as a symbol and is often used to denote transitions or operations, such as in file renaming.
- **Example**: `RenameOperation([D:\Temp\0_temp1] -> [D:\Temp\0_temp12])`

### 3.5. Special Keywords and Values

- **`null`**: The literal word `null` is styled as a `null` value when it appears as a parameter value.
- **`result`**: The key `result` (case-insensitive) is styled as `resultKey`. Its corresponding value is styled as `resultVal`.
  - This applies to `key=value` parameters and standalone `key: value` formats.
  - **Parameter Example**: `processData(result=success)`
  - **Standalone Example**: `Result: success`
- **Result Values**: Common result keywords like `success`, `partial`, `removed` are highlighted as `resultVal`.

### 3.6. Edge Cases and Nuances

- **Filenames with Spaces/Parentheses**: The parser correctly handles filenames that contain spaces or parentheses by segmenting the value around the internal parentheses.
  - **Example**: `_calcFile(document (2023).pdf, exists)`
- **Unmatched Brackets/Parens**: Trailing unmatched closing brackets or parentheses are treated as plaintext and do not break the parsing of the valid preceding structures.
- **Colon in Parameter**: A colon `:` within a parameter is treated as a symbol. The parser distinguishes between `key=value` and `key: value`. The latter is parsed as a single `paramVal` containing a symbol.
  - `options={recursive: true}` is a valid value for a `paramKey` `options`.
  - `options: {recursive: true}` is a single `paramVal` that gets segmented internally.

### 3.7. Output Integrity

- The colorizer should not update the output text at all, besides adding colors.
- It should not change or normalize spaces, casing, or anything else.
- The only modifications allowed are adding color/style information, and debug tags when in debug mode.
