targets:
  $default:
    sources:
      include:
        - "$package$"
        - "lib/$lib$"
        - "lib/**.dart"
      exclude:
        # Exclude the Dart source files that will be handled by the 'rust_gen' target.
        - "lib/rust/**.dart"
        # Exclude generated files from being considered as sources for new generation
        - "lib/**/.gen/*.dart"
        - "integration_test/**.dart"
        - "test/**.dart"
    builders:
      source_gen:combining_builder:
        options:
          build_extensions:
            '{{dir}}/{{file}}.dart': '{{dir}}/./.gen/{{file}}.g.dart'
      freezed:
        options:
          build_extensions:
            '{{dir}}/{{file}}.dart': '{{dir}}/./.gen/{{file}}.freezed.dart'
      json_serializable:
        options:
          explicit_to_json: true
      mobx_codegen:mobx_generator:
        options:
          debouncedComputed: DebouncedComputed
      mockito|mockBuilder:
        options:
          build_extensions:
            '{{dir}}/{{file}}.dart': '{{dir}}/.gen/{{file}}.mocks.dart'

  rust_gen:
    sources:
      include:
        - "lib/rust/.gen/**.dart"
    builders:
      source_gen:combining_builder:
        options:
          build_extensions:
            '{{dir}}/{{file}}.dart': '{{dir}}/{{file}}.g.dart'
      json_serializable:
        options:
          explicit_to_json: true