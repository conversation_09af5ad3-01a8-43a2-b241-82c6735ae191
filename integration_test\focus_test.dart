import 'package:davi/davi.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/presentation/directory_view/domain/display_file.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // Test files with explicit timestamps
  final testFiles = [
    TestFile(
      name: 'file1.txt',
      modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
    ),
    TestFile(
      name: 'file2.txt',
      modifyTime: DateTime(2024, 1, 2, 11, 0, 0),
    ),
  ];

  group('Focus Tests', () {
    testWidgets('Left pane should be focused and respond to arrow keys after app loads', (tester) async {
      final test = await prepareTest(tester, name: 'left_pane_focus_test', leftFiles: testFiles, rightFiles: testFiles);

      // Verify left pane is source
      expect(test.rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

      // Verify left FileList has focus
      final primaryFocusNode = FocusManager.instance.primaryFocus;
      final leftFileListFocusNode = _getFocusNodeForSide(test, Side.left);
      expect(primaryFocusNode, equals(leftFileListFocusNode),
          reason: 'Left FileList widget should have primary focus after app load and key press');
      expect(leftFileListFocusNode.hasFocus, isTrue, reason: 'Left FileList FocusNode should report hasFocus');

      // Test that left file list handles keyboard events
      final leftPaneStore = test.rootStore.allPanesStore.left;
      expect(leftPaneStore.directoryViewStore.focusedRowIndex, equals(0));

      // Send arrow down key
      await test.sendKeyAndWait(LogicalKeyboardKey.arrowDown);

      // The index should change
      expect(leftPaneStore.directoryViewStore.focusedRowIndex, equals(1),
          reason: 'Arrow key should change focused row when left pane has focus');

      // Golden test to verify the view updates correctly
      await expectLater(find.byType(Davi<DisplayFile>).first, matchesGoldenFile('goldens/focus_test.left_pane_arrow_down.png'));
    });

    testWidgets('Tab key should switch focused pane', (WidgetTester tester) async {
      final test = await prepareTest(tester, name: 'tab_switching_test', leftFiles: testFiles, rightFiles: testFiles);

      // Should start with left pane as source
      expect(test.rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

      final leftFocusNode = _getFocusNodeForSide(test, Side.left);
      final rightFocusNode = _getFocusNodeForSide(test, Side.right);

      // Press Tab to switch to right pane
      await test.sendKeyAndWait(LogicalKeyboardKey.tab);

      expect(test.rootStore.allPanesStore.sourcePaneSide, equals(Side.right));

      // Verify focus has moved to the right pane
      expect(FocusManager.instance.primaryFocus, equals(rightFocusNode), reason: 'After Tab, right FileList widget should have primary focus');
      expect(rightFocusNode.hasFocus, isTrue, reason: 'After Tab, right FileList FocusNode should have focus');
      expect(leftFocusNode.hasFocus, isFalse, reason: 'After Tab, left FileList FocusNode should NOT have focus');

      // Golden file check for the first tab press (Right pane is source)
      await expectLater(
          find.byType(Davi<DisplayFile>).first, // Assuming the first Davi is the left pane, then right after tab
          matchesGoldenFile('goldens/focus_test.tab_to_right_pane.png'));

      // Press Tab again to switch back to left pane
      await test.sendKeyAndWait(LogicalKeyboardKey.tab);

      expect(test.rootStore.allPanesStore.sourcePaneSide, equals(Side.left));

      // Verify focus has moved back to the left pane
      expect(FocusManager.instance.primaryFocus, equals(leftFocusNode), reason: 'After 2nd Tab, left FileList widget should have primary focus');
      expect(leftFocusNode.hasFocus, isTrue, reason: 'After 2nd Tab, left FileList FocusNode should have focus');
      expect(rightFocusNode.hasFocus, isFalse, reason: 'After 2nd Tab, right FileList FocusNode should NOT have focus');

      // Golden file check for the second tab press (Left pane is source again)
      await expectLater(
          find.byType(Davi<DisplayFile>).first, // Assuming the first Davi is the left pane
          matchesGoldenFile('goldens/focus_test.tab_back_to_left_pane.png'));
    });
  });
}

// Helper to get the FocusNode for the FileList of a specific side
FocusNode _getFocusNodeForSide(TestState test, Side side) {
  final expectedDebugLabel = 'FileList_${side.name}';
  // Find all Focus widgets and check their focusNode's debugLabel
  final focusWidgets = test.tester.widgetList<Focus>(find.byType(Focus));
  for (final focusWidget in focusWidgets) {
    if (focusWidget.focusNode?.debugLabel == expectedDebugLabel) {
      return focusWidget.focusNode!;
    }
  }
  throw StateError(
      'Could not find FocusNode for side $side with label $expectedDebugLabel. Found labels: ${focusWidgets.map((f) => f.focusNode?.debugLabel).where((l) => l != null).join(', ')}');
}
