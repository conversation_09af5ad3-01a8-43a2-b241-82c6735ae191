name: qfiler
description: QFiler

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.6.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  awesome_extensions: ^2.0.23
  collection: ^1.18.0
  combine: ^0.5.7
  cupertino_icons: ^1.0.8
  dartx: ^1.2.0
  davi: ^4.0.1
  duration: ^4.0.3
  fast_immutable_collections: ^11.0.3
  file_sizes: ^1.0.6
  fixed_collections: ^0.5.0-beta
  flutter:
    sdk: flutter
  flutter_hooks: ^0.21.2
  flutter_mobx: ^2.3.0
  flutter_rust_bridge: 2.10.0
  getwidget: ^6.0.0
  intl: ^0.20.2
  json_annotation: ^4.9.0
  legalize: ^1.2.2
  logging: ^1.2.0
  meta: ^1.15.0
  mobx: ^2.5.0
  multi_split_view: ^3.5.0
  path: ^1.9.0
  provider: ^6.1.2
  queue: ^3.4.0
  quiver: ^3.2.2
  rust_lib_qfiler:
    path: rust_builder
  tabbed_view: ^1.18.0
  tsid_dart: ^0.0.6
  window_manager: ^0.5.1
  freezed_annotation: ^3.1.0
  class_to_string: ^1.0.0
  petitparser: ^6.1.0

dev_dependencies:
  analyzer: ^7.5.6
  build_runner: ^2.4.15
  flutter_launcher_icons: ^0.14.3
  mockito: ^5.4.4
  freezed: ^3.1.0

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter
  json_serializable: ^6.9.0
  mobx_codegen: ^2.7.0
  pubspec_dependency_sorter: ^1.0.5
  source_gen: ^2.0.0
  test: ^1.24.0
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/default_keybindings.json
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

scripts:
  build: flutter pub run build_runner watch --delete-conflicting-outputs
  sort_dependencies: flutter pub run pubspec_dependency_sorter
