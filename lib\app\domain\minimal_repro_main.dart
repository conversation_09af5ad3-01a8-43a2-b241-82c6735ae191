import 'dart:async'; // For Timer
import 'package:davi/davi.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:provider/provider.dart';
import 'package:multi_split_view/multi_split_view.dart'; // Added for Provider
import 'package:tabbed_view/tabbed_view.dart';

void main() {
  runApp(const MinimalReproApp());
}

// Simplified DirectoryViewStore for the repro
class ReproDirectoryViewStore extends ChangeNotifier {
  bool _isLoading = true; // Start true for initial skeleton data
  int? _focusedRowIndex;

  bool get isLoading => _isLoading;
  int? get focusedRowIndex => _focusedRowIndex;

  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void setFocusedRowIndex(int? index) {
    if (_focusedRowIndex != index) {
      _focusedRowIndex = index;
      notifyListeners();
    }
  }
}

// Simplified PaneStore for the repro
class ReproPaneStore extends ChangeNotifier {
  final String side;
  late final ReproDirectoryViewStore directoryViewStore; // Holds view-specific state
  bool _isSource = false;

  ReproPaneStore(this.side, {bool isInitiallySource = false}) {
    _isSource = isInitiallySource;
    directoryViewStore = ReproDirectoryViewStore();
  }

  bool get isSource => _isSource;

  void makeSource() {
    if (!_isSource) {
      _isSource = true;
      notifyListeners(); // Notify for isSource change
    }
  }

  void makeNotSource() {
    if (_isSource) {
      _isSource = false;
      notifyListeners(); // Notify for isSource change
    }
  }

  // Dispose the directoryViewStore when PaneStore is disposed
  @override
  void dispose() {
    directoryViewStore.dispose();
    super.dispose();
  }
}

// Manages both pane stores for the repro
class AllReproPanesStore extends ChangeNotifier {
  final ReproPaneStore left;
  final ReproPaneStore right;

  AllReproPanesStore()
      : left = ReproPaneStore('Left', isInitiallySource: true),
        right = ReproPaneStore('Right') {
    left.addListener(_onPaneChange);
    right.addListener(_onPaneChange);
  }

  ReproPaneStore get sourcePane => left.isSource ? left : right;
  ReproPaneStore get targetPane => left.isSource ? right : left;

  void _onPaneChange() {
    if (left.isSource && right.isSource) {
    }
    notifyListeners();
  }

  void switchFocusTo(String side) {
    final targetIsLeft = side == 'Left';
    if (left.isSource != targetIsLeft) {
      left.makeSource(); // This will notify
    }
    if (right.isSource == targetIsLeft) { // If right was source and left becomes source, make right not source
      right.makeNotSource(); // This will notify
    } else if (!targetIsLeft && !right.isSource) { // If right should be source and isn't
      right.makeSource();
      if (left.isSource) left.makeNotSource();
    }
    // Individual makeSource/makeNotSource calls handle notifications.
  }

  @override
  void dispose() {
    left.removeListener(_onPaneChange);
    right.removeListener(_onPaneChange);
    left.dispose();
    right.dispose();
    super.dispose();
  }
}

class MinimalReproApp extends StatefulWidget {
  const MinimalReproApp({super.key});

  @override
  State<MinimalReproApp> createState() => _MinimalReproAppState();
}

class _MinimalReproAppState extends State<MinimalReproApp> {

  late AllReproPanesStore _allPanesStore;
  Timer? _initialLoadTimer;
  final pageStorageBucket = PageStorageBucket();

  @override
  void initState() {
    super.initState();
    _allPanesStore = AllReproPanesStore();

    // Simulate initial load completion
    _initialLoadTimer = Timer(const Duration(seconds: 1), () {
      // Access stores directly since we have the instance
      if (_allPanesStore.left.directoryViewStore.isLoading) {
        _allPanesStore.left.directoryViewStore.setLoading(false);
      }
      if (_allPanesStore.right.directoryViewStore.isLoading) {
        _allPanesStore.right.directoryViewStore.setLoading(false);
      }
    });
  }

  @override
  void dispose() {
    _initialLoadTimer?.cancel();
    _allPanesStore.dispose(); // Ensure the store is disposed
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Use the _allPanesStore instance initialized in initState

    return ChangeNotifierProvider<AllReproPanesStore>(
      create: (_) => _allPanesStore,
      child: PageStorage(
        bucket: pageStorageBucket,
        child: MaterialApp(
          title: 'Minimal Repro',
          theme: ThemeData.dark(),
          home: MainScreen(), // Removed const
        ),
      ),
    );
  }
}

// Define areas at the top level, similar to production app.dart
// The builder functions will get AllReproPanesStore from context.
final List<Area> splitAreasRepro = [
  Area(
    flex: 1, min: 200,
    builder: (context, area) {
      final allPanesStore = Provider.of<AllReproPanesStore>(context, listen: false);
      return ChangeNotifierProvider.value(
        value: allPanesStore.left,
        child: TabbedDaviPanel(paneStore: allPanesStore.left),
      );
    },
  ),
  Area(
    flex: 1, min: 200,
    builder: (context, area) {
      final allPanesStore = Provider.of<AllReproPanesStore>(context, listen: false);
      return ChangeNotifierProvider.value(
        value: allPanesStore.right,
        child: TabbedDaviPanel(paneStore: allPanesStore.right),
      );
    },
  ),
];

class MainScreen extends StatelessWidget {
  MainScreen({super.key}); // Constructor for StatelessWidget

  // Initialize controller as a final field, similar to production app.dart
  final MultiSplitViewController multiSplitViewController = MultiSplitViewController(areas: splitAreasRepro);

  @override
  Widget build(BuildContext context) {
    // The initial load logic has been moved to MinimalReproApp's initState.

    return Scaffold(
      appBar: AppBar(title: const Text('Minimal Scroll Repro with MultiSplitView')),
      body: MultiSplitViewTheme(
        data: MultiSplitViewThemeData(
          dividerThickness: 3,
          dividerPainter: DividerPainters.background(
            color: Colors.grey[800]!, // Darker for dark theme
            highlightedColor: Colors.blueAccent,
            animationDuration: const Duration(milliseconds: 200),
          ),
        ),
        child: MultiSplitView(
          controller: multiSplitViewController,
          // onDividerDoubleTap: (_) => multiSplitViewController.areas = splitAreasRepro, // Optional: reset areas
        ),
      ),
    );
  }
}

class TabbedDaviPanel extends HookWidget {
  final ReproPaneStore paneStore;

  const TabbedDaviPanel({
    super.key,
    required this.paneStore,
  });

  @override
  Widget build(BuildContext context) {
    final tabController = useMemoized(() {
      final String tabId = '${paneStore.side}_Tab1'; // Unique ID for the tab's content
      
      // Create TabData similar to production DirectoryTabs._createTabData
      final tabContent = ChangeNotifierProvider<ReproDirectoryViewStore>.value(
        value: paneStore.directoryViewStore,
        // Also provide ReproPaneStore for DaviPanel to access 'isSource' and 'side'
        child: ChangeNotifierProvider<ReproPaneStore>.value(
          value: paneStore,
          child: Column(
            children: [
              // Placeholder for NavigationBar
              Container(
                height: 40,
                color: Colors.blueGrey.shade700,
                alignment: Alignment.center,
                child: Text('Placeholder Nav Bar (${paneStore.side})', style: const TextStyle(color: Colors.white)),
              ),
              Expanded(child: DaviPanel(key: ValueKey(tabId), panelId: tabId)),
            ],
          ),
        ),
      );

      return TabbedViewController([
        TabData(
          text: 'Tab 1',
          value: tabId,
          content: tabContent,
        ),
      ]);
    }, [paneStore]); // Depends on paneStore instance itself

    useEffect(() {
      return tabController.dispose;
    }, [tabController]);

    return TabbedViewTheme(
      data: TabbedViewThemeData.minimalist(),
      child: TabbedView(controller: tabController),
    );
  }
}

class DaviPanel extends HookWidget {
  final String panelId; // Unique identifier for this panel's content (was tabId)

  const DaviPanel({
    super.key, // Often ValueKey based on panelId or similar
    required this.panelId,
  });

  @override
  Widget build(BuildContext context) {
    // final focusNode = useFocusNode(); // This was defined but not used in the previous DaviPanel snippet, re-evaluating if needed
    final paneStore = context.watch<ReproPaneStore>();
    final directoryViewStore = context.watch<ReproDirectoryViewStore>();
    
    final daviKey = ValueKey('davi_${panelId}_${directoryViewStore.isLoading}_${paneStore.isSource}');

    final focusNode = useFocusNode(debugLabel: 'DaviFocus_${paneStore.side}_$panelId');
    final scrollController = useScrollController(debugLabel: 'DaviScroll_${paneStore.side}_$panelId');
    final globalKey = useMemoized(() => GlobalKey(), [panelId]); // For the outer Container

    useEffect(() {
      if (paneStore.isSource) {
        focusNode.requestFocus();
      }
      return null;
    }, [paneStore.isSource, focusNode]);

    final data = List.generate(directoryViewStore.isLoading ? 15 : 100, (index) => <String, Object>{
      'id': index,
      'name': directoryViewStore.isLoading ? 'Loading item ${index + 1}...' : 'Item ${index + 1} ($panelId)',
      'description': directoryViewStore.isLoading ? 'Skeleton content for item ${index + 1}' : 'Description for item ${index + 1} in $panelId',
      'modified': directoryViewStore.isLoading ? '---' : '2024-01-${(index % 28 + 1).toString().padLeft(2, '0')}',
    });

    final daviColumns = [
      DaviColumn(
        name: 'ID', 
        width: 60, 
        cellWidget: (WidgetBuilderParams<Map<String, Object>> params) => Text(params.data['id'].toString())
      ),
      DaviColumn(
        name: 'Name', 
        grow: 1, 
        cellWidget: (WidgetBuilderParams<Map<String, Object>> params) => Text(params.data['name'] as String? ?? '')
      ),
      DaviColumn(
        name: 'Description', 
        grow: 2, 
        cellWidget: (WidgetBuilderParams<Map<String, Object>> params) => Text(params.data['description'] as String? ?? '')
      ),
      DaviColumn(
        name: 'Modified', 
        width: 100, 
        cellWidget: (WidgetBuilderParams<Map<String, Object>> params) => Text(params.data['modified'] as String? ?? '')
      ),
    ];

    final model = DaviModel<Map<String, Object>>(rows: data, columns: daviColumns);

    return Focus(
      focusNode: focusNode, // Associate the FocusNode here
      child: Container(
        key: globalKey, 
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
        border: Border.all(color: paneStore.isSource ? Colors.blue : Colors.grey, width: paneStore.isSource ? 2 : 1),
        color: paneStore.isSource ? Colors.blue.withOpacity(0.05) : Colors.transparent,
      ),
      child: DaviTheme(
        data: DaviThemeData(
          row: RowThemeData(
            color: (rowIndex) {
              if (rowIndex == directoryViewStore.focusedRowIndex) {
                return Colors.blue.withOpacity(0.2);
              }
              return null; // Default color
            },
          ),
        ),
        child: Davi<Map<String, Object>>(
          model, 
          key: daviKey, // Use the defined daviKey
          verticalScrollController: scrollController,
          // focusNode: focusNode, // Davi does not take focusNode directly
          onRowTap: (Map<String, Object> row) { 
            final rowIndex = data.indexOf(row);
            // print('$panelId (${paneStore.side}): Tapped row $rowIndex. IsSource: ${paneStore.isSource}');

            final allPanes = Provider.of<AllReproPanesStore>(context, listen: false);
            
            directoryViewStore.setFocusedRowIndex(rowIndex);
            // paneStore.makeSource(); // Ensure this pane is marked as source upon interaction

            if (paneStore.side == 'left') {
              allPanes.switchFocusTo('right');
            } else {
              allPanes.switchFocusTo('left');
            }
            // No need to call focusNode.requestFocus() here if Davi handles it internally on tap or if setFocus above triggers it.
            // If explicit request is needed after state change, it can be added.
          },
        ),
      ),
    ), // Closes Focus widget
    );
  }
}