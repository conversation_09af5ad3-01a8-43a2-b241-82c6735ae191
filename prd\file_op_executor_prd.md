# FileOpExecutor Product Requirements Document (PRD)

## 1. Overview

`FileOpExecutor` is the core engine responsible for executing file system operations within QFiler. It takes abstract `Operation` objects (like copy, move, delete) from a task queue and translates them into actual file system calls. It is designed to be robust, handling errors gracefully, providing progress updates for long-running operations, and interacting with the user for conflict resolution.

## 2. Core Components & Dependencies

`FileOpExecutor` relies on several key abstractions and components to perform its duties:

- **`FileSystem`**: An abstraction over the native file system. This allows for platform-agnostic file operations and facilitates testing with an in-memory mock (`InMemoryFileSystem`).
- **`Operation` Subclasses**: Data objects that define the work to be done (e.g., `CopyOperation`, `MoveOperation`, `DeleteOperation`, `RenameOperation`). Each operation contains details like source, destination, and the parent `Task`.
- **`PendingOperationRepository`**: A repository that tracks all active operations. `FileOpExecutor` is responsible for removing operations from this repository once they are completed, skipped, or cancelled.
- **Dialog Interfaces**: 
  - `ShowConfirmOverwriteDialog`: A function that is called to prompt the user when a file operation would overwrite an existing file. The user's choice (`Overwrite`, `Skip`, `Cancel`) dictates the subsequent action.
  - `ShowFileOpErrorDialog`: A function called when an unexpected file system error occurs. It allows the user to decide whether to `Retry`, `Skip` the problematic file, or `Cancel` the entire task.

## 3. Operation Lifecycle and User Interaction

Each file operation managed by the executor follows a resilient, user-centric lifecycle.

- **Initiation and Status**: An operation begins when passed to the executor. Its status is immediately marked as "in-progress" and is kept updated throughout its lifecycle (`completed`, `skipped`, `cancelled`).

- **Conflict Resolution**: The system is designed to pause and ask for user input when it cannot proceed automatically.
    - **File Overwrites**: If an operation would overwrite an existing file, the user is prompted with a dialog to choose whether to **Overwrite** the file, **Skip** it, or **Cancel** the entire task. This choice can be applied to all subsequent conflicts within the same task.
    - **Execution Errors**: If any other file system error occurs, the user is prompted to **Retry** the failed operation, **Skip** the problematic file, or **Cancel** the task.

- **Resilience and Retries**: Operations are persistent. If the user chooses to 'Retry', the executor will continue attempting the operation until it succeeds or the user makes a different choice (Skip or Cancel).

- **Cleanup**: Regardless of the outcome—success, skip, or cancellation—the executor ensures that the operation is cleanly removed from the active task queue upon completion.

## 4. Operation-Specific Goals

### 4.1. Copy (`CopyOperation`)

- **Goal**: To create an exact duplicate of a source file or directory structure at a new destination, preserving file timestamps.
- **Behavior**: The system optimizes for both speed and user feedback. Small file copies are executed rapidly, while larger transfers display detailed, pausable progress. A directory copy is an all-or-nothing transaction; it is only considered successful if the directory and all of its contents are copied completely.

### 4.2. Move (`MoveOperation`)

- **Goal**: To relocate a file or directory to a new destination.
- **Behavior**: Move operations are optimized for speed by attempting a direct rename where possible. The system automatically handles complex cases, such as moving files across different drives, by transparently falling back to a copy-then-delete process. The user experience remains seamless.

### 4.3. Delete (`DeleteOperation`)

- **Goal**: To permanently remove a file or directory.
- **Behavior**: A directory is only deleted if all of its contents can be successfully removed first. This prevents orphaned files and partially-deleted directories.

### 4.4. Rename (`RenameOperation`)

- **Goal**: To change the name of a file or directory while keeping it in the same location.

### 4.5. Create (`CreateFileOperation`)

- **Goal**: To create a new, empty file or directory.
- **Status**: This feature is planned but not yet implemented.

## 5. System Guarantees

- **No Data Loss on Overwrite**: The system will never overwrite a file without explicit user permission.
- **Graceful Error Handling**: Unexpected errors will not crash the application; instead, the user will be prompted to make a decision on how to proceed.
- **Transactional Integrity for Directories**: Operations on directories (copy, delete) aim for transactional integrity. They are only marked as successful if the entire directory structure is processed as intended.

## 6. Known Issues / Areas for Improvement

- **`OpContext`**: The `OpContext` class, intended to hold state for a single operation, is currently shared across all operations within a single `execute` call. A FIXME note in the code indicates this does not work as expected and may need to be refactored to correctly manage per-operation state.
