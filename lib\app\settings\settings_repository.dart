import 'package:meta/meta.dart';
import 'package:mobx/mobx.dart';

import '../domain/settings.dart';
import '../persistence/data_repository.dart';

part '.gen/settings_repository.g.dart';

class SettingsRepository extends SettingsRepositoryBase with _$SettingsRepository {
  SettingsRepository._();

  static Future<SettingsRepository> create(DataRepository dataRepository) async {
    final repository = SettingsRepository._();
    await dataRepository.register<Settings>(
      name: "settings",
      createDefault: repository._createDefault,
      snapshot: repository._snapshot,
      restore: repository._restore,
      serializer: Settings.serializer,
    );
    return repository;
  }

  Settings _createDefault() => const Settings();

  Settings _snapshot() => settings;

  void _restore(Settings snapshot) => settings = snapshot;

  @visibleForTesting
  void dispose() {}
}

abstract class SettingsRepositoryBase with Store {
  // TODO: When this starts growing large, can split this into granular settings, each in its own file.
  @observable
  Settings settings = const Settings();

  @action
  void updateSettings(Settings Function(Settings settings) updater) => settings = updater(settings);

  @action
  void updatePathTextfieldSettings(PathTextfieldSettings Function(PathTextfieldSettings settings) updater) =>
      settings = settings.copyWith(pathTextfieldSettings: updater(settings.pathTextfieldSettings));

  @action
  void updateWindowSettings(WindowSettings Function(WindowSettings settings) updater) =>
      settings = settings.copyWith(windowSettings: updater(settings.windowSettings));
}
