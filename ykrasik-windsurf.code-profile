{"name": "ykrasik-windsurf", "settings": "{\"settings\":\"{\\r\\n  \\\"windsurf.autocompleteSpeed\\\": \\\"default\\\",\\r\\n  \\\"database-client.autoSync\\\": true,\\r\\n  \\\"workbench.iconTheme\\\": \\\"vscode-icons\\\",\\r\\n  \\\"workbench.auxiliaryActivityBar.location\\\": \\\"default\\\",\\r\\n  \\\"indentRainbow.indicatorStyle\\\": \\\"light\\\",\\r\\n  \\\"indentRainbow.lightIndicatorStyleLineWidth\\\": 5,\\r\\n  \\\"workbench.startupEditor\\\": \\\"newUntitledFile\\\",\\r\\n  \\\"python.formatting.provider\\\": \\\"none\\\",\\r\\n  \\\"[python]\\\": {\\r\\n    \\\"editor.semanticHighlighting.enabled\\\": true,\\r\\n    \\\"editor.codeActionsOnSave\\\": {\\r\\n      \\\"source.fixAll\\\": \\\"explicit\\\",\\r\\n      \\\"source.organizeImports\\\": \\\"explicit\\\"\\r\\n    },\\r\\n    \\\"editor.formatOnSave\\\": true,\\r\\n    \\\"editor.defaultFormatter\\\": \\\"charliermarsh.ruff\\\"\\r\\n  },\\r\\n  \\\"[javascript]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"vscode.typescript-language-features\\\",\\r\\n    \\\"editor.formatOnSave\\\": true\\r\\n  },\\r\\n  \\\"[typescript]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"dbaeumer.vscode-eslint\\\",\\r\\n    \\\"editor.formatOnSave\\\": true\\r\\n  },\\r\\n  \\\"[json]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"dbaeumer.vscode-eslint\\\",\\r\\n    \\\"editor.formatOnSave\\\": true\\r\\n  },\\r\\n  \\\"[sql]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"cweijan.vscode-database-client2\\\",\\r\\n    \\\"editor.formatOnSave\\\": true\\r\\n  },\\r\\n  \\\"editor.minimap.maxColumn\\\": 160,\\r\\n  \\\"editor.tabSize\\\": 2,\\r\\n  \\\"remote.SSH.remotePlatform\\\": {\\r\\n    \\\"deimos\\\": \\\"linux\\\",\\r\\n    \\\"ykrasik.asuscomm.com\\\": \\\"linux\\\"\\r\\n  },\\r\\n  \\\"workbench.iconTheme\\\": \\\"vscode-icons\\\",\\r\\n  \\\"vscode-home-assistant.hostUrl\\\": \\\"https://homeassistant.ykrasik.com\\\",\\r\\n  \\\"vscode-home-assistant.longLivedAccessToken\\\": \\\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJhZGY1ZjY4MmZlYmI0NDBkODA1NTBkMThjMGU0YzkwZSIsImlhdCI6MTcwNzQzNTQ1NiwiZXhwIjoyMDIyNzk1NDU2fQ.m_K8isbWAk4caczg4PHd-aRAuHfY6fnWCJe-fJVEv8U\\\",\\r\\n  \\\"redhat.telemetry.enabled\\\": false,\\r\\n  \\\"explorer.confirmDragAndDrop\\\": false,\\r\\n  \\\"sqltools.useNodeRuntime\\\": false,\\r\\n  \\\"sqltools.connections\\\": [],\\r\\n  \\\"sqltools.dependencyManager\\\": {\\r\\n    \\\"packageManager\\\": \\\"npm\\\",\\r\\n    \\\"installArgs\\\": [\\r\\n      \\\"install\\\"\\r\\n    ],\\r\\n    \\\"runScriptArgs\\\": [\\r\\n      \\\"run\\\"\\r\\n    ],\\r\\n    \\\"autoAccept\\\": false\\r\\n  },\\r\\n  \\\"sqltools.results.limit\\\": 500,\\r\\n  \\\"sqltools.results.location\\\": \\\"current\\\",\\r\\n  \\\"sqltools.sessionFilesFolder\\\": \\\"sql\\\",\\r\\n  \\\"files.trimTrailingWhitespace\\\": true,\\r\\n  \\\"github.copilot.enable\\\": {\\r\\n    \\\"*\\\": true,\\r\\n    \\\"plaintext\\\": true,\\r\\n    \\\"markdown\\\": false,\\r\\n    \\\"scminput\\\": false\\r\\n  },\\r\\n  \\\"files.associations\\\": {\\r\\n    \\\"*.conf\\\": \\\"nginx\\\"\\r\\n  },\\r\\n  \\\"nginx-conf-hint.syntax\\\": \\\"sublime\\\",\\r\\n  \\\"docker.containers.label\\\": \\\"ContainerName\\\",\\r\\n  \\\"docker.containers.sortBy\\\": \\\"Label\\\",\\r\\n  \\\"docker.containers.description\\\": [\\r\\n    \\\"Status\\\"\\r\\n  ],\\r\\n  \\\"gitlens.plusFeatures.enabled\\\": false,\\r\\n  \\\"gitlens.codeLens.enabled\\\": false,\\r\\n  \\\"gitlens.currentLine.enabled\\\": false,\\r\\n  \\\"editor.fontFamily\\\": \\\"'FiraCode Nerd Font', Consolas, 'Courier New', monospace\\\",\\r\\n  \\\"editor.fontLigatures\\\": true,\\r\\n  \\\"files.autoSave\\\": \\\"afterDelay\\\",\\r\\n  \\\"debug.console.fontSize\\\": 14,\\r\\n  \\\"terminal.integrated.fontSize\\\": 14,\\r\\n  \\\"chat.editor.fontSize\\\": 14,\\r\\n  \\\"editor.fontWeight\\\": \\\"100\\\",\\r\\n  \\\"scm.inputFontSize\\\": 14,\\r\\n  \\\"scm.inputFontFamily\\\": \\\"editor\\\",\\r\\n  \\\"terminal.integrated.fontWeight\\\": \\\"200\\\",\\r\\n  \\\"errorLens.fontWeight\\\": \\\"200\\\",\\r\\n  \\\"vsicons.dontShowNewVersionMessage\\\": true,\\r\\n  \\\"[dockerfile]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"ms-azuretools.vscode-docker\\\"\\r\\n  },\\r\\n  \\\"python.analysis.autoFormatStrings\\\": true,\\r\\n  \\\"python.analysis.completeFunctionParens\\\": true,\\r\\n  \\\"python.analysis.typeCheckingMode\\\": \\\"strict\\\",\\r\\n  \\\"security.workspace.trust.untrustedFiles\\\": \\\"open\\\",\\r\\n  \\\"terminal.integrated.persistentSessionScrollback\\\": 3000,\\r\\n  \\\"editor.bracketPairColorization.independentColorPoolPerBracketType\\\": true,\\r\\n  \\\"editor.autoClosingBrackets\\\": \\\"beforeWhitespace\\\",\\r\\n  \\\"editor.autoClosingDelete\\\": \\\"always\\\",\\r\\n  \\\"editor.autoClosingQuotes\\\": \\\"beforeWhitespace\\\",\\r\\n  \\\"semanticdiff.diff.compareMovedCode\\\": true,\\r\\n  \\\"semanticdiff.diff.contextLines\\\": 30,\\r\\n  \\\"editor.semanticTokenColorCustomizations\\\": {\\r\\n    \\\"enabled\\\": true,\\r\\n    \\\"rules\\\": {\\r\\n      \\\"class.defaultLibrary\\\": {\\r\\n        \\\"foreground\\\": \\\"#569CD6\\\"\\r\\n      },\\r\\n      \\\"class.builtin\\\": {\\r\\n        \\\"foreground\\\": \\\"#B57EDC\\\"\\r\\n      },\\r\\n      \\\"class\\\": \\\"#7fb06a\\\",\\r\\n      \\\"enum\\\": \\\"#9c9c56\\\",\\r\\n      \\\"typeParameter\\\": {\\r\\n        \\\"foreground\\\": \\\"#7fb06a\\\",\\r\\n        \\\"fontStyle\\\": \\\"bold\\\"\\r\\n      },\\r\\n      \\\"property\\\": \\\"#c4ba62\\\",\\r\\n      // \\\"parameter\\\": \\\"#ad8b58\\\",\\r\\n      \\\"selfParameter\\\": \\\"#B57EDC\\\",\\r\\n      \\\"variable\\\": \\\"#7a99bf\\\",\\r\\n      \\\"variable.static.readonly\\\": {\\r\\n        \\\"foreground\\\": \\\"#B57EDC\\\",\\r\\n        \\\"fontStyle\\\": \\\"italic\\\"\\r\\n      },\\r\\n      \\\"method\\\": \\\"#E76A7D\\\",\\r\\n      \\\"function\\\": \\\"#E76A7D\\\",\\r\\n      \\\"magicFunction\\\": \\\"#629755\\\",\\r\\n      \\\"macro\\\": {\\r\\n        \\\"fontStyle\\\": \\\"bold\\\"\\r\\n      }\\r\\n    }\\r\\n  },\\r\\n  \\\"editor.tokenColorCustomizations\\\": {\\r\\n    \\\"textMateRules\\\": [\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"keyword.control\\\",\\r\\n          \\\"keyword.operator\\\",\\r\\n          \\\"constant.language\\\",\\r\\n          \\\"storage.type.function\\\",\\r\\n          \\\"punctuation.definition.decorator\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#569CD6\\\",\\r\\n          \\\"fontStyle\\\": \\\"bold\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"meta.function\\\",\\r\\n          \\\"meta.function-call\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#E76A7D\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"string.quoted\\\",\\r\\n          \\\"string.unquoted\\\",\\r\\n          \\\"punctuation.definition.string\\\",\\r\\n          \\\"string.template\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#bab51f\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": \\\"variable\\\",\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#7E7E7E\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"punctuation\\\",\\r\\n          \\\"punctuation.separator\\\",\\r\\n          \\\"punctuation.section\\\",\\r\\n          \\\"keyword.operator.assignment\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#b4b4b4\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": \\\"keyword.codetag\\\",\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#d8d525\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"entity.name.scope-resolution\\\",\\r\\n          \\\"meta.block\\\",\\r\\n          \\\"entity.name.scope-resolution.parameter\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#8796B0\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": \\\"entity.name.function.member\\\",\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#89A97D\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"comment.line.double-slash\\\",\\r\\n          \\\"punctuation.definition.comment\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#6A9955\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"entity.name.type.parameter\\\",\\r\\n          \\\"entity.name.type.class\\\",\\r\\n          \\\"entity.name.function.definition.special.constructor\\\",\\r\\n          \\\"entity.name.function.constructor\\\",\\r\\n          \\\"meta.head.function.definition.special.constructor\\\",\\r\\n          \\\"meta.function.definition.special.constructor\\\",\\r\\n          \\\"entity.name.function.definition.special.member.destructor\\\",\\r\\n          \\\"entity.name.function.destructor\\\",\\r\\n          \\\"meta.head.function.definition.special.member.destructor\\\",\\r\\n          \\\"meta.function.definition.special.member.destructor\\\",\\r\\n          \\\"entity.name.type\\\",\\r\\n          \\\"meta.qualified_type\\\",\\r\\n          \\\"meta.function.definition\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#6F8FA1\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"variable.parameter\\\",\\r\\n          \\\"meta.parameter\\\",\\r\\n          \\\"meta.function.definition.parameters\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#AE6A21\\\"\\r\\n        }\\r\\n      },\\r\\n      {\\r\\n        \\\"scope\\\": [\\r\\n          \\\"variable.parameter.function.language.special.self\\\"\\r\\n        ],\\r\\n        \\\"settings\\\": {\\r\\n          \\\"foreground\\\": \\\"#B57EDC\\\"\\r\\n        }\\r\\n      }\\r\\n    ]\\r\\n  },\\r\\n  \\\"cSpell.userWords\\\": [\\r\\n    \\\"AILLM\\\",\\r\\n    \\\"ASGI\\\",\\r\\n    \\\"authtoken\\\",\\r\\n    \\\"autouse\\\",\\r\\n    \\\"azuretools\\\",\\r\\n    \\\"boto\\\",\\r\\n    \\\"botocore\\\",\\r\\n    \\\"Callsite\\\",\\r\\n    \\\"charliermarsh\\\",\\r\\n    \\\"codetag\\\",\\r\\n    \\\"Consolas\\\",\\r\\n    \\\"Cowabunga\\\",\\r\\n    \\\"dateutil\\\",\\r\\n    \\\"Dima\\\",\\r\\n    \\\"djangoql\\\",\\r\\n    \\\"dont\\\",\\r\\n    \\\"dotenv\\\",\\r\\n    \\\"esbenp\\\",\\r\\n    \\\"Fira\\\",\\r\\n    \\\"Krasik\\\",\\r\\n    \\\"langchain\\\",\\r\\n    \\\"localstack\\\",\\r\\n    \\\"makereport\\\",\\r\\n    \\\"mobx\\\",\\r\\n    \\\"openai\\\",\\r\\n    \\\"Parens\\\",\\r\\n    \\\"phonenumbers\\\",\\r\\n    \\\"phonenumberutil\\\",\\r\\n    \\\"pycache\\\",\\r\\n    \\\"pydantic\\\",\\r\\n    \\\"pytest\\\",\\r\\n    \\\"pytestmark\\\",\\r\\n    \\\"qfiler\\\",\\r\\n    \\\"retryable\\\",\\r\\n    \\\"scminput\\\",\\r\\n    \\\"Scrollback\\\",\\r\\n    \\\"secretmanager\\\",\\r\\n    \\\"semanticdiff\\\",\\r\\n    \\\"signum\\\",\\r\\n    \\\"sqltools\\\",\\r\\n    \\\"structlog\\\",\\r\\n    \\\"timeslot\\\",\\r\\n    \\\"tracebacks\\\",\\r\\n    \\\"tryfirst\\\",\\r\\n    \\\"unidecode\\\",\\r\\n    \\\"venv\\\",\\r\\n    \\\"vsicons\\\",\\r\\n    \\\"whitenoise\\\",\\r\\n    \\\"xframe\\\",\\r\\n    \\\"Yevgeny\\\"\\r\\n  ],\\r\\n  \\\"editor.quickSuggestions\\\": {\\r\\n    \\\"other\\\": \\\"off\\\"\\r\\n  },\\r\\n  \\\"terminal.integrated.shellIntegration.suggestEnabled\\\": true,\\r\\n  \\\"editor.screenReaderAnnounceInlineSuggestion\\\": false,\\r\\n  \\\"editor.acceptSuggestionOnEnter\\\": \\\"smart\\\",\\r\\n  \\\"editor.suggestOnTriggerCharacters\\\": false,\\r\\n  \\\"[jsonc]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"vscode.json-language-features\\\"\\r\\n  },\\r\\n  \\\"files.exclude\\\": {\\r\\n    \\\"**/__pycache__\\\": true,\\r\\n    \\\"**/.pytest_cache\\\": true\\r\\n  },\\r\\n  \\\"python.analysis.autoImportCompletions\\\": true,\\r\\n  \\\"python.analysis.inlayHints.functionReturnTypes\\\": true,\\r\\n  \\\"python.analysis.inlayHints.pytestParameters\\\": true,\\r\\n  \\\"python.analysis.inlayHints.variableTypes\\\": true,\\r\\n  \\\"git.autofetch\\\": true,\\r\\n  \\\"diffEditor.experimental.showMoves\\\": true,\\r\\n  \\\"errorLens.enableOnDiffView\\\": true,\\r\\n  \\\"terminal.integrated.defaultProfile.windows\\\": \\\"Windows PowerShell\\\",\\r\\n  \\\"editor.lineHeight\\\": 1.6,\\r\\n  \\\"terminal.integrated.lineHeight\\\": 1.2,\\r\\n  \\\"git.allowForcePush\\\": true,\\r\\n  \\\"git.useForcePushIfIncludes\\\": false,\\r\\n  \\\"git.enableSmartCommit\\\": true,\\r\\n  \\\"githubPullRequests.pullBranch\\\": \\\"never\\\",\\r\\n  \\\"git.branchProtectionPrompt\\\": \\\"alwaysCommit\\\",\\r\\n  \\\"git.mergeEditor\\\": true,\\r\\n  \\\"gitlens.views.commits.files.layout\\\": \\\"tree\\\",\\r\\n  \\\"cmake.showOptionsMovedNotification\\\": false,\\r\\n  \\\"git.confirmForcePush\\\": false,\\r\\n  \\\"workbench.settings.applyToAllProfiles\\\": [\\r\\n    \\\"editor.formatOnSave\\\"\\r\\n  ],\\r\\n  \\\"git.confirmSync\\\": false,\\r\\n  \\\"[dotenv]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"foxundermoon.shell-format\\\"\\r\\n  },\\r\\n  \\\"python.analysis.packageIndexDepths\\\": [\\r\\n    {\\r\\n      \\\"name\\\": \\\"django\\\",\\r\\n      \\\"depth\\\": 5\\r\\n    }\\r\\n  ],\\r\\n  \\\"python.analysis.ignore\\\": [\\r\\n    \\\".venv\\\"\\r\\n  ],\\r\\n  \\\"[dart]\\\": {\\r\\n    \\\"editor.formatOnSave\\\": true,\\r\\n    \\\"editor.defaultFormatter\\\": \\\"Dart-Code.dart-code\\\",\\r\\n    \\\"editor.codeActionsOnSave\\\": {\\r\\n      \\\"source.organizeImports\\\": \\\"always\\\",\\r\\n    },\\r\\n    \\\"editor.rulers\\\": [\\r\\n      140\\r\\n    ],\\r\\n  },\\r\\n  \\\"dart.lineLength\\\": 140,\\r\\n  \\\"terminal.integrated.enableMultiLinePasteWarning\\\": false,\\r\\n  \\\"git.rebaseWhenSync\\\": true,\\r\\n  \\\"debug.toolBarLocation\\\": \\\"docked\\\",\\r\\n  \\\"debug.hideLauncherWhileDebugging\\\": true,\\r\\n  \\\"eslint.validate\\\": [\\r\\n    \\\"javascript\\\",\\r\\n    \\\"javascriptreact\\\",\\r\\n    \\\"vue\\\"\\r\\n  ],\\r\\n  \\\"eslint.experimental.useFlatConfig\\\": true,\\r\\n  \\\"editor.formatOnPaste\\\": false,\\r\\n  \\\"editor.formatOnType\\\": false,\\r\\n  \\\"editor.codeActionsOnSave\\\": {\\r\\n    \\\"source.fixAll\\\": \\\"explicit\\\"\\r\\n  },\\r\\n  \\\"eslint.format.enable\\\": true,\\r\\n  \\\"eslint.useESLintClass\\\": true,\\r\\n  \\\"amazonQ.showInlineCodeSuggestionsWithCodeReferences\\\": false,\\r\\n  \\\"amazonQ.shareContentWithAWS\\\": false,\\r\\n  \\\"files.autoSaveDelay\\\": 2000,\\r\\n  \\\"javascript.updateImportsOnFileMove.enabled\\\": \\\"always\\\",\\r\\n  \\\"autoimport.useSemiColon\\\": false,\\r\\n  \\\"tsimporter.preferRelative\\\": true,\\r\\n  \\\"editor.formatOnSaveMode\\\": \\\"modifications\\\",\\r\\n  \\\"terminal.integrated.scrollback\\\": 3000,\\r\\n  \\\"ruff.nativeServer\\\": true,\\r\\n  \\\"python.terminal.activateEnvInCurrentTerminal\\\": true,\\r\\n  \\\"projectManager.git.baseFolders\\\": [\\r\\n    \\\"E:\\\\\\\\\\\"\\r\\n  ],\\r\\n  \\\"vs-kubernetes\\\": {\\r\\n    \\\"vscode-kubernetes.helm-path-linux\\\": \\\"/home/<USER>/.local/state/vs-kubernetes/tools/helm/linux-arm64/helm\\\",\\r\\n    \\\"vscode-kubernetes.minikube-path-linux\\\": \\\"/home/<USER>/.local/state/vs-kubernetes/tools/minikube/linux-arm64/minikube\\\",\\r\\n    \\\"vs-kubernetes.kubeconfig\\\": {\\r\\n      \\\"nodeType\\\": \\\"folder.resource\\\",\\r\\n      \\\"nodeCategory\\\": \\\"kubernetes-explorer-node\\\",\\r\\n      \\\"id\\\": \\\"namespace\\\",\\r\\n      \\\"displayName\\\": \\\"Namespaces\\\",\\r\\n      \\\"contextValue\\\": \\\"vsKubernetes.kind\\\",\\r\\n      \\\"kind\\\": {\\r\\n        \\\"displayName\\\": \\\"Namespace\\\",\\r\\n        \\\"pluralDisplayName\\\": \\\"Namespaces\\\",\\r\\n        \\\"manifestKind\\\": \\\"Namespace\\\",\\r\\n        \\\"abbreviation\\\": \\\"namespace\\\",\\r\\n        \\\"apiName\\\": \\\"namespaces\\\"\\r\\n      }\\r\\n    }\\r\\n  },\\r\\n  \\\"hediet.vscode-drawio.resizeImages\\\": null,\\r\\n  \\\"editor.formatOnSave\\\": true,\\r\\n  \\\"[nginx]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"AaaaronZhou.nginx-config-formatter-vscode-extension\\\"\\r\\n  },\\r\\n  \\\"python.analysis.extraPaths\\\": [\\r\\n    \\\"libs/common\\\"\\r\\n  ],\\r\\n  \\\"dart.debugExternalPackageLibraries\\\": true,\\r\\n  \\\"dart.debugSdkLibraries\\\": true,\\r\\n  \\\"editor.minimap.enabled\\\": false,\\r\\n  \\\"terminal.integrated.persistentSessionReviveProcess\\\": \\\"onExitAndWindowClose\\\",\\r\\n  \\\"workbench.colorTheme\\\": \\\"Visual Studio Dark\\\",\\r\\n  \\\"[typescriptreact]\\\": {\\r\\n    \\\"editor.defaultFormatter\\\": \\\"rvest.vs-code-prettier-eslint\\\"\\r\\n  },\\r\\n  \\\"workbench.editor.empty.hint\\\": \\\"hidden\\\",\\r\\n  \\\"editor.mouseWheelScrollSensitivity\\\": 4,\\r\\n  \\\"aider-composer.inlineDiff.enable\\\": true,\\r\\n  \\\"aider-composer.pythonPath\\\": \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\scoop\\\\\\\\apps\\\\\\\\python\\\\\\\\current\\\\\\\\\\\",\\r\\n  \\\"dart.flutterHotReloadOnSave\\\": \\\"allIfDirty\\\",\\r\\n  \\\"windsurf.autoExecutionPolicy\\\": \\\"auto\\\",\\r\\n  \\\"windsurf.explainAndFixInCurrentConversation\\\": true,\\r\\n  \\\"workbench.colorTheme\\\": \\\"Dark\\\",\\r\\n  \\\"window.titleBarStyle\\\": \\\"custom\\\",\\r\\n  \\\"editor.scrollbar.vertical\\\": \\\"visible\\\",\\r\\n  \\\"editor.scrollbar.horizontal\\\": \\\"visible\\\",\\r\\n  \\\"editor.scrollbar.verticalScrollbarSize\\\": 8,\\r\\n  \\\"editor.scrollbar.horizontalScrollbarSize\\\": 8,\\r\\n  \\\"breadcrumbs.showFiles\\\": true,\\r\\n  \\\"breadcrumbs.showArrays\\\": false,\\r\\n  \\\"breadcrumbs.showBooleans\\\": false,\\r\\n  \\\"breadcrumbs.showConstants\\\": false,\\r\\n  \\\"breadcrumbs.showClasses\\\": false,\\r\\n  \\\"breadcrumbs.showConstructors\\\": false,\\r\\n  \\\"breadcrumbs.showEnumMembers\\\": false,\\r\\n  \\\"breadcrumbs.showEnums\\\": false,\\r\\n  \\\"breadcrumbs.showEvents\\\": false,\\r\\n  \\\"breadcrumbs.showFields\\\": false,\\r\\n  \\\"breadcrumbs.showFunctions\\\": false,\\r\\n  \\\"breadcrumbs.showInterfaces\\\": false,\\r\\n  \\\"breadcrumbs.showKeys\\\": false,\\r\\n  \\\"breadcrumbs.showMethods\\\": false,\\r\\n  \\\"breadcrumbs.showModules\\\": false,\\r\\n  \\\"breadcrumbs.showNamespaces\\\": false,\\r\\n  \\\"breadcrumbs.showNull\\\": false,\\r\\n  \\\"breadcrumbs.showNumbers\\\": false,\\r\\n  \\\"breadcrumbs.showObjects\\\": false,\\r\\n  \\\"breadcrumbs.showOperators\\\": false,\\r\\n  \\\"breadcrumbs.showPackages\\\": false,\\r\\n  \\\"breadcrumbs.showProperties\\\": false,\\r\\n  \\\"breadcrumbs.showStrings\\\": false,\\r\\n  \\\"breadcrumbs.showStructs\\\": false,\\r\\n  \\\"breadcrumbs.showTypeParameters\\\": false,\\r\\n  \\\"breadcrumbs.showVariables\\\": false,\\r\\n  \\\"workbench.colorCustomizations\\\": {\\r\\n    // ACTIVITY BAR\\r\\n    \\\"activityBar.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"activityBar.foreground\\\": \\\"#ffffff\\\",\\r\\n    \\\"activityBar.inactiveForeground\\\": \\\"#a9a9a9\\\",\\r\\n    \\\"activityBar.border\\\": \\\"#24262B\\\",\\r\\n    // SIDEBAR (Explorer)\\r\\n    \\\"sideBar.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"sideBar.foreground\\\": \\\"#ffffff\\\",\\r\\n    \\\"sideBarSectionHeader.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"sideBarSectionHeader.foreground\\\": \\\"#ffffff\\\",\\r\\n    \\\"sideBar.border\\\": \\\"#24262B\\\",\\r\\n    // BREADCRUMBS\\r\\n    \\\"breadcrumb.foreground\\\": \\\"#aaaaaa\\\",\\r\\n    \\\"breadcrumb.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"breadcrumb.focusForeground\\\": \\\"#ffffff\\\",\\r\\n    \\\"breadcrumb.activeSelectionForeground\\\": \\\"#ffffff\\\",\\r\\n    \\\"breadcrumbPicker.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"breadcrumbIcon.foreground\\\": \\\"#aaaaaa\\\",\\r\\n    // TITLE BAR\\r\\n    \\\"titleBar.activeBackground\\\": \\\"#181A1F\\\",\\r\\n    \\\"titleBar.activeForeground\\\": \\\"#ffffff\\\",\\r\\n    \\\"titleBar.inactiveBackground\\\": \\\"#181A1F\\\",\\r\\n    \\\"titleBar.inactiveForeground\\\": \\\"#cccccc\\\",\\r\\n    \\\"titleBar.border\\\": \\\"#24262B\\\",\\r\\n    // STATUS BAR\\r\\n    \\\"statusBar.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"statusBar.foreground\\\": \\\"#ffffff\\\",\\r\\n    \\\"statusBar.border\\\": \\\"#24262B\\\",\\r\\n    \\\"statusBar.noFolderBackground\\\": \\\"#181A1F\\\",\\r\\n    \\\"statusBar.debuggingBackground\\\": \\\"#24262B\\\",\\r\\n    // EDITOR & TABS\\r\\n    \\\"editor.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"editor.foreground\\\": \\\"#ffffff\\\",\\r\\n    \\\"editorGroup.border\\\": \\\"#24262B\\\",\\r\\n    \\\"editorGroupHeader.tabsBackground\\\": \\\"#181A1F\\\",\\r\\n    \\\"editorGroupHeader.tabsBorder\\\": \\\"#24262B\\\",\\r\\n    // Highlight the active tab with #30343F\\r\\n    \\\"tab.activeBackground\\\": \\\"#30343F\\\",\\r\\n    \\\"tab.activeForeground\\\": \\\"#ffffff\\\",\\r\\n    \\\"tab.inactiveBackground\\\": \\\"#181A1F\\\",\\r\\n    \\\"tab.inactiveForeground\\\": \\\"#aaaaaa\\\",\\r\\n    \\\"tab.border\\\": \\\"#24262B\\\",\\r\\n    // PANELS\\r\\n    \\\"panel.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"panel.foreground\\\": \\\"#ffffff\\\",\\r\\n    \\\"panel.border\\\": \\\"#24262B\\\",\\r\\n    // SCROLLBARS\\r\\n    \\\"scrollbarSlider.background\\\": \\\"#3A3D41\\\",\\r\\n    \\\"scrollbarSlider.hoverBackground\\\": \\\"#484b50\\\",\\r\\n    \\\"scrollbarSlider.activeBackground\\\": \\\"#606366\\\",\\r\\n    // OPTIONAL CONTRAST TWEAKS\\r\\n    \\\"editorLineNumber.foreground\\\": \\\"#8b8b8b\\\",\\r\\n    \\\"editor.selectionBackground\\\": \\\"#3A3D41\\\",\\r\\n    // \\\"editorCursor.foreground\\\": \\\"#ffffff\\\",\\r\\n    // Optional: borders, placeholder text, etc.\\r\\n    \\\"input.border\\\": \\\"#24262B\\\",\\r\\n    \\\"input.placeholderForeground\\\": \\\"#aaaaaa\\\",\\r\\n    // If you also want to style the “dropdown” portions (e.g. pickers):\\r\\n    \\\"dropdown.background\\\": \\\"#30343F\\\",\\r\\n    \\\"dropdown.foreground\\\": \\\"#FFFFFF\\\",\\r\\n    // Gutter (line numbers, breakpoints, etc.) background\\r\\n    // Make it a shade lighter/darker so the boundary stands out\\r\\n    \\\"editorGutter.background\\\": \\\"#16181D\\\",\\r\\n    // QUICK INPUT PANEL (the background and text color of the entire pop-up)\\r\\n    \\\"quickInput.background\\\": \\\"#181A1F\\\",\\r\\n    \\\"quickInput.foreground\\\": \\\"#FFFFFF\\\",\\r\\n    // If you want the \\\"input\\\" portion (top text box) to match:\\r\\n    \\\"input.background\\\": \\\"#30343F\\\",\\r\\n    \\\"input.foreground\\\": \\\"#FFFFFF\\\",\\r\\n    // LIST SELECTION COLORS (these are crucial for showing the \\\"cursor\\\")\\r\\n    // The item your arrow key is currently on:\\r\\n    \\\"list.focusBackground\\\": \\\"#30343F\\\",\\r\\n    \\\"list.focusForeground\\\": \\\"#FFFFFF\\\",\\r\\n    // Optionally, the item you’ve clicked or activated:\\r\\n    \\\"list.activeSelectionBackground\\\": \\\"#30343F\\\",\\r\\n    \\\"list.activeSelectionForeground\\\": \\\"#FFFFFF\\\",\\r\\n    // Hover color if you want a highlight when you mouse over items:\\r\\n    \\\"list.hoverBackground\\\": \\\"#24262B\\\",\\r\\n    \\\"list.hoverForeground\\\": \\\"#FFFFFF\\\",\\r\\n    // Optional: color for non-focused, inactive selections\\r\\n    \\\"list.inactiveSelectionBackground\\\": \\\"#24262B\\\",\\r\\n    \\\"list.inactiveSelectionForeground\\\": \\\"#AAAAAA\\\",\\r\\n    // The global focus border used in many widgets (input boxes, quick pick, etc.)\\r\\n    \\\"focusBorder\\\": \\\"#00000000\\\",\\r\\n    // Specifically remove the focus border from text inputs\\r\\n    \\\"input.focusBorder\\\": \\\"#00000000\\\",\\r\\n    // Remove focus outline from buttons\\r\\n    \\\"button.focusBorder\\\": \\\"#00000000\\\",\\r\\n    // Remove focus outline from dropdowns\\r\\n    \\\"dropdown.focusBorder\\\": \\\"#00000000\\\",\\r\\n    // Remove focus outline from list/tree items (e.g., in the Explorer or Quick Pick)\\r\\n    \\\"list.focusOutline\\\": \\\"#00000000\\\",\\r\\n    \\\"list.inactiveFocusOutline\\\": \\\"#00000000\\\"\\r\\n  },\\r\\n  \\\"windsurf.chatFontSize\\\": \\\"default\\\",\\r\\n  \\\"windsurf.rememberLastModelSelection\\\": false,\\r\\n  \\\"windsurf.openRecentConversation\\\": false,\\r\\n  \\\"windsurf.enableTabToJump\\\": true,\\r\\n  \\\"remote.autoForwardPortsSource\\\": \\\"hybrid\\\",\\r\\n  \\\"explorer.confirmPasteNative\\\": false,\\r\\n  \\\"terminal.integrated.rightClickBehavior\\\": \\\"default\\\",\\r\\n  \\\"containers.containers.description\\\": [\\r\\n    \\\"Status\\\"\\r\\n  ],\\r\\n  \\\"containers.containers.label\\\": \\\"ContainerName\\\",\\r\\n  \\\"containers.containers.sortBy\\\": \\\"Label\\\",\\r\\n  \\\"intellicodeApiExamples.typescript.enabled\\\": false,\\r\\n  \\\"intellicodeApiExamples.python.enabled\\\": false,\\r\\n  \\\"cSpell.showAutocompleteDirectiveSuggestions\\\": false,\\r\\n  \\\"javascript.suggest.enabled\\\": false,\\r\\n  \\\"typescript.suggest.enabled\\\": false,\\r\\n  \\\"editor.snippetSuggestions\\\": \\\"none\\\",\\r\\n  \\\"editor.wordBasedSuggestions\\\": \\\"off\\\",\\r\\n  \\\"editor.inlineSuggest.suppressSuggestions\\\": true,\\r\\n  \\\"dart.closeDevTools\\\": \\\"ifOpened\\\",\\r\\n  \\\"dart.devToolsBrowser\\\": \\\"default\\\",\\r\\n  \\\"dart.devToolsLocation\\\": {\\r\\n    \\\"default\\\": \\\"sidebar\\\",\\r\\n    \\\"inspector\\\": \\\"sidebar\\\"\\r\\n  },\\r\\n  \\\"diffEditor.ignoreTrimWhitespace\\\": false,\\r\\n  \\\"diffEditor.codeLens\\\": true\\r\\n}\"}", "keybindings": "{\"keybindings\":\"// Place your key bindings in this file to override the defaults\\n[\\n  {\\n    \\\"key\\\": \\\"ctrl+t\\\",\\n    \\\"command\\\": \\\"-git.sync\\\",\\n    \\\"when\\\": \\\"!operationInProgress\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+y\\\",\\n    \\\"command\\\": \\\"-editor.action.deleteLines\\\",\\n    \\\"when\\\": \\\"editorTextFocus && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+y\\\",\\n    \\\"command\\\": \\\"-redo\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+n\\\",\\n    \\\"command\\\": \\\"-workbench.action.files.newUntitledFile\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+t\\\",\\n    \\\"command\\\": \\\"-workbench.action.showAllSymbols\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+t\\\",\\n    \\\"command\\\": \\\"editor.action.copyLinesDownAction\\\",\\n    \\\"when\\\": \\\"editorTextFocus && !editorHasSelection && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+d\\\",\\n    \\\"command\\\": \\\"-editor.action.copyLinesDownAction\\\",\\n    \\\"when\\\": \\\"editorTextFocus && !editorHasSelection && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+d\\\",\\n    \\\"command\\\": \\\"editor.action.deleteLines\\\",\\n    \\\"when\\\": \\\"textInputFocus && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+shift+k\\\",\\n    \\\"command\\\": \\\"-editor.action.deleteLines\\\",\\n    \\\"when\\\": \\\"textInputFocus && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"f2\\\",\\n    \\\"command\\\": \\\"-editor.action.marker.next\\\",\\n    \\\"when\\\": \\\"editorFocus\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"f2\\\",\\n    \\\"command\\\": \\\"editor.action.rename\\\",\\n    \\\"when\\\": \\\"editorHasRenameProvider && editorTextFocus && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"shift+f6\\\",\\n    \\\"command\\\": \\\"-editor.action.rename\\\",\\n    \\\"when\\\": \\\"editorHasRenameProvider && editorTextFocus && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+m\\\",\\n    \\\"command\\\": \\\"-editor.action.toggleTabFocusMode\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+m\\\",\\n    \\\"command\\\": \\\"workbench.action.gotoSymbol\\\",\\n    \\\"when\\\": \\\"editorTextFocus\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+right\\\",\\n    \\\"command\\\": \\\"-editor.action.inlineSuggest.acceptNextWord\\\",\\n    \\\"when\\\": \\\"inlineSuggestionVisible && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"f4\\\",\\n    \\\"command\\\": \\\"-references-view.next\\\",\\n    \\\"when\\\": \\\"reference-list.hasResult && references-view.canNavigate\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"f4\\\",\\n    \\\"command\\\": \\\"git.timeline.openDiff\\\",\\n    \\\"when\\\": \\\"editorTextFocus && !isInDiffEditor\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"f4\\\",\\n    \\\"command\\\": \\\"-editor.action.goToDeclaration\\\",\\n    \\\"when\\\": \\\"editorTextFocus\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"f4\\\",\\n    \\\"command\\\": \\\"git.openFile\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+y\\\",\\n    \\\"command\\\": \\\"explorer.newFile\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+d\\\",\\n    \\\"command\\\": \\\"-editor.action.duplicateSelection\\\",\\n    \\\"when\\\": \\\"editorHasSelection && editorTextFocus && !editorReadonly\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+t\\\",\\n    \\\"command\\\": \\\"editor.action.duplicateSelection\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+backspace\\\",\\n    \\\"command\\\": \\\"-chatEditor.action.reject\\\",\\n    \\\"when\\\": \\\"chat.hasEditorModifications && editorFocus && hasUndecidedChatEditingResource\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+shift+backspace\\\",\\n    \\\"command\\\": \\\"-chatEditor.action.undoHunk\\\",\\n    \\\"when\\\": \\\"chat.hasEditorModifications && editorFocus && hasUndecidedChatEditingResource && !chatSessionRequestInProgress\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+backspace\\\",\\n    \\\"command\\\": \\\"-windsurf.prioritized.cascadeRejectAllInFile\\\",\\n    \\\"when\\\": \\\"editorTextFocus && windsurf.canAcceptOrRejectAllCascadeEditsInFile\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+backspace\\\",\\n    \\\"command\\\": \\\"-windsurf.terminalCommand.reject\\\",\\n    \\\"when\\\": \\\"terminalFocus && windsurf.canTriggerTerminalCommandAction\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+backspace\\\",\\n    \\\"command\\\": \\\"-windsurf.command.reject\\\",\\n    \\\"when\\\": \\\"editorTextFocus && windsurf.canAcceptOrRejectCommand && !editorHasSelection\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+l\\\",\\n    \\\"command\\\": \\\"workbench.action.terminal.clear\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+l\\\",\\n    \\\"command\\\": \\\"-workbench.action.chat.newChat\\\",\\n    \\\"when\\\": \\\"chatIsEnabled && inChat && !config.chat.unifiedChatView && chatLocation != 'editing-session'\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+l\\\",\\n    \\\"command\\\": \\\"-workbench.action.chat.newEditSession\\\",\\n    \\\"when\\\": \\\"chatEditingParticipantRegistered && chatIsEnabled && inChat\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+l\\\",\\n    \\\"command\\\": \\\"-expandLineSelection\\\",\\n    \\\"when\\\": \\\"textInputFocus\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+l\\\",\\n    \\\"command\\\": \\\"-notebook.centerActiveCell\\\",\\n    \\\"when\\\": \\\"notebookEditorFocused\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+l\\\",\\n    \\\"command\\\": \\\"-windsurf.prioritized.chat.open\\\",\\n    \\\"when\\\": \\\"!terminalFocus\\\"\\n  },\\n  {\\n    \\\"key\\\": \\\"ctrl+l\\\",\\n    \\\"command\\\": \\\"-windsurf.prioritized.chat.openFromTerminal\\\",\\n    \\\"when\\\": \\\"terminalFocus\\\"\\n  }\\n]\",\"platform\":3}", "snippets": "{\"snippets\":{\"python.json\":\"{\\n\\t// Place your snippets for python here. Each snippet is defined under a snippet name and has a prefix, body and \\n\\t// description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:\\n\\t// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the \\n\\t// same ids are connected.\\n\\t// Example:\\n\\t// \\\"Print to console\\\": {\\n\\t// \\t\\\"prefix\\\": \\\"log\\\",\\n\\t// \\t\\\"body\\\": [\\n\\t// \\t\\t\\\"console.log('$1');\\\",\\n\\t// \\t\\t\\\"$2\\\"\\n\\t// \\t],\\n\\t// \\t\\\"description\\\": \\\"Log output to console\\\"\\n\\t// }\\n}\"}}", "extensions": "[{\"identifier\":{\"id\":\"aaaaronzhou.nginx-config-formatter-vscode-extension\",\"uuid\":\"187dc6f5-0396-4bdd-bb7e-3426924c71e5\"},\"displayName\":\"Nginx Config Formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"adpyke.vscode-sql-formatter\",\"uuid\":\"ac70a31d-d9ab-417b-b259-baf7cd9d6cb0\"},\"displayName\":\"SQL Formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ahmadalli.vscode-nginx-conf\",\"uuid\":\"9a97436d-76aa-479c-8ae9-db2f400a7b04\"},\"displayName\":\"NGINX Configuration Language Support\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ale<PERSON>gnani.project-manager\",\"uuid\":\"1b747f06-3789-4ebd-ac99-f1fe430c3347\"},\"displayName\":\"Project Manager\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"arturock.gitstash\",\"uuid\":\"c6f98943-7f9b-4776-b2a8-409227a481e2\"},\"displayName\":\"Git Stash\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"charliermarsh.ruff\",\"uuid\":\"c2ca9b43-fa38-44fc-928e-5125970b9c00\"},\"displayName\":\"Ruff\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"christian-kohler.npm-intellisense\",\"uuid\":\"************************************\"},\"displayName\":\"npm Intellisense\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"codeium.windsurfpyright\",\"uuid\":\"1adb9c26-188a-4fb6-840e-b1e951ecff7c\"},\"displayName\":\"Windsurf Pyright\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"codezombiech.gitignore\",\"uuid\":\"3e891cf9-53cb-49a3-8d01-8f0b1f0afb29\"},\"displayName\":\"gitignore\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dart-code.dart-code\",\"uuid\":\"f57f68ea-9ee8-42b5-9a97-041d3e4278c4\"},\"displayName\":\"Dart\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dart-code.flutter\",\"uuid\":\"f6c3ec04-6057-4d9c-b997-69cba07a6158\"},\"displayName\":\"Flutter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dbaeumer.vscode-eslint\",\"uuid\":\"583b2b34-2c1e-4634-8c0b-0b82e283ea3a\"},\"displayName\":\"ESLint\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dbcode.dbcode\",\"uuid\":\"a172cbe5-f099-458f-b024-382d48f54aa0\"},\"displayName\":\"DBCode - Database Management\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.githistory\",\"uuid\":\"5960f38e-0bbe-4644-8f9c-9c8824e82511\"},\"displayName\":\"Git History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.python-environment-manager\",\"uuid\":\"0c9f60fd-5588-42f7-9176-e80c3ae111ec\"},\"displayName\":\"Python Environment Manager (deprecated)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.python-extension-pack\",\"uuid\":\"f5188937-53e0-45bb-a16d-61231003fa3b\"},\"displayName\":\"Python Extension Pack\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"eamodio.gitlens\",\"uuid\":\"4de763bd-505d-4978-9575-2b7696ecf94e\"},\"displayName\":\"GitLens — Git supercharged\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"emilast.logfilehighlighter\",\"uuid\":\"e8b488af-fccf-4adf-b60c-fc7455bea107\"},\"displayName\":\"Log File Highlighter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"esbenp.prettier-vscode\",\"uuid\":\"96fa4707-6983-4489-b7c5-d5ffdfdcce90\"},\"displayName\":\"Prettier - Code formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"esphome.esphome-vscode\",\"uuid\":\"b48b693e-8a2d-4491-a373-c98cde694c27\"},\"displayName\":\"ESPHome\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"flutterando.flutter-mobx\",\"uuid\":\"4da4c97b-b272-4552-a789-0af9ddaec7f9\"},\"displayName\":\"flutter_mobx\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"foxundermoon.shell-format\",\"uuid\":\"5fb19573-2183-4cf2-b53d-0fb869dae7ae\"},\"displayName\":\"shell-format\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.remotehub\",\"uuid\":\"fc7d7e85-2e58-4c1c-97a3-2172ed9a77cd\"},\"displayName\":\"GitHub Repositories\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.vscode-github-actions\",\"uuid\":\"04f49bfc-8330-4eee-8237-ea938fb755ef\"},\"displayName\":\"GitHub Actions\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.vscode-pull-request-github\",\"uuid\":\"69ddd764-339a-4ecc-97c1-9c4ece58e36d\"},\"displayName\":\"GitHub Pull Requests\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"gitworktrees.git-worktrees\",\"uuid\":\"7a773281-9fa5-47ce-8e6f-aabeec3859a0\"},\"displayName\":\"Git Worktrees\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"google.geminicodeassist\",\"uuid\":\"51643712-2cb2-4384-b7cc-d55b01b8274b\"},\"displayName\":\"Gemini Code Assist\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"graphql.vscode-graphql\",\"uuid\":\"55ef6448-487b-49a0-a66e-4d2d9bb82229\"},\"displayName\":\"GraphQL: Language Feature Support\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"graphql.vscode-graphql-syntax\",\"uuid\":\"e1ab76b1-9acd-4ffa-baf7-1d9eaf7cf3d2\"},\"displayName\":\"GraphQL: Syntax Highlighting\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"hbenl.vscode-test-explorer\",\"uuid\":\"ff96f1b4-a4b8-45ef-8ecf-c232c0cb75c8\"},\"displayName\":\"Test Explorer UI\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"hediet.vscode-drawio\",\"uuid\":\"ea6a6046-2132-421f-a984-664909fcf0b8\"},\"displayName\":\"Draw.io Integration\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"jeroen-meijer.pubspec-assist\",\"uuid\":\"91c5dadd-29e8-4ced-8d0b-6c4bf5901ee1\"},\"displayName\":\"Pubspec Assist\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"k--kato.intellij-idea-keybindings\",\"uuid\":\"f30b63fa-e34a-40af-a573-5de5ecfb6c5e\"},\"displayName\":\"IntelliJ IDEA Keybindings\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"kevinrose.vsc-python-indent\",\"uuid\":\"f3cbfb84-b1e1-40ff-b70f-************\"},\"displayName\":\"Python Indent\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"littlefoxteam.vscode-python-test-adapter\",\"uuid\":\"912fd5ae-d9f2-4c31-8a0b-d7fb669afbb4\"},\"displayName\":\"Python Test Explorer for Visual Studio Code\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"lukas-tr.materialdesignicons-intellisense\",\"uuid\":\"6bddf0ae-fff2-4b40-9091-3361abfb87d4\"},\"displayName\":\"Material Design Icons Intellisense\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mechatroner.rainbow-csv\",\"uuid\":\"3792588c-3d35-442d-91ea-fe6a755e8155\"},\"displayName\":\"Rainbow CSV\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mikestead.dotenv\",\"uuid\":\"532533c9-a894-4a58-9eee-bbfbe7c06f71\"},\"displayName\":\"DotENV\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mindaro-dev.file-downloader\",\"uuid\":\"c5f9ea77-0f11-4bd6-9736-341deca7a35c\"},\"displayName\":\"File Downloader\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-azuretools.vscode-containers\",\"uuid\":\"2cd1d691-3d69-4d2d-ae39-fda4bc4cfd3d\"},\"displayName\":\"Container Tools\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-azuretools.vscode-docker\",\"uuid\":\"************************************\"},\"displayName\":\"Docker\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-kubernetes-tools.vscode-kubernetes-tools\",\"uuid\":\"4837e4f3-1b01-4732-b1a6-daa57ef64cab\"},\"displayName\":\"Kubernetes\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.debugpy\",\"uuid\":\"4bd5d2c9-9d65-401a-b0b2-7498d9f17615\"},\"displayName\":\"Python Debugger\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.python\",\"uuid\":\"f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5\"},\"displayName\":\"Python\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter\",\"uuid\":\"6c2f1801-1e7f-45b2-9b5c-7782f1e076e8\"},\"displayName\":\"Jupyter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-keymap\",\"uuid\":\"9f6dc8db-620c-4844-b8c5-e74914f1be27\"},\"displayName\":\"Jupyter Keymap\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-renderers\",\"uuid\":\"b15c72f8-d5fe-421a-a4f7-27ed9f6addbf\"},\"displayName\":\"Jupyter Notebook Renderers\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-cell-tags\",\"uuid\":\"ab4fb32a-befb-4102-adf9-1652d0cd6a5e\"},\"displayName\":\"Jupyter Cell Tags\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-slideshow\",\"uuid\":\"e153ca70-b543-4865-b4c5-b31d34185948\"},\"displayName\":\"Jupyter Slide Show\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh-edit\",\"uuid\":\"bfeaf631-bcff-4908-93ed-fda4ef9a0c5c\"},\"displayName\":\"Remote - SSH: Editing Configuration Files\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.vscode-remote-extensionpack\",\"uuid\":\"23d72dfc-8dd1-4e30-926e-8783b4378f13\"},\"displayName\":\"Remote Development\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.azure-repos\",\"uuid\":\"25cfa506-1433-4595-a73f-61666807126d\"},\"displayName\":\"Azure Repos\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-explorer\",\"uuid\":\"11858313-52cc-4e57-b3e4-d7b65281e34b\"},\"displayName\":\"Remote Explorer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-repositories\",\"uuid\":\"cf5142f0-3701-4992-980c-9895a750addf\"},\"displayName\":\"Remote Repositories\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-server\",\"uuid\":\"105c0b3c-07a9-4156-a4fc-4141040eb07e\"},\"displayName\":\"Remote - Tunnels\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.test-adapter-converter\",\"uuid\":\"47210ec2-0324-4cbb-9523-9dff02a5f9ec\"},\"displayName\":\"Test Adapter Converter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"nefrob.vscode-just-syntax\",\"uuid\":\"4d059c6e-aaea-42e3-97f5-f4fd35b70d3d\"},\"displayName\":\"vscode-just\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"njpwerner.autodocstring\",\"uuid\":\"2d6fea35-f68e-461d-9b7b-5cd05be99451\"},\"displayName\":\"autoDocstring - Python Docstring Generator\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"oderwat.indent-rainbow\",\"uuid\":\"eaa2127d-cb69-4ab9-8505-a60c9ee5f28b\"},\"applicationScoped\":false},{\"identifier\":{\"id\":\"rangav.vscode-thunder-client\",\"uuid\":\"2fd56207-78ef-49d4-95d2-9b801eee4dbf\"},\"displayName\":\"Thunder Client\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"raynigon.nginx-formatter\",\"uuid\":\"1a7b1f64-469d-4116-bb35-508ea3894f88\"},\"displayName\":\"nginx-formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"redhat.java\",\"uuid\":\"198a707e-28af-4e84-8610-6e2f628dd12d\"},\"displayName\":\"Language Support for Java(TM) by Red Hat\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"redhat.vscode-yaml\",\"uuid\":\"2061917f-f76a-458a-8da9-f162de22b97e\"},\"displayName\":\"YAML\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rockingskier.copy-copy-paste\",\"uuid\":\"06457f7c-6032-4500-880d-1575c1b1cd01\"},\"displayName\":\"Copy Copy Paste\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rust-lang.rust-analyzer\",\"uuid\":\"06574cb4-e5dc-4631-8174-a543a4533621\"},\"displayName\":\"rust-analyzer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rvest.vs-code-prettier-eslint\",\"uuid\":\"d4b06bd6-36a0-469f-be55-c0a73413b688\"},\"displayName\":\"Prettier ESLint\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"saoudrizwan.claude-dev\",\"uuid\":\"ad94c633-a9d5-4f78-b85f-c664e7d91a0f\"},\"displayName\":\"Cline\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"streetsidesoftware.code-spell-checker\",\"uuid\":\"f6dbd813-b0a0-42c1-90ea-10dde9d925a7\"},\"displayName\":\"Code Spell Checker\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"stylelint.vscode-stylelint\",\"uuid\":\"ec35b5a3-9802-4c68-b5ff-e85f19ec0977\"},\"displayName\":\"Stylelint\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"tamasfe.even-better-toml\",\"uuid\":\"b2215d5f-675e-4a2b-b6ac-1ca737518b78\"},\"displayName\":\"Even Better TOML\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"teclado.vscode-nginx-format\",\"uuid\":\"40261fd4-b94e-428c-9ce4-c8e9f7acda78\"},\"displayName\":\"nginx-format\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"usernamehw.errorlens\",\"uuid\":\"9d8c32ab-354c-4daf-a9bf-20b633734435\"},\"displayName\":\"Error Lens\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vadimcn.vscode-lldb\",\"uuid\":\"bee31e34-a44b-4a76-9ec2-e9fd1439a0f6\"},\"displayName\":\"CodeLLDB\",\"version\":\"1.11.5\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"visualstudioexptteam.intellicode-api-usage-examples\",\"uuid\":\"9fa2a00e-3bfa-4c2a-abc4-a865bb2b5cf3\"},\"displayName\":\"IntelliCode API Usage Examples\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscjava.vscode-gradle\",\"uuid\":\"a226f06e-9b5e-493b-b92a-553d3572f969\"},\"displayName\":\"Gradle for Java\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscjava.vscode-java-debug\",\"uuid\":\"61fcd0cf-64d7-4836-8d6b-d55f4fb83281\"},\"displayName\":\"Debugger for Java\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscjava.vscode-java-dependency\",\"uuid\":\"7865e561-1c83-410e-9b99-aabada597a7e\"},\"displayName\":\"Project Manager for Java\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscjava.vscode-java-pack\",\"uuid\":\"96f11e1f-1a46-4592-b084-f025b2c2a81f\"},\"displayName\":\"Extension Pack for Java\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscjava.vscode-java-test\",\"uuid\":\"67c06b0d-1891-42ca-b2a8-113e79bff069\"},\"displayName\":\"Test Runner for Java\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscjava.vscode-maven\",\"uuid\":\"b0f06c6b-24fb-4d7b-bd79-bc5e2fa17312\"},\"displayName\":\"Maven for Java\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscode-icons-team.vscode-icons\",\"uuid\":\"9ccc1dd7-7ec4-4a46-bd4f-7d7b8b9d322a\"},\"displayName\":\"vscode-icons\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vue.volar\",\"uuid\":\"a95ee795-1576-4ffa-acda-8d6e6a95c584\"},\"displayName\":\"Vue (Official)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"wholroyd.jinja\",\"uuid\":\"c941a679-d500-46a8-b2a9-************\"},\"displayName\":\"Jinja\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"william-voyek.vscode-nginx\",\"uuid\":\"414b2873-c80e-4dc6-9031-bd185cfb3944\"},\"displayName\":\"NGINX Configuration\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"yoavbls.pretty-ts-errors\",\"uuid\":\"1e149c89-8f97-447e-863d-1146f0ad1b70\"},\"displayName\":\"Pretty TypeScript Errors\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"zhuangtongfa.material-theme\",\"uuid\":\"26a529c9-2654-4b95-a63f-02f6a52429e6\"},\"displayName\":\"One Dark Pro\",\"applicationScoped\":false}]", "globalState": "{\"storage\":{\"windsurfProductEducation\":\"{\\\"onboardingState\\\":2,\\\"onboardingItems\\\":[{\\\"id\\\":\\\"windsurf.prioritized.chat.open\\\",\\\"title\\\":\\\"Code with Cascade\\\",\\\"completed\\\":true,\\\"tooltip\\\":\\\"Cascade is a powerful reasoning engine capable of deep, multi-step\\\\ndeliberation, equipped to both edit and explain.\\\",\\\"displayName\\\":\\\"Toggle Cascade\\\",\\\"alwaysShow\\\":true},{\\\"id\\\":\\\"windsurf.prioritized.command.open\\\",\\\"title\\\":\\\"Edit Code using Command\\\",\\\"completed\\\":true,\\\"tooltip\\\":\\\"Command solicits instructions for in-line edits or generations.\\\\nTry selecting some code and using the keybinding below.\\\",\\\"displayName\\\":\\\"Edit Code using Command\\\",\\\"alwaysShow\\\":false},{\\\"id\\\":\\\"editor.action.inlineSuggest.commit\\\",\\\"title\\\":\\\"Accept an Autocomplete while editing code\\\",\\\"completed\\\":true,\\\"tooltip\\\":\\\"Autocomplete suggestions show in-line while you edit code. Press tab to accept them.\\\",\\\"displayName\\\":\\\"Accept an Autocomplete\\\",\\\"alwaysShow\\\":false},{\\\"id\\\":\\\"workbench.action.showCommands\\\",\\\"title\\\":\\\"Open Command Palette\\\",\\\"completed\\\":true,\\\"tooltip\\\":\\\"The Command Palette provides quick search access to IDE commands.\\\\nYou can also use Ctrl+P to search for files.\\\",\\\"displayName\\\":\\\"Open Command Palette\\\",\\\"alwaysShow\\\":true},{\\\"id\\\":\\\"windsurf.prioritized.supercompleteAccept\\\",\\\"title\\\":\\\"Accept a Supercomplete while editing code\\\",\\\"completed\\\":true,\\\"tooltip\\\":\\\"Supercomplete suggests edits in a region beyond your immediate cursor position. It remembers your recent edit history and serves as a keystroke-saving edit assistant.\\\",\\\"displayName\\\":\\\"Tab to Accept a Supercomplete, Esc to hide\\\",\\\"alwaysShow\\\":false}]}\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"dartDependencyTree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"commitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"compareCommitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"materialDesignIconsExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"claude-dev.SidebarProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"makefile.outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"rustDependencies\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"javaProjectExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"mavenProjects\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":true,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"gitstash.explorer\\\",\\\"isHidden\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"gitlens.views.repositories\\\",\\\"isHidden\\\":true,\\\"order\\\":6},{\\\"id\\\":\\\"gitlens.views.commits\\\",\\\"isHidden\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"gitlens.views.branches\\\",\\\"isHidden\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"gitlens.views.remotes\\\",\\\"isHidden\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"gitlens.views.stashes\\\",\\\"isHidden\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"gitlens.views.tags\\\",\\\"isHidden\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"gitlens.views.worktrees\\\",\\\"isHidden\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"gitlens.views.scm.grouped\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false}]\",\"workbench.activityBar.location\":\"default\",\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.continue\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.github-cweijan-mysql\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.flutterPropertyEditor\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.github-dbclient-history\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.sidebarDevToolsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.github-cweijan-nosql\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.package-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.sidebarDevToolsDeepLinksContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.gitlens\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.gitlensInspect\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.gitlensPatch\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.github-actions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.github-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":20},{\\\"id\\\":\\\"workbench.view.extension.github-pull-request\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":21},{\\\"id\\\":\\\"workbench.view.extension.dockerView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.kubernetesView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.jupyter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.sqltoolsActivityBarContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.view.extension.thunder-client\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.project-manager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.cspell-info-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":28},{\\\"id\\\":\\\"workbench.view.extension.cspell-regexp-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.cspell-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.dbcodeActivitybarContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.rustSyntaxTreeContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":27},{\\\"id\\\":\\\"workbench.view.extension.containersView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":23},{\\\"id\\\":\\\"workbench.view.extension.gradleContainerView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":30},{\\\"id\\\":\\\"workbench.panel.chatSidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100}]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.dbclient-search-result\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.gitlensPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.jupyter-variables\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.sqltoolsPanelContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.cspellPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.dbcodePanelContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.telemetryOptOutShown\":\"true\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"CppSshTargetsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"lldb.loadedModules\\\",\\\"isHidden\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"lldb.excludedCallers\\\",\\\"isHidden\\\":false,\\\"order\\\":10}]\",\"editorGroupWindsurfWelcomeKeybindings\":\"[{\\\"text\\\":\\\"Code with Cascade\\\",\\\"id\\\":\\\"windsurf.prioritized.chat.open\\\"},{\\\"text\\\":\\\"Edit code inline\\\",\\\"keybinding\\\":[{\\\"ctrlKey\\\":true,\\\"shiftKey\\\":false,\\\"altKey\\\":false,\\\"metaKey\\\":false,\\\"keyLabel\\\":\\\"I\\\",\\\"keyAriaLabel\\\":\\\"I\\\"}],\\\"label\\\":\\\"Ctrl+I\\\",\\\"id\\\":\\\"windsurf.prioritized.command.open\\\"}]\",\"themeUpdatedNotificationShown\":\"true\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs-dark vscode-theme-defaults-themes-dark_modern-json\\\",\\\"label\\\":\\\"Dark Modern\\\",\\\"settingsId\\\":\\\"Default Dark Modern\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#D4D4D4\\\"},\\\"scope\\\":[\\\"meta.embedded\\\",\\\"source.groovy.embedded\\\",\\\"string meta.image.inline.markdown\\\",\\\"variable.legacy.builtin.python\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"emphasis\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":\\\"strong\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"comment\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"constant.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.enummember\\\",\\\"keyword.operator.plus.exponent\\\",\\\"keyword.operator.minus.exponent\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#646695\\\"},\\\"scope\\\":\\\"constant.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"entity.name.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.name.tag.css\\\",\\\"entity.name.tag.less\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"entity.other.attribute-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.class.css\\\",\\\"source.css entity.other.attribute-name.class\\\",\\\"entity.other.attribute-name.id.css\\\",\\\"entity.other.attribute-name.parent-selector.css\\\",\\\"entity.other.attribute-name.parent.less\\\",\\\"source.css entity.other.attribute-name.pseudo-class\\\",\\\"entity.other.attribute-name.pseudo-element.css\\\",\\\"source.css.less entity.other.attribute-name.id\\\",\\\"entity.other.attribute-name.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#f44747\\\"},\\\"scope\\\":\\\"invalid\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":\\\"markup.underline\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.bold\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.heading\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.italic\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\"},\\\"scope\\\":\\\"markup.strikethrough\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"markup.inserted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.deleted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.changed\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"punctuation.definition.quote.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6796e6\\\"},\\\"scope\\\":\\\"punctuation.definition.list.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.inline.raw\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#808080\\\"},\\\"scope\\\":\\\"punctuation.definition.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"entity.name.function.preprocessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"meta.preprocessor.string\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"meta.preprocessor.numeric\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"meta.structure.dictionary.key.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"meta.diff.header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage.type\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"keyword.operator.noexcept\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":[\\\"string\\\",\\\"meta.embedded.assembly\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.value\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":\\\"string.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"meta.template.expression\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":[\\\"support.type.vendored.property-name\\\",\\\"support.type.property-name\\\",\\\"source.css variable\\\",\\\"source.coffee.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword.control\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":\\\"keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"keyword.operator.new\\\",\\\"keyword.operator.expression\\\",\\\"keyword.operator.cast\\\",\\\"keyword.operator.sizeof\\\",\\\"keyword.operator.alignof\\\",\\\"keyword.operator.typeid\\\",\\\"keyword.operator.alignas\\\",\\\"keyword.operator.instanceof\\\",\\\"keyword.operator.logical.python\\\",\\\"keyword.operator.wordlike\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"keyword.other.unit\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded.begin.php\\\",\\\"punctuation.section.embedded.end.php\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"support.function.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"constant.sha.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"storage.modifier.import.java\\\",\\\"variable.language.wildcard.java\\\",\\\"storage.modifier.package.java\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"variable.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"support.function\\\",\\\"support.constant.handlebars\\\",\\\"source.powershell variable.other.member\\\",\\\"entity.name.operator.custom-literal\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"support.class\\\",\\\"support.type\\\",\\\"entity.name.type\\\",\\\"entity.name.namespace\\\",\\\"entity.other.attribute\\\",\\\"entity.name.scope-resolution\\\",\\\"entity.name.class\\\",\\\"storage.type.numeric.go\\\",\\\"storage.type.byte.go\\\",\\\"storage.type.boolean.go\\\",\\\"storage.type.string.go\\\",\\\"storage.type.uintptr.go\\\",\\\"storage.type.error.go\\\",\\\"storage.type.rune.go\\\",\\\"storage.type.cs\\\",\\\"storage.type.generic.cs\\\",\\\"storage.type.modifier.cs\\\",\\\"storage.type.variable.cs\\\",\\\"storage.type.annotation.java\\\",\\\"storage.type.generic.java\\\",\\\"storage.type.java\\\",\\\"storage.type.object.array.java\\\",\\\"storage.type.primitive.array.java\\\",\\\"storage.type.primitive.java\\\",\\\"storage.type.token.java\\\",\\\"storage.type.groovy\\\",\\\"storage.type.annotation.groovy\\\",\\\"storage.type.parameters.groovy\\\",\\\"storage.type.generic.groovy\\\",\\\"storage.type.object.array.groovy\\\",\\\"storage.type.primitive.array.groovy\\\",\\\"storage.type.primitive.groovy\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"meta.type.cast.expr\\\",\\\"meta.type.new.expr\\\",\\\"support.constant.math\\\",\\\"support.constant.dom\\\",\\\"support.constant.json\\\",\\\"entity.other.inherited-class\\\",\\\"punctuation.separator.namespace.ruby\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C586C0\\\"},\\\"scope\\\":[\\\"keyword.control\\\",\\\"source.cpp keyword.operator.new\\\",\\\"keyword.operator.delete\\\",\\\"keyword.other.using\\\",\\\"keyword.other.directive.using\\\",\\\"keyword.other.operator\\\",\\\"entity.name.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"meta.definition.variable.name\\\",\\\"support.variable\\\",\\\"entity.name.variable\\\",\\\"constant.other.placeholder\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4FC1FF\\\"},\\\"scope\\\":[\\\"variable.other.constant\\\",\\\"variable.other.enummember\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"meta.object-literal.key\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\",\\\"punctuation.definition.group.assertion.regexp\\\",\\\"punctuation.definition.character-class.regexp\\\",\\\"punctuation.character.set.begin.regexp\\\",\\\"punctuation.character.set.end.regexp\\\",\\\"keyword.operator.negation.regexp\\\",\\\"support.other.parenthesis.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":[\\\"constant.character.character-class.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.other.character-class.regexp\\\",\\\"constant.character.set.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"keyword.operator.or.regexp\\\",\\\"keyword.control.anchor.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"constant.character\\\",\\\"constant.other.option\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"constant.character.escape\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C8C8C8\\\"},\\\"scope\\\":\\\"entity.name.label\\\"}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#c586c0\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#dcdcaa\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"vscode.theme-defaults\\\",\\\"_extensionIsBuiltin\\\":true,\\\"_extensionName\\\":\\\"theme-defaults\\\",\\\"_extensionPublisher\\\":\\\"vscode\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"checkbox.border\\\":\\\"#3c3c3c\\\",\\\"editor.background\\\":\\\"#1f1f1f\\\",\\\"editor.foreground\\\":\\\"#cccccc\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"editorIndentGuide.background1\\\":\\\"#404040\\\",\\\"editorIndentGuide.activeBackground1\\\":\\\"#707070\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#add6ff26\\\",\\\"list.dropBackground\\\":\\\"#383b3d\\\",\\\"activityBarBadge.background\\\":\\\"#0078d4\\\",\\\"sideBarTitle.foreground\\\":\\\"#cccccc\\\",\\\"input.placeholderForeground\\\":\\\"#989898\\\",\\\"menu.background\\\":\\\"#1f1f1f\\\",\\\"menu.foreground\\\":\\\"#cccccc\\\",\\\"menu.separatorBackground\\\":\\\"#454545\\\",\\\"menu.border\\\":\\\"#454545\\\",\\\"menu.selectionBackground\\\":\\\"#0078d4\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#ffffff\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#0078d4\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#369432\\\",\\\"sideBarSectionHeader.background\\\":\\\"#181818\\\",\\\"sideBarSectionHeader.border\\\":\\\"#2b2b2b\\\",\\\"tab.selectedBackground\\\":\\\"#222222\\\",\\\"tab.selectedForeground\\\":\\\"#ffffffa0\\\",\\\"tab.lastPinnedBorder\\\":\\\"#cccccc33\\\",\\\"list.activeSelectionIconForeground\\\":\\\"#ffffff\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"widget.border\\\":\\\"#313131\\\",\\\"actionBar.toggledBackground\\\":\\\"#383a49\\\",\\\"activityBar.activeBorder\\\":\\\"#0078d4\\\",\\\"activityBar.background\\\":\\\"#181818\\\",\\\"activityBar.border\\\":\\\"#2b2b2b\\\",\\\"activityBar.foreground\\\":\\\"#d7d7d7\\\",\\\"activityBar.inactiveForeground\\\":\\\"#868686\\\",\\\"activityBarBadge.foreground\\\":\\\"#ffffff\\\",\\\"badge.background\\\":\\\"#616161\\\",\\\"badge.foreground\\\":\\\"#f8f8f8\\\",\\\"button.background\\\":\\\"#0078d4\\\",\\\"button.border\\\":\\\"#ffffff12\\\",\\\"button.foreground\\\":\\\"#ffffff\\\",\\\"button.hoverBackground\\\":\\\"#026ec1\\\",\\\"button.secondaryBackground\\\":\\\"#313131\\\",\\\"button.secondaryForeground\\\":\\\"#cccccc\\\",\\\"button.secondaryHoverBackground\\\":\\\"#3c3c3c\\\",\\\"chat.slashCommandBackground\\\":\\\"#34414b\\\",\\\"chat.slashCommandForeground\\\":\\\"#40a6ff\\\",\\\"chat.editedFileForeground\\\":\\\"#e2c08d\\\",\\\"checkbox.background\\\":\\\"#313131\\\",\\\"debugToolBar.background\\\":\\\"#181818\\\",\\\"descriptionForeground\\\":\\\"#9d9d9d\\\",\\\"dropdown.background\\\":\\\"#313131\\\",\\\"dropdown.border\\\":\\\"#3c3c3c\\\",\\\"dropdown.foreground\\\":\\\"#cccccc\\\",\\\"dropdown.listBackground\\\":\\\"#1f1f1f\\\",\\\"editor.findMatchBackground\\\":\\\"#9e6a03\\\",\\\"editorGroup.border\\\":\\\"#ffffff17\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#181818\\\",\\\"editorGroupHeader.tabsBorder\\\":\\\"#2b2b2b\\\",\\\"editorGutter.addedBackground\\\":\\\"#2ea043\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f85149\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#0078d4\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#cccccc\\\",\\\"editorLineNumber.foreground\\\":\\\"#6e7681\\\",\\\"editorOverviewRuler.border\\\":\\\"#010409\\\",\\\"editorWidget.background\\\":\\\"#202020\\\",\\\"errorForeground\\\":\\\"#f85149\\\",\\\"focusBorder\\\":\\\"#0078d4\\\",\\\"foreground\\\":\\\"#cccccc\\\",\\\"icon.foreground\\\":\\\"#cccccc\\\",\\\"input.background\\\":\\\"#313131\\\",\\\"input.border\\\":\\\"#3c3c3c\\\",\\\"input.foreground\\\":\\\"#cccccc\\\",\\\"inputOption.activeBackground\\\":\\\"#2489db82\\\",\\\"inputOption.activeBorder\\\":\\\"#2488db\\\",\\\"keybindingLabel.foreground\\\":\\\"#cccccc\\\",\\\"notificationCenterHeader.background\\\":\\\"#1f1f1f\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#cccccc\\\",\\\"notifications.background\\\":\\\"#1f1f1f\\\",\\\"notifications.border\\\":\\\"#2b2b2b\\\",\\\"notifications.foreground\\\":\\\"#cccccc\\\",\\\"panel.background\\\":\\\"#181818\\\",\\\"panel.border\\\":\\\"#2b2b2b\\\",\\\"panelInput.border\\\":\\\"#2b2b2b\\\",\\\"panelTitle.activeBorder\\\":\\\"#0078d4\\\",\\\"panelTitle.activeForeground\\\":\\\"#cccccc\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"peekViewEditor.background\\\":\\\"#1f1f1f\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"peekViewResult.background\\\":\\\"#1f1f1f\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"pickerGroup.border\\\":\\\"#3c3c3c\\\",\\\"progressBar.background\\\":\\\"#0078d4\\\",\\\"quickInput.background\\\":\\\"#222222\\\",\\\"quickInput.foreground\\\":\\\"#cccccc\\\",\\\"settings.dropdownBackground\\\":\\\"#313131\\\",\\\"settings.dropdownBorder\\\":\\\"#3c3c3c\\\",\\\"settings.headerForeground\\\":\\\"#ffffff\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#bb800966\\\",\\\"sideBar.background\\\":\\\"#181818\\\",\\\"sideBar.border\\\":\\\"#2b2b2b\\\",\\\"sideBar.foreground\\\":\\\"#cccccc\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.background\\\":\\\"#181818\\\",\\\"statusBar.border\\\":\\\"#2b2b2b\\\",\\\"statusBar.debuggingBackground\\\":\\\"#0078d4\\\",\\\"statusBar.debuggingForeground\\\":\\\"#ffffff\\\",\\\"statusBar.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBar.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.noFolderBackground\\\":\\\"#1f1f1f\\\",\\\"statusBarItem.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#6e768166\\\",\\\"tab.activeBackground\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorder\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorderTop\\\":\\\"#0078d4\\\",\\\"tab.activeForeground\\\":\\\"#ffffff\\\",\\\"tab.selectedBorderTop\\\":\\\"#6caddf\\\",\\\"tab.border\\\":\\\"#2b2b2b\\\",\\\"tab.hoverBackground\\\":\\\"#1f1f1f\\\",\\\"tab.inactiveBackground\\\":\\\"#181818\\\",\\\"tab.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#1f1f1f\\\",\\\"tab.unfocusedActiveBorderTop\\\":\\\"#2b2b2b\\\",\\\"tab.unfocusedHoverBackground\\\":\\\"#1f1f1f\\\",\\\"terminal.foreground\\\":\\\"#cccccc\\\",\\\"terminal.tab.activeBorder\\\":\\\"#0078d4\\\",\\\"textBlockQuote.background\\\":\\\"#2b2b2b\\\",\\\"textBlockQuote.border\\\":\\\"#616161\\\",\\\"textCodeBlock.background\\\":\\\"#2b2b2b\\\",\\\"textLink.activeForeground\\\":\\\"#4daafc\\\",\\\"textLink.foreground\\\":\\\"#4daafc\\\",\\\"textPreformat.foreground\\\":\\\"#d0d0d0\\\",\\\"textPreformat.background\\\":\\\"#3c3c3c\\\",\\\"textSeparator.foreground\\\":\\\"#21262d\\\",\\\"titleBar.activeBackground\\\":\\\"#181818\\\",\\\"titleBar.activeForeground\\\":\\\"#cccccc\\\",\\\"titleBar.border\\\":\\\"#2b2b2b\\\",\\\"titleBar.inactiveBackground\\\":\\\"#1f1f1f\\\",\\\"titleBar.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"welcomePage.tileBackground\\\":\\\"#2b2b2b\\\",\\\"welcomePage.progress.foreground\\\":\\\"#0078d4\\\"},\\\"watch\\\":false}\",\"workbench.panel.alignment\":\"center\",\"windsurf_auth-Yevgeny Krasik\":\"[{\\\"id\\\":\\\"codeium.windsurf\\\",\\\"name\\\":\\\"Windsurf\\\",\\\"allowed\\\":true}]\",\"windsurfOnboarding\":\"true\",\"windsurfAuthStatus\":\"{\\\"name\\\":\\\"Yevgeny Krasik\\\",\\\"apiKey\\\":\\\"80a59e99-e508-49ab-a218-890121221964\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"teamId\\\":\\\"d5ca0846-2870-4414-899b-f70e338acc42\\\",\\\"planName\\\":\\\"Pro\\\",\\\"allowPremiumCommandModels\\\":true,\\\"allowedCommandModelConfigsProtoJsonString\\\":[\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"Windsurf Fast\\\\\\\",\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_12119\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0,\\\\\\\"disabled\\\\\\\":false,\\\\\\\"supportsImages\\\\\\\":false,\\\\\\\"supportsLegacy\\\\\\\":false,\\\\\\\"isPremium\\\\\\\":false,\\\\\\\"betaWarningMessage\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"isBeta\\\\\\\":false,\\\\\\\"provider\\\\\\\":\\\\\\\"MODEL_PROVIDER_UNSPECIFIED\\\\\\\",\\\\\\\"isRecommended\\\\\\\":false,\\\\\\\"allowedTiers\\\\\\\":[\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"TEAMS_TIER_PRO_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_TRIAL\\\\\\\",\\\\\\\"TEAMS_TIER_UNSPECIFIED\\\\\\\",\\\\\\\"TEAMS_TIER_WAITLIST_PRO\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_HOSTED\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_SERVE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SAAS\\\\\\\",\\\\\\\"TEAMS_TIER_HYBRID\\\\\\\"],\\\\\\\"pricingType\\\\\\\":\\\\\\\"MODEL_PRICING_TYPE_UNSPECIFIED\\\\\\\"}\\\",\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"GPT 4o Mini\\\\\\\",\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4O_MINI_2024_07_18\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0,\\\\\\\"disabled\\\\\\\":false,\\\\\\\"supportsImages\\\\\\\":false,\\\\\\\"supportsLegacy\\\\\\\":false,\\\\\\\"isPremium\\\\\\\":false,\\\\\\\"betaWarningMessage\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"isBeta\\\\\\\":false,\\\\\\\"provider\\\\\\\":\\\\\\\"MODEL_PROVIDER_UNSPECIFIED\\\\\\\",\\\\\\\"isRecommended\\\\\\\":false,\\\\\\\"allowedTiers\\\\\\\":[\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"TEAMS_TIER_PRO_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_TRIAL\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_HOSTED\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_SERVE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SAAS\\\\\\\",\\\\\\\"TEAMS_TIER_HYBRID\\\\\\\"],\\\\\\\"pricingType\\\\\\\":\\\\\\\"MODEL_PRICING_TYPE_UNSPECIFIED\\\\\\\"}\\\",\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"GPT 4o\\\\\\\",\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4O_2024_08_06\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0,\\\\\\\"disabled\\\\\\\":false,\\\\\\\"supportsImages\\\\\\\":false,\\\\\\\"supportsLegacy\\\\\\\":false,\\\\\\\"isPremium\\\\\\\":false,\\\\\\\"betaWarningMessage\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"isBeta\\\\\\\":false,\\\\\\\"provider\\\\\\\":\\\\\\\"MODEL_PROVIDER_UNSPECIFIED\\\\\\\",\\\\\\\"isRecommended\\\\\\\":false,\\\\\\\"allowedTiers\\\\\\\":[\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"TEAMS_TIER_PRO_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_TRIAL\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_HOSTED\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_SERVE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SAAS\\\\\\\",\\\\\\\"TEAMS_TIER_HYBRID\\\\\\\"],\\\\\\\"pricingType\\\\\\\":\\\\\\\"MODEL_PRICING_TYPE_UNSPECIFIED\\\\\\\"}\\\",\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"Claude 3.5 Sonnet\\\\\\\",\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_5_SONNET_20241022\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0,\\\\\\\"disabled\\\\\\\":false,\\\\\\\"supportsImages\\\\\\\":false,\\\\\\\"supportsLegacy\\\\\\\":false,\\\\\\\"isPremium\\\\\\\":false,\\\\\\\"betaWarningMessage\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"isBeta\\\\\\\":false,\\\\\\\"provider\\\\\\\":\\\\\\\"MODEL_PROVIDER_UNSPECIFIED\\\\\\\",\\\\\\\"isRecommended\\\\\\\":false,\\\\\\\"allowedTiers\\\\\\\":[\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"TEAMS_TIER_PRO_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_TRIAL\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_HOSTED\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_SERVE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SAAS\\\\\\\",\\\\\\\"TEAMS_TIER_HYBRID\\\\\\\"],\\\\\\\"pricingType\\\\\\\":\\\\\\\"MODEL_PRICING_TYPE_UNSPECIFIED\\\\\\\"}\\\",\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"Claude 3.7 Sonnet\\\\\\\",\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_7_SONNET_20250219\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0,\\\\\\\"disabled\\\\\\\":false,\\\\\\\"supportsImages\\\\\\\":false,\\\\\\\"supportsLegacy\\\\\\\":false,\\\\\\\"isPremium\\\\\\\":false,\\\\\\\"betaWarningMessage\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"isBeta\\\\\\\":false,\\\\\\\"provider\\\\\\\":\\\\\\\"MODEL_PROVIDER_UNSPECIFIED\\\\\\\",\\\\\\\"isRecommended\\\\\\\":false,\\\\\\\"allowedTiers\\\\\\\":[\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"TEAMS_TIER_PRO_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_TRIAL\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_HOSTED\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_SERVE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SAAS\\\\\\\",\\\\\\\"TEAMS_TIER_HYBRID\\\\\\\"],\\\\\\\"pricingType\\\\\\\":\\\\\\\"MODEL_PRICING_TYPE_UNSPECIFIED\\\\\\\"}\\\",\\\"{\\\\\\\"label\\\\\\\":\\\\\\\"GPT-4.1\\\\\\\",\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4_1_2025_04_14\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0,\\\\\\\"disabled\\\\\\\":false,\\\\\\\"supportsImages\\\\\\\":false,\\\\\\\"supportsLegacy\\\\\\\":false,\\\\\\\"isPremium\\\\\\\":false,\\\\\\\"betaWarningMessage\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"isBeta\\\\\\\":false,\\\\\\\"provider\\\\\\\":\\\\\\\"MODEL_PROVIDER_UNSPECIFIED\\\\\\\",\\\\\\\"isRecommended\\\\\\\":false,\\\\\\\"allowedTiers\\\\\\\":[\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"TEAMS_TIER_PRO_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_TRIAL\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS\\\\\\\",\\\\\\\"TEAMS_TIER_TEAMS_ULTIMATE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_HOSTED\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SELF_SERVE\\\\\\\",\\\\\\\"TEAMS_TIER_ENTERPRISE_SAAS\\\\\\\",\\\\\\\"TEAMS_TIER_HYBRID\\\\\\\"],\\\\\\\"pricingType\\\\\\\":\\\\\\\"MODEL_PRICING_TYPE_UNSPECIFIED\\\\\\\"}\\\"],\\\"hasAutocompleteFastMode\\\":true,\\\"adminEnabledWebSearch\\\":true,\\\"isTeams\\\":false,\\\"isEnterprise\\\":false,\\\"canCustomizeAppIcon\\\":true,\\\"cascadeCanAutoRunCommands\\\":true,\\\"hasTabToJump\\\":true,\\\"userStatusJson\\\":\\\"{\\\\\\\"name\\\\\\\":\\\\\\\"Yevgeny Krasik\\\\\\\",\\\\\\\"ignoreChatTelemetrySetting\\\\\\\":true,\\\\\\\"teamId\\\\\\\":\\\\\\\"d5ca0846-2870-4414-899b-f70e338acc42\\\\\\\",\\\\\\\"teamStatus\\\\\\\":\\\\\\\"USER_TEAM_STATUS_APPROVED\\\\\\\",\\\\\\\"email\\\\\\\":\\\\\\\"<EMAIL>\\\\\\\",\\\\\\\"permissions\\\\\\\":[\\\\\\\"PERMISSION_ATTRIBUTION_READ\\\\\\\",\\\\\\\"PERMISSION_ANALYTICS_READ\\\\\\\",\\\\\\\"PERMISSION_LICENSE_READ\\\\\\\",\\\\\\\"PERMISSION_TEAM_USER_READ\\\\\\\",\\\\\\\"PERMISSION_TEAM_USER_UPDATE\\\\\\\",\\\\\\\"PERMISSION_TEAM_USER_DELETE\\\\\\\",\\\\\\\"PERMISSION_INDEXING_READ\\\\\\\",\\\\\\\"PERMISSION_INDEXING_CREATE\\\\\\\",\\\\\\\"PERMISSION_INDEXING_UPDATE\\\\\\\",\\\\\\\"PERMISSION_INDEXING_DELETE\\\\\\\",\\\\\\\"PERMISSION_SSO_READ\\\\\\\",\\\\\\\"PERMISSION_SSO_WRITE\\\\\\\",\\\\\\\"PERMISSION_SERVICE_KEY_READ\\\\\\\",\\\\\\\"PERMISSION_SERVICE_KEY_CREATE\\\\\\\",\\\\\\\"PERMISSION_SERVICE_KEY_DELETE\\\\\\\",\\\\\\\"PERMISSION_BILLING_READ\\\\\\\",\\\\\\\"PERMISSION_TEAM_USER_INVITE\\\\\\\",\\\\\\\"PERMISSION_BILLING_WRITE\\\\\\\",\\\\\\\"PERMISSION_FINETUNING_READ\\\\\\\",\\\\\\\"PERMISSION_FINETUNING_CREATE\\\\\\\",\\\\\\\"PERMISSION_FINETUNING_UPDATE\\\\\\\",\\\\\\\"PERMISSION_FINETUNING_DELETE\\\\\\\",\\\\\\\"PERMISSION_ROLE_READ\\\\\\\",\\\\\\\"PERMISSION_ROLE_CREATE\\\\\\\",\\\\\\\"PERMISSION_ROLE_UPDATE\\\\\\\",\\\\\\\"PERMISSION_ROLE_DELETE\\\\\\\",\\\\\\\"PERMISSION_INDEXING_MANAGEMENT\\\\\\\",\\\\\\\"PERMISSION_SERVICE_KEY_UPDATE\\\\\\\",\\\\\\\"PERMISSION_EXTERNAL_CHAT_UPDATE\\\\\\\",\\\\\\\"PERMISSION_TEAM_SETTINGS_READ\\\\\\\",\\\\\\\"PERMISSION_TEAM_SETTINGS_UPDATE\\\\\\\"],\\\\\\\"planInfo\\\\\\\":{\\\\\\\"teamsTier\\\\\\\":\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"planName\\\\\\\":\\\\\\\"Pro\\\\\\\",\\\\\\\"hasAutocompleteFastMode\\\\\\\":true,\\\\\\\"allowStickyPremiumModels\\\\\\\":true,\\\\\\\"maxNumPremiumChatMessages\\\\\\\":\\\\\\\"-1\\\\\\\",\\\\\\\"maxNumChatInputTokens\\\\\\\":\\\\\\\"16384\\\\\\\",\\\\\\\"maxCustomChatInstructionCharacters\\\\\\\":\\\\\\\"600\\\\\\\",\\\\\\\"maxNumPinnedContextItems\\\\\\\":\\\\\\\"-1\\\\\\\",\\\\\\\"maxLocalIndexSize\\\\\\\":\\\\\\\"-1\\\\\\\",\\\\\\\"monthlyPromptCredits\\\\\\\":50000,\\\\\\\"monthlyFlowCredits\\\\\\\":150000,\\\\\\\"monthlyFlexCreditPurchaseAmount\\\\\\\":25000,\\\\\\\"allowPremiumCommandModels\\\\\\\":true,\\\\\\\"canBuyMoreCredits\\\\\\\":true,\\\\\\\"cascadeWebSearchEnabled\\\\\\\":true,\\\\\\\"canCustomizeAppIcon\\\\\\\":true,\\\\\\\"cascadeAllowedModelsConfig\\\\\\\":[{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_SWE_1\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20070\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20071\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20072\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_SWE_1_LITE\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20067\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20068\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20069\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_CASCADE_BASE\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_VISTA\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_SHAMU\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O3\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O3_HIGH\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4_1_2025_04_14\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O4_MINI\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O4_MINI_HIGH\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.5},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_PRO\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.75},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_5_HAIKU_20241022\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_5_SONNET_20241022\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_7_SONNET_20250219\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_7_SONNET_20250219_THINKING\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET_DATABRICKS\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET_THINKING\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET_THINKING_DATABRICKS\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_OPUS\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_OPUS_THINKING\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.10000000149011612},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.15000000596046448},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4O_2024_08_06\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4O_MINI_2024_07_18\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_DEEPSEEK_V3\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_DEEPSEEK_R1\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_XAI_GROK_3\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_XAI_GROK_3_MINI_REASONING\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.125},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O3_MINI\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_0_FLASH\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_04_17\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4_1_MINI_2025_04_14\\\\\\\"}}],\\\\\\\"cascadeCanAutoRunCommands\\\\\\\":true,\\\\\\\"hasTabToJump\\\\\\\":true,\\\\\\\"defaultTeamConfig\\\\\\\":{\\\\\\\"allowMcpServers\\\\\\\":true,\\\\\\\"allowAutoRunCommands\\\\\\\":true,\\\\\\\"allowCustomRecipes\\\\\\\":true,\\\\\\\"maxUnclaimedSites\\\\\\\":5,\\\\\\\"allowAppDeployments\\\\\\\":true,\\\\\\\"maxNewSitesPerDay\\\\\\\":5,\\\\\\\"allowSandboxAppDeployments\\\\\\\":true},\\\\\\\"canGenerateCommitMessages\\\\\\\":true,\\\\\\\"knowledgeBaseEnabled\\\\\\\":true,\\\\\\\"canAllowCascadeInBackground\\\\\\\":true,\\\\\\\"browserEnabled\\\\\\\":true},\\\\\\\"planStatus\\\\\\\":{\\\\\\\"planInfo\\\\\\\":{\\\\\\\"teamsTier\\\\\\\":\\\\\\\"TEAMS_TIER_PRO\\\\\\\",\\\\\\\"planName\\\\\\\":\\\\\\\"Pro\\\\\\\",\\\\\\\"hasAutocompleteFastMode\\\\\\\":true,\\\\\\\"allowStickyPremiumModels\\\\\\\":true,\\\\\\\"maxNumPremiumChatMessages\\\\\\\":\\\\\\\"-1\\\\\\\",\\\\\\\"maxNumChatInputTokens\\\\\\\":\\\\\\\"16384\\\\\\\",\\\\\\\"maxCustomChatInstructionCharacters\\\\\\\":\\\\\\\"600\\\\\\\",\\\\\\\"maxNumPinnedContextItems\\\\\\\":\\\\\\\"-1\\\\\\\",\\\\\\\"maxLocalIndexSize\\\\\\\":\\\\\\\"-1\\\\\\\",\\\\\\\"monthlyPromptCredits\\\\\\\":50000,\\\\\\\"monthlyFlowCredits\\\\\\\":150000,\\\\\\\"monthlyFlexCreditPurchaseAmount\\\\\\\":25000,\\\\\\\"allowPremiumCommandModels\\\\\\\":true,\\\\\\\"canBuyMoreCredits\\\\\\\":true,\\\\\\\"cascadeWebSearchEnabled\\\\\\\":true,\\\\\\\"canCustomizeAppIcon\\\\\\\":true,\\\\\\\"cascadeAllowedModelsConfig\\\\\\\":[{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_SWE_1\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20070\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20071\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20072\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_SWE_1_LITE\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20067\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20068\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CASCADE_20069\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_CASCADE_BASE\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_VISTA\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"alias\\\\\\\":\\\\\\\"MODEL_ALIAS_SHAMU\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O3\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O3_HIGH\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4_1_2025_04_14\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O4_MINI\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O4_MINI_HIGH\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.5},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_PRO\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.75},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_5_HAIKU_20241022\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_5_SONNET_20241022\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_7_SONNET_20250219\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_3_7_SONNET_20250219_THINKING\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET_DATABRICKS\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET_THINKING\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_SONNET_THINKING_DATABRICKS\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_OPUS\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CLAUDE_4_OPUS_THINKING\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.10000000149011612},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.15000000596046448},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4O_2024_08_06\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4O_MINI_2024_07_18\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_DEEPSEEK_V3\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_DEEPSEEK_R1\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_XAI_GROK_3\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_XAI_GROK_3_MINI_REASONING\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.125},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_O3_MINI\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":1},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_0_FLASH\\\\\\\"},\\\\\\\"creditMultiplier\\\\\\\":0.25},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_04_17\\\\\\\"}},{\\\\\\\"modelOrAlias\\\\\\\":{\\\\\\\"model\\\\\\\":\\\\\\\"MODEL_CHAT_GPT_4_1_MINI_2025_04_14\\\\\\\"}}],\\\\\\\"cascadeCanAutoRunCommands\\\\\\\":true,\\\\\\\"hasTabToJump\\\\\\\":true,\\\\\\\"defaultTeamConfig\\\\\\\":{\\\\\\\"allowMcpServers\\\\\\\":true,\\\\\\\"allowAutoRunCommands\\\\\\\":true,\\\\\\\"allowCustomRecipes\\\\\\\":true,\\\\\\\"maxUnclaimedSites\\\\\\\":5,\\\\\\\"allowAppDeployments\\\\\\\":true,\\\\\\\"maxNewSitesPerDay\\\\\\\":5,\\\\\\\"allowSandboxAppDeployments\\\\\\\":true},\\\\\\\"canGenerateCommitMessages\\\\\\\":true,\\\\\\\"knowledgeBaseEnabled\\\\\\\":true,\\\\\\\"canAllowCascadeInBackground\\\\\\\":true,\\\\\\\"browserEnabled\\\\\\\":true},\\\\\\\"planStart\\\\\\\":\\\\\\\"2025-06-15T15:17:48Z\\\\\\\",\\\\\\\"planEnd\\\\\\\":\\\\\\\"2025-07-15T15:17:48Z\\\\\\\",\\\\\\\"usedPromptCredits\\\\\\\":17025,\\\\\\\"availablePromptCredits\\\\\\\":50000,\\\\\\\"availableFlowCredits\\\\\\\":150000,\\\\\\\"topUpStatus\\\\\\\":{\\\\\\\"topUpTransactionStatus\\\\\\\":\\\\\\\"TRANSACTION_STATUS_NO_ACTIVE\\\\\\\"}},\\\\\\\"userUsedPromptCredits\\\\\\\":\\\\\\\"17025\\\\\\\",\\\\\\\"hasFingerprintSet\\\\\\\":true,\\\\\\\"hasUsedWindsurf\\\\\\\":true,\\\\\\\"teamConfig\\\\\\\":{\\\\\\\"allowMcpServers\\\\\\\":true,\\\\\\\"allowAutoRunCommands\\\\\\\":true,\\\\\\\"allowCustomRecipes\\\\\\\":true,\\\\\\\"maxUnclaimedSites\\\\\\\":5,\\\\\\\"allowAppDeployments\\\\\\\":true,\\\\\\\"maxNewSitesPerDay\\\\\\\":5,\\\\\\\"allowSandboxAppDeployments\\\\\\\":true}}\\\",\\\"browserEnabled\\\":true}\",\"workbench.view.extension.project-manager.state.hidden\":\"[{\\\"id\\\":\\\"projectsExplorerFavorites\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerGit\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerSVN\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerAny\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerMercurial\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerVSCode\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectManagerHelpAndFeedback\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.continue.state.hidden\":\"[{\\\"id\\\":\\\"continue.continueGUIView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-cweijan-mysql.state.hidden\":\"[{\\\"id\\\":\\\"github.cweijan.mysql\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-cweijan-nosql.state.hidden\":\"[{\\\"id\\\":\\\"github.cweijan.nosql\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.package-explorer.state.hidden\":\"[{\\\"id\\\":\\\"workspaceEnvironments\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pythonEnvironments\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlens.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensInspect.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-actions.state.hidden\":\"[{\\\"id\\\":\\\"github-actions.current-branch\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github-actions.workflows\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github-actions.settings\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github-actions.empty-view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-requests.state.hidden\":\"[{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"notifications:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:conflictResolution\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dockerView.state.hidden\":\"[{\\\"id\\\":\\\"dockerContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerImages\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerRegistries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerNetworks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.kubernetesView.state.hidden\":\"[{\\\"id\\\":\\\"extension.vsKubernetesExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extension.vsKubernetesHelmRepoExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"kubernetes.cloudExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.makefile__viewContainer.state.hidden\":\"[{\\\"id\\\":\\\"makefile.outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"windsurfDevContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"windsurfSSHHosts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteHub.views.workspaceRepositories\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"windsurfWslTargets\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sqltoolsActivityBarContainer.state.hidden\":\"[{\\\"id\\\":\\\"sqltoolsViewConnectionExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sqltoolsViewBookmarksExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sqltoolsViewHistoryExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sqltoolsPanelContainer.state.hidden\":\"[{\\\"id\\\":\\\"sqltoolsViewConsoleMessages\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.thunder-client.state.hidden\":\"[{\\\"id\\\":\\\"thunder-client-sidebar\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.claude-dev-ActivityBar.state.hidden\":\"[{\\\"id\\\":\\\"claude-dev.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.view.testCoverage\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"test-explorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensPanel.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graphDetails\\\",\\\"isHidden\\\":false}]\",\"github-ykrasik\":\"[{\\\"id\\\":\\\"github.vscode-github-actions\\\",\\\"name\\\":\\\"GitHub Actions\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"continue.continue\\\",\\\"name\\\":\\\"Continue - Codestral, Claude, and more\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"ms-vscode.remote-server\\\",\\\"name\\\":\\\"Remote - Tunnels\\\",\\\"allowed\\\":true}]\",\"workbench.auxiliaryactivity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"windsurf.cascadeViewContainerId\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.extension.geminiChat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":22},{\\\"id\\\":\\\"workbench.view.extension.sidebarDevToolsInspectorContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.view.extension.flutter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.windsurf\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.claude-dev-ActivityBar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.aider-composer-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.panel.chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.panel.chatEditing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":101}]\",\"workbench.auxiliaryActivityBar.location\":\"default\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.local.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.remote.installed\\\",\\\"isHidden\\\":false}]\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"alefragnani.project-manager#projectManagerWelcome\\\",{\\\"firstSeen\\\":1731531145688,\\\"stepIDs\\\":[\\\"saveYourFavoriteProjects\\\",\\\"autoDetectGitRepositories\\\",\\\"findAndOpenProjects\\\",\\\"organizeWithTags\\\",\\\"exclusiveSideBar\\\",\\\"workingWithRemotes\\\"],\\\"manaullyOpened\\\":false}],[\\\"eamodio.gitlens#welcome\\\",{\\\"firstSeen\\\":1731531145688,\\\"stepIDs\\\":[\\\"get-started\\\",\\\"core-features\\\",\\\"pro-features\\\",\\\"pro-trial\\\",\\\"pro-upgrade\\\",\\\"pro-reactivate\\\",\\\"pro-paid\\\",\\\"visualize\\\",\\\"launchpad\\\",\\\"code-collab\\\",\\\"integrations\\\",\\\"more\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-docker#dockerStart\\\",{\\\"firstSeen\\\":1731531145688,\\\"stepIDs\\\":[\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"dockerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1731531145688,\\\"stepIDs\\\":[\\\"python.createPythonFolder\\\",\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1731531145688,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.jupyter#jupyterWelcome\\\",{\\\"firstSeen\\\":1731531145688,\\\"stepIDs\\\":[\\\"ipynb.newUntitledIpynb\\\",\\\"jupyter.selectKernel\\\",\\\"jupyter.exploreAndDebug\\\",\\\"jupyter.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.remote-repositories#remoteRepositoriesWalkthrough\\\",{\\\"firstSeen\\\":1731531145688,\\\"stepIDs\\\":[\\\"editCommitRepo\\\",\\\"createGitHubPullRequest\\\",\\\"continueOn\\\",\\\"openRepo\\\",\\\"remoteIndicator\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-containers#containersStart\\\",{\\\"firstSeen\\\":1750365124150,\\\"stepIDs\\\":[\\\"chooseContainerRuntime\\\",\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"containerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}],[\\\"rust-lang.rust-analyzer#landing\\\",{\\\"firstSeen\\\":1750365124150,\\\"stepIDs\\\":[\\\"setup\\\",\\\"docs\\\",\\\"faq\\\",\\\"changelog\\\"],\\\"manaullyOpened\\\":false}],[\\\"vscjava.vscode-java-pack#javaWelcome\\\",{\\\"firstSeen\\\":1750365124150,\\\"stepIDs\\\":[\\\"java.runtime\\\",\\\"java.showProjectExplorer\\\",\\\"java.showProjectExplorer.inactive\\\",\\\"java.codeActions\\\",\\\"java.debugAndTest\\\",\\\"java.extensions\\\",\\\"java.more\\\",\\\"java.moreForMac\\\"],\\\"manaullyOpened\\\":false}]]\",\"memento/gettingStartedService\":\"{\\\"ms-azuretools.vscode-docker#dockerStart#dockerExplorer\\\":{\\\"done\\\":true},\\\"eamodio.gitlens#welcome#more\\\":{\\\"done\\\":true},\\\"alefragnani.project-manager#projectManagerWelcome#autoDetectGitRepositories\\\":{\\\"done\\\":true},\\\"ms-python.python#pythonWelcome#python.createPythonFile\\\":{\\\"done\\\":true},\\\"intellisense\\\":{\\\"done\\\":true},\\\"quickOpen\\\":{\\\"done\\\":true},\\\"quickOpenWeb\\\":{\\\"done\\\":true},\\\"vscjava.vscode-java-pack#javaWelcome#java.showProjectExplorer.inactive\\\":{\\\"done\\\":true},\\\"commandPaletteTask\\\":{\\\"done\\\":true},\\\"commandPaletteTaskWeb\\\":{\\\"done\\\":true},\\\"commandPaletteTaskAccessibility\\\":{\\\"done\\\":true},\\\"codeFolding\\\":{\\\"done\\\":true}}\",\"memento/workbench.editor.keybindings\":\"{\\\"searchHistory\\\":[\\\"\\\\\\\"ctrl+shift+z\\\\\\\"\\\",\\\"\\\\\\\"ctrl+y\\\\\\\"\\\",\\\"\\\\\\\"ctrl+n\\\\\\\"\\\",\\\"\\\\\\\"ctrl+t\\\\\\\"\\\",\\\"\\\\\\\"ctrl+d\\\\\\\"\\\",\\\"delete lin\\\",\\\"delete line\\\",\\\"f2\\\",\\\"rename\\\",\\\"ctrl+m\\\",\\\"go to symbol\\\",\\\"ctrl+r\\\",\\\"ctrl+right\\\",\\\"git:open\\\",\\\"git:opencha\\\",\\\"git:openchang\\\",\\\"git:openchanges\\\",\\\"git: openchanges\\\",\\\"git: open changes\\\",\\\"explorer.open\\\",\\\"f4\\\",\\\"open file\\\",\\\"ctrl+\\\",\\\"ctrl+y\\\",\\\"new file\\\",\\\"duplicate\\\",\\\"ctrl+bac\\\",\\\"ctrl+backspace\\\",\\\"clear\\\",\\\"clear \\\",\\\"clear te\\\",\\\"clear ter\\\",\\\"ctrl+l\\\"]}\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"workbench.action.openSettingsJson\\\",\\\"value\\\":79},{\\\"key\\\":\\\"workbench.action.openRemoteSettings\\\",\\\"value\\\":80},{\\\"key\\\":\\\"windsurf-dev-containers.reopenInContainer\\\",\\\"value\\\":84},{\\\"key\\\":\\\"windsurf-dev-containers.reopenFolderLocally\\\",\\\"value\\\":85},{\\\"key\\\":\\\"workbench.action.reloadWindow\\\",\\\"value\\\":86}]}\",\"commandPalette.mru.counter\":\"87\",\"windsurf.profileUrl\":\"data:image/png;base64,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\",\"terminal.integrated.showTerminalConfigPrompt\":\"false\",\"workbench.view.extension.flutter.state.hidden\":\"[{\\\"id\\\":\\\"dartFlutterSidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dartFlutterOutline\\\",\\\"isHidden\\\":false}]\",\"expandSuggestionDocs\":\"true\",\"tabs-list-width-horizontal\":\"212\",\"workbench.view.extension.cspell-info-explorer.state.hidden\":\"[{\\\"id\\\":\\\"cSpellInfoView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cSpellRegExpView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cspellPanel.state.hidden\":\"[{\\\"id\\\":\\\"cSpellIssuesViewByFile\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cSpellIssuesViewByIssue\\\",\\\"isHidden\\\":false}]\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{\\\"workbench.view.extension.claude-dev-ActivityBar\\\":2,\\\"workbench.view.extension.aider-composer-activitybar\\\":2,\\\"workbench.view.extension.sidebarDevToolsInspectorContainer\\\":2,\\\"workbench.view.extension.flutter\\\":2,\\\"workbench.view.extension.geminiChat\\\":2},\\\"viewLocations\\\":{},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"file.particpants.additionalEdits\":\"false\",\"windsurf_editor_command_model_preferred_id\":\"MODEL_CLAUDE_3_7_SONNET_20250219\",\"windsurf.hasInstalledWindsurfPyright\":\"true\",\"workbench.view.extension.aider-composer-activitybar.state.hidden\":\"[{\\\"id\\\":\\\"aider-composer.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dbcodeActivitybarContainer.state.hidden\":\"[{\\\"id\\\":\\\"dbcode.connections.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.tunnels.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.history.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.account.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.help.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.favorites.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dbcodePanelContainer.state.hidden\":\"[{\\\"id\\\":\\\"dbcode.panelView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.references-view.state.hidden\":\"[{\\\"id\\\":\\\"references-view.tree\\\",\\\"isHidden\\\":false}]\",\"windsurfConfigurations\":\"{\\\"autocompleteSpeed\\\":\\\"AUTOCOMPLETE_SPEED_DEFAULT\\\",\\\"lastSelectedCascadeModel\\\":\\\"MODEL_GOOGLE_GEMINI_2_5_PRO\\\",\\\"cascadeNuxStates\\\":[{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_DIFF_OVERVIEW\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_TOOL_CALL\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_WEB_SEARCH\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_MODEL_SELECTOR_NUX\\\"},{\\\"event\\\":\\\"CASCADE_NUX_EVENT_PLAN_MODE\\\"}],\\\"cascadePlannerMode\\\":\\\"CONVERSATIONAL_PLANNER_MODE_DEFAULT\\\",\\\"cascadeAllowedCommands\\\":[\\\"python\\\",\\\"uv\\\",\\\"d:\\\\\\\\dev\\\\\\\\story_time\\\\\\\\.venv\\\\\\\\Scripts\\\\\\\\python.exe\\\"],\\\"cascadeAutoExecutionPolicy\\\":\\\"CASCADE_COMMANDS_AUTO_EXECUTION_AUTO\\\",\\\"lastSelectedCascadeId\\\":\\\"22e07417-ca21-4f09-95d7-c69e1b997a99\\\",\\\"explainAndFixInCurrentConversation\\\":true,\\\"allowCascadeAccessGitignoreFiles\\\":true,\\\"useClipboardForCompletions\\\":true,\\\"lastModelOverride\\\":\\\"MODEL_CHAT_GPT_4_1_2025_04_14\\\",\\\"featureUsageData\\\":{\\\"CASCADE_CLICK_MODEL_SELECTOR\\\":{\\\"hasUsed\\\":true},\\\"CASCADE_REVERT_TO_STEP\\\":{\\\"hasUsed\\\":true}},\\\"conversationBrainConfigs\\\":{\\\"1e8e0f41-8d04-426a-b8cd-3f45baa5dd2d\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"54d5fb71-aa76-434f-9dc2-b77e577d899c\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"1905e9f2-6de2-4dff-a019-6317ebeefc3f\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"3e279ef0-e9e1-4301-823c-fd5ef9ebbc97\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"e0c777c3-d5e6-4641-80ec-0c737175482f\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"806d5bd1-22df-4fad-92df-3b7859ac554d\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"5d007935-0c27-498c-ba8b-f33270686844\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"37781fad-dd22-47ce-857b-fd509ebca564\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"cc047496-e027-4d20-a5b8-56a58af9eff0\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"d4a8bfaa-6367-4889-8ff0-14001638ec31\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"41a2e09d-3936-4a08-ab10-608620c5059b\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"6564423b-9e22-4832-93d8-12f5211f9dd2\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"ef8531a8-62ed-493f-bfed-e169be02dd77\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"4f5f0cf6-efd1-4fa2-9d02-3d3dda0ae107\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"7e65c646-31fd-452e-9e57-53cd67fc1269\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"1aa126c5-ddf8-415a-9739-6d12b813b3e8\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"cb6cb7c3-4d8c-4f05-9015-37db2ddd5fe0\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"138df21d-0d35-4616-9adf-83b4ab183b69\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"84748a73-4fbd-4410-a50b-0cdc9c55b876\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"5b1fdc27-c70b-444c-81e5-59e949410f1e\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"c0715c2f-6f2c-488d-9db1-f7369a6c051c\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"6973d39c-7173-4617-a946-0090a1be2c71\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"26639fae-b67d-47cf-87f0-4be9411859cc\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"a708716f-07ae-4415-8904-88dda12fd017\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"e2cce42f-6c6f-4115-acd6-6fc0aa77f0e7\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"},\\\"60ed980c-dd8c-4bdd-88cd-83bec1bb8571\\\":{\\\"planMode\\\":\\\"PLAN_MODE_OFF\\\"}},\\\"cachedCascadeModelConfigs\\\":[{\\\"label\\\":\\\"SWE-1 (free limited time)\\\",\\\"modelOrAlias\\\":{\\\"alias\\\":\\\"MODEL_ALIAS_SWE_1\\\"},\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_WINDSURF\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"SWE-1-lite\\\",\\\"modelOrAlias\\\":{\\\"alias\\\":\\\"MODEL_ALIAS_SWE_1_LITE\\\"},\\\"supportsLegacy\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_WINDSURF\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"o3\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CHAT_O3\\\"},\\\"creditMultiplier\\\":1,\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_OPENAI\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"o3 (high reasoning)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CHAT_O3_HIGH\\\"},\\\"creditMultiplier\\\":1,\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_OPENAI\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"GPT-4.1 (promo)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CHAT_GPT_4_1_2025_04_14\\\"},\\\"creditMultiplier\\\":0.25,\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_OPENAI\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"o4-mini (medium reasoning) (promo)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CHAT_O4_MINI\\\"},\\\"creditMultiplier\\\":0.25,\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_OPENAI\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"o4-mini (high reasoning) (promo)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CHAT_O4_MINI_HIGH\\\"},\\\"creditMultiplier\\\":0.5,\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_OPENAI\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"Gemini 2.5 Pro (promo)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_GOOGLE_GEMINI_2_5_PRO\\\"},\\\"creditMultiplier\\\":0.75,\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_GOOGLE\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"Claude 3.5 Sonnet\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_3_5_SONNET_20241022\\\"},\\\"creditMultiplier\\\":1,\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"Claude 3.7 Sonnet\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_3_7_SONNET_20250219\\\"},\\\"creditMultiplier\\\":1,\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"Claude 3.7 Sonnet (Thinking)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_3_7_SONNET_20250219_THINKING\\\"},\\\"creditMultiplier\\\":1.25,\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"Claude Sonnet 4 (BYOK)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_4_SONNET\\\"},\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"Register your Anthropic API key in Windsurf settings.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_BYOK\\\"},{\\\"label\\\":\\\"Claude Sonnet 4\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_4_SONNET_DATABRICKS\\\"},\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"May be subject to rate limits or slow generation.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_API\\\"},{\\\"label\\\":\\\"Claude Sonnet 4 (Thinking, BYOK)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_4_SONNET_THINKING\\\"},\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"Register your Anthropic API key in Windsurf settings.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_BYOK\\\"},{\\\"label\\\":\\\"Claude Sonnet 4 (Thinking)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_4_SONNET_THINKING_DATABRICKS\\\"},\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"May be subject to rate limits or slow generation.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"isRecommended\\\":true,\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_API\\\"},{\\\"label\\\":\\\"Claude Opus 4 (BYOK)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_4_OPUS\\\"},\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"Register your Anthropic API key in Windsurf settings.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_BYOK\\\"},{\\\"label\\\":\\\"Claude Opus 4 (Thinking, BYOK)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CLAUDE_4_OPUS_THINKING\\\"},\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"Register your Anthropic API key in Windsurf settings.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_ANTHROPIC\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_BYOK\\\"},{\\\"label\\\":\\\"Gemini 2.5 Flash\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20\\\"},\\\"creditMultiplier\\\":0.10000000149011612,\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"May be subject to rate limits or slow generation.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_GOOGLE\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"Gemini 2.5 Flash (Thinking)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_GOOGLE_GEMINI_2_5_FLASH_PREVIEW_05_20_THINKING\\\"},\\\"creditMultiplier\\\":0.15000000596046448,\\\"supportsImages\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"May be subject to rate limits or slow generation.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_GOOGLE\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"GPT-4o\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CHAT_GPT_4O_2024_08_06\\\"},\\\"creditMultiplier\\\":1,\\\"supportsImages\\\":true,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_OPENAI\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"DeepSeek V3 (0324)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_DEEPSEEK_V3\\\"},\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_DEEPSEEK\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"DeepSeek R1 (0528)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_DEEPSEEK_R1\\\"},\\\"isPremium\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_DEEPSEEK\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"xAI Grok-3\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_XAI_GROK_3\\\"},\\\"creditMultiplier\\\":1,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"May be subject to rate limits or slow generation.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_XAI\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"xAI Grok-3 mini (Thinking)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_XAI_GROK_3_MINI_REASONING\\\"},\\\"creditMultiplier\\\":0.125,\\\"supportsLegacy\\\":true,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"May be subject to rate limits or slow generation.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_XAI\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"},{\\\"label\\\":\\\"o3-mini (medium reasoning)\\\",\\\"modelOrAlias\\\":{\\\"model\\\":\\\"MODEL_CHAT_O3_MINI\\\"},\\\"creditMultiplier\\\":1,\\\"isPremium\\\":true,\\\"betaWarningMessage\\\":\\\"May be subject to rate limits or slow generation.\\\",\\\"isBeta\\\":true,\\\"provider\\\":\\\"MODEL_PROVIDER_OPENAI\\\",\\\"pricingType\\\":\\\"MODEL_PRICING_TYPE_STATIC_CREDIT\\\"}],\\\"cachedCascadeModelSorts\\\":[{\\\"name\\\":\\\"Recommended\\\",\\\"groups\\\":[{\\\"modelLabels\\\":[\\\"SWE-1 (free limited time)\\\",\\\"SWE-1-lite\\\",\\\"o3\\\",\\\"GPT-4.1 (promo)\\\",\\\"Gemini 2.5 Pro (promo)\\\",\\\"Claude 3.7 Sonnet\\\",\\\"Claude 3.7 Sonnet (Thinking)\\\",\\\"Claude Sonnet 4 (BYOK)\\\",\\\"Claude Sonnet 4\\\",\\\"Claude Sonnet 4 (Thinking, BYOK)\\\",\\\"Claude Sonnet 4 (Thinking)\\\"]}]},{\\\"name\\\":\\\"Provider\\\",\\\"groups\\\":[{\\\"groupName\\\":\\\"Windsurf\\\",\\\"modelLabels\\\":[\\\"SWE-1 (free limited time)\\\",\\\"SWE-1-lite\\\"]},{\\\"groupName\\\":\\\"OpenAI\\\",\\\"modelLabels\\\":[\\\"o3\\\",\\\"o3 (high reasoning)\\\",\\\"GPT-4.1 (promo)\\\",\\\"o4-mini (medium reasoning) (promo)\\\",\\\"o4-mini (high reasoning) (promo)\\\",\\\"GPT-4o\\\",\\\"o3-mini (medium reasoning)\\\"]},{\\\"groupName\\\":\\\"Anthropic\\\",\\\"modelLabels\\\":[\\\"Claude 3.5 Sonnet\\\",\\\"Claude 3.7 Sonnet\\\",\\\"Claude 3.7 Sonnet (Thinking)\\\",\\\"Claude Sonnet 4 (BYOK)\\\",\\\"Claude Sonnet 4\\\",\\\"Claude Sonnet 4 (Thinking, BYOK)\\\",\\\"Claude Sonnet 4 (Thinking)\\\",\\\"Claude Opus 4 (BYOK)\\\",\\\"Claude Opus 4 (Thinking, BYOK)\\\"]},{\\\"groupName\\\":\\\"Google\\\",\\\"modelLabels\\\":[\\\"Gemini 2.5 Pro (promo)\\\",\\\"Gemini 2.5 Flash\\\",\\\"Gemini 2.5 Flash (Thinking)\\\"]},{\\\"groupName\\\":\\\"xAI\\\",\\\"modelLabels\\\":[\\\"xAI Grok-3\\\",\\\"xAI Grok-3 mini (Thinking)\\\"]},{\\\"groupName\\\":\\\"DeepSeek\\\",\\\"modelLabels\\\":[\\\"DeepSeek V3 (0324)\\\",\\\"DeepSeek R1 (0528)\\\"]}]},{\\\"name\\\":\\\"Cost\\\",\\\"groups\\\":[{\\\"groupName\\\":\\\"Premium\\\",\\\"modelLabels\\\":[\\\"o3\\\",\\\"o3 (high reasoning)\\\",\\\"Claude 3.5 Sonnet\\\",\\\"Claude 3.7 Sonnet\\\",\\\"Claude 3.7 Sonnet (Thinking)\\\",\\\"GPT-4o\\\",\\\"xAI Grok-3\\\",\\\"o3-mini (medium reasoning)\\\"]},{\\\"groupName\\\":\\\"Value\\\",\\\"modelLabels\\\":[\\\"GPT-4.1 (promo)\\\",\\\"o4-mini (medium reasoning) (promo)\\\",\\\"o4-mini (high reasoning) (promo)\\\",\\\"Gemini 2.5 Pro (promo)\\\",\\\"Gemini 2.5 Flash\\\",\\\"Gemini 2.5 Flash (Thinking)\\\",\\\"xAI Grok-3 mini (Thinking)\\\"]},{\\\"groupName\\\":\\\"BYOK\\\",\\\"modelLabels\\\":[\\\"Claude Sonnet 4 (BYOK)\\\",\\\"Claude Sonnet 4 (Thinking, BYOK)\\\",\\\"Claude Opus 4 (BYOK)\\\",\\\"Claude Opus 4 (Thinking, BYOK)\\\"]},{\\\"groupName\\\":\\\"API Pricing\\\",\\\"modelLabels\\\":[\\\"Claude Sonnet 4\\\",\\\"Claude Sonnet 4 (Thinking)\\\"]},{\\\"groupName\\\":\\\"Free\\\",\\\"modelLabels\\\":[\\\"SWE-1 (free limited time)\\\",\\\"SWE-1-lite\\\",\\\"DeepSeek V3 (0324)\\\",\\\"DeepSeek R1 (0528)\\\"]}]}],\\\"globalPlanModePreference\\\":\\\"PLAN_MODE_ON\\\"}\",\"windsurf.configuration.oneTimeSync\":\"true\",\"windsurf.cascadeViewContainerId.state.hidden\":\"[{\\\"id\\\":\\\"windsurf.cascadePanel\\\",\\\"isHidden\\\":false}]\",\"extensions.trustedPublishers\":\"{\\\"aaaaronzhou\\\":{\\\"publisher\\\":\\\"aaaaronzhou\\\",\\\"publisherDisplayName\\\":\\\"Aaaaron Zhou\\\"},\\\"adpyke\\\":{\\\"publisher\\\":\\\"adpyke\\\",\\\"publisherDisplayName\\\":\\\"adpyke\\\"},\\\"ahmadalli\\\":{\\\"publisher\\\":\\\"ahmadalli\\\",\\\"publisherDisplayName\\\":\\\"ahmadalli\\\"},\\\"alefragnani\\\":{\\\"publisher\\\":\\\"alefragnani\\\",\\\"publisherDisplayName\\\":\\\"Alessandro Fragnani\\\"},\\\"arturock\\\":{\\\"publisher\\\":\\\"arturock\\\",\\\"publisherDisplayName\\\":\\\"arturock\\\"},\\\"charliermarsh\\\":{\\\"publisher\\\":\\\"charliermarsh\\\",\\\"publisherDisplayName\\\":\\\"charliermarsh\\\"},\\\"christian-kohler\\\":{\\\"publisher\\\":\\\"christian-kohler\\\",\\\"publisherDisplayName\\\":\\\"Christian Kohler\\\"},\\\"codeium\\\":{\\\"publisher\\\":\\\"codeium\\\",\\\"publisherDisplayName\\\":\\\"Codeium\\\"},\\\"codezombiech\\\":{\\\"publisher\\\":\\\"codezombiech\\\",\\\"publisherDisplayName\\\":\\\"CodeZombie\\\"},\\\"dart-code\\\":{\\\"publisher\\\":\\\"dart-code\\\",\\\"publisherDisplayName\\\":\\\"Dart-Code\\\"},\\\"dbaeumer\\\":{\\\"publisher\\\":\\\"dbaeumer\\\",\\\"publisherDisplayName\\\":\\\"Microsoft\\\"},\\\"dbcode\\\":{\\\"publisher\\\":\\\"dbcode\\\",\\\"publisherDisplayName\\\":\\\"dbcode\\\"},\\\"donjayamanne\\\":{\\\"publisher\\\":\\\"donjayamanne\\\",\\\"publisherDisplayName\\\":\\\"Don Jayamanne\\\"},\\\"eamodio\\\":{\\\"publisher\\\":\\\"eamodio\\\",\\\"publisherDisplayName\\\":\\\"GitKraken\\\"},\\\"emilast\\\":{\\\"publisher\\\":\\\"emilast\\\",\\\"publisherDisplayName\\\":\\\"Emil Åström\\\"},\\\"esbenp\\\":{\\\"publisher\\\":\\\"esbenp\\\",\\\"publisherDisplayName\\\":\\\"Prettier\\\"},\\\"esphome\\\":{\\\"publisher\\\":\\\"esphome\\\",\\\"publisherDisplayName\\\":\\\"ESPHome\\\"},\\\"flutterando\\\":{\\\"publisher\\\":\\\"flutterando\\\",\\\"publisherDisplayName\\\":\\\"Flutterando\\\"},\\\"foxundermoon\\\":{\\\"publisher\\\":\\\"foxundermoon\\\",\\\"publisherDisplayName\\\":\\\"foxundermoon\\\"},\\\"github\\\":{\\\"publisher\\\":\\\"github\\\",\\\"publisherDisplayName\\\":\\\"GitHub\\\"},\\\"gitworktrees\\\":{\\\"publisher\\\":\\\"gitworktrees\\\",\\\"publisherDisplayName\\\":\\\"Git Worktrees\\\"},\\\"graphql\\\":{\\\"publisher\\\":\\\"graphql\\\",\\\"publisherDisplayName\\\":\\\"GraphQL Foundation\\\"},\\\"hbenl\\\":{\\\"publisher\\\":\\\"hbenl\\\",\\\"publisherDisplayName\\\":\\\"Holger Benl\\\"},\\\"hediet\\\":{\\\"publisher\\\":\\\"hediet\\\",\\\"publisherDisplayName\\\":\\\"Henning Dieterichs\\\"},\\\"jeroen-meijer\\\":{\\\"publisher\\\":\\\"jeroen-meijer\\\",\\\"publisherDisplayName\\\":\\\"jeroen-meijer\\\"},\\\"k--kato\\\":{\\\"publisher\\\":\\\"k--kato\\\",\\\"publisherDisplayName\\\":\\\"k--kato\\\"},\\\"kevinrose\\\":{\\\"publisher\\\":\\\"kevinrose\\\",\\\"publisherDisplayName\\\":\\\"KevinRose\\\"},\\\"littlefoxteam\\\":{\\\"publisher\\\":\\\"littlefoxteam\\\",\\\"publisherDisplayName\\\":\\\"Little Fox Team\\\"},\\\"lukas-tr\\\":{\\\"publisher\\\":\\\"lukas-tr\\\",\\\"publisherDisplayName\\\":\\\"Lukas Troyer\\\"},\\\"mechatroner\\\":{\\\"publisher\\\":\\\"mechatroner\\\",\\\"publisherDisplayName\\\":\\\"mechatroner\\\"},\\\"mikestead\\\":{\\\"publisher\\\":\\\"mikestead\\\",\\\"publisherDisplayName\\\":\\\"mikestead\\\"},\\\"mindaro-dev\\\":{\\\"publisher\\\":\\\"mindaro-dev\\\",\\\"publisherDisplayName\\\":\\\"Microsoft DevLabs\\\"},\\\"ms-azuretools\\\":{\\\"publisher\\\":\\\"ms-azuretools\\\",\\\"publisherDisplayName\\\":\\\"ms-azuretools\\\"},\\\"ms-kubernetes-tools\\\":{\\\"publisher\\\":\\\"ms-kubernetes-tools\\\",\\\"publisherDisplayName\\\":\\\"ms-kubernetes-tools\\\"},\\\"ms-python\\\":{\\\"publisher\\\":\\\"ms-python\\\",\\\"publisherDisplayName\\\":\\\"ms-python\\\"},\\\"ms-toolsai\\\":{\\\"publisher\\\":\\\"ms-toolsai\\\",\\\"publisherDisplayName\\\":\\\"Microsoft\\\"},\\\"ms-vscode-remote\\\":{\\\"publisher\\\":\\\"ms-vscode-remote\\\",\\\"publisherDisplayName\\\":\\\"Microsoft\\\"},\\\"ms-vscode\\\":{\\\"publisher\\\":\\\"ms-vscode\\\",\\\"publisherDisplayName\\\":\\\"Microsoft\\\"},\\\"nefrob\\\":{\\\"publisher\\\":\\\"nefrob\\\",\\\"publisherDisplayName\\\":\\\"nefrob\\\"},\\\"njpwerner\\\":{\\\"publisher\\\":\\\"njpwerner\\\",\\\"publisherDisplayName\\\":\\\"Nils Werner\\\"},\\\"oderwat\\\":{\\\"publisher\\\":\\\"oderwat\\\",\\\"publisherDisplayName\\\":\\\"oderwat\\\"},\\\"rangav\\\":{\\\"publisher\\\":\\\"rangav\\\",\\\"publisherDisplayName\\\":\\\"Thunder Client\\\"},\\\"raynigon\\\":{\\\"publisher\\\":\\\"raynigon\\\",\\\"publisherDisplayName\\\":\\\"Simon Schneider\\\"},\\\"redhat\\\":{\\\"publisher\\\":\\\"redhat\\\",\\\"publisherDisplayName\\\":\\\"Red Hat\\\"},\\\"rockingskier\\\":{\\\"publisher\\\":\\\"rockingskier\\\",\\\"publisherDisplayName\\\":\\\"RockingSkier\\\"},\\\"rvest\\\":{\\\"publisher\\\":\\\"rvest\\\",\\\"publisherDisplayName\\\":\\\"Rebecca Vest\\\"},\\\"saoudrizwan\\\":{\\\"publisher\\\":\\\"saoudrizwan\\\",\\\"publisherDisplayName\\\":\\\"saoudrizwan\\\"},\\\"streetsidesoftware\\\":{\\\"publisher\\\":\\\"streetsidesoftware\\\",\\\"publisherDisplayName\\\":\\\"Street Side Software\\\"},\\\"stylelint\\\":{\\\"publisher\\\":\\\"stylelint\\\",\\\"publisherDisplayName\\\":\\\"stylelint\\\"},\\\"tamasfe\\\":{\\\"publisher\\\":\\\"tamasfe\\\",\\\"publisherDisplayName\\\":\\\"tamasfe\\\"},\\\"teclado\\\":{\\\"publisher\\\":\\\"teclado\\\",\\\"publisherDisplayName\\\":\\\"teclado\\\"},\\\"usernamehw\\\":{\\\"publisher\\\":\\\"usernamehw\\\",\\\"publisherDisplayName\\\":\\\"usernamehw\\\"},\\\"visualstudioexptteam\\\":{\\\"publisher\\\":\\\"visualstudioexptteam\\\",\\\"publisherDisplayName\\\":\\\"Microsoft\\\"},\\\"vscode-icons-team\\\":{\\\"publisher\\\":\\\"vscode-icons-team\\\",\\\"publisherDisplayName\\\":\\\"vscode-icons-team\\\"},\\\"vue\\\":{\\\"publisher\\\":\\\"vue\\\",\\\"publisherDisplayName\\\":\\\"Vue\\\"},\\\"wholroyd\\\":{\\\"publisher\\\":\\\"wholroyd\\\",\\\"publisherDisplayName\\\":\\\"wholroyd\\\"},\\\"william-voyek\\\":{\\\"publisher\\\":\\\"william-voyek\\\",\\\"publisherDisplayName\\\":\\\"William Voyek\\\"},\\\"yoavbls\\\":{\\\"publisher\\\":\\\"yoavbls\\\",\\\"publisherDisplayName\\\":\\\"yoavbls\\\"},\\\"zhuangtongfa\\\":{\\\"publisher\\\":\\\"zhuangtongfa\\\",\\\"publisherDisplayName\\\":\\\"zhuangtongfa\\\"},\\\"windsurf\\\":{\\\"publisher\\\":\\\"windsurf\\\",\\\"publisherDisplayName\\\":\\\"Windsurf\\\"},\\\"rust-lang\\\":{\\\"publisher\\\":\\\"rust-lang\\\",\\\"publisherDisplayName\\\":\\\"rust-lang\\\"},\\\"vadimcn\\\":{\\\"publisher\\\":\\\"vadimcn\\\",\\\"publisherDisplayName\\\":\\\"vadimcn\\\"},\\\"vscjava\\\":{\\\"publisher\\\":\\\"vscjava\\\",\\\"publisherDisplayName\\\":\\\"vscjava\\\"},\\\"google\\\":{\\\"publisher\\\":\\\"Google\\\",\\\"publisherDisplayName\\\":\\\"Google\\\"}}\",\"Comments.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-request.state.hidden\":\"[{\\\"id\\\":\\\"github:createPullRequestWebview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesFiles\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesCommits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false}]\",\"windsurf.pricingNotificationDismissed\":\"true\",\"chatEditsView.hideMovedEditsView\":\"true\",\"terminal.integrated.tabs.showDetailed\":\"1\",\"workbench.view.extension.jupyter-variables.state.hidden\":\"[{\\\"id\\\":\\\"jupyterViewVariables\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cell-tag\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.containersView.state.hidden\":\"[{\\\"id\\\":\\\"vscode-containers.views.containers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.images\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.registries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.networks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.volumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.testResults.state.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.testResults.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gradleContainerView.state.hidden\":\"[{\\\"id\\\":\\\"gradleTasksView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gradleDefaultProjectsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"recentTasksView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gradleDaemonsView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sidebarDevToolsInspectorContainer.state.hidden\":\"[{\\\"id\\\":\\\"sidebarDevToolsInspector\\\",\\\"isHidden\\\":false}]\",\"windsurf.rulesFileInfoWidget.collapseState\":\"true\",\"workbench.view.extension.sidebarDevToolsDeepLinksContainer.state.hidden\":\"[{\\\"id\\\":\\\"sidebarDevToolsDeepLinks\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sidebarDevToolsContainer.state.hidden\":\"[{\\\"id\\\":\\\"sidebarDevToolsCpuProfiler\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sidebarDevToolsMemory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sidebarDevToolsPerformance\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sidebarDevToolsNetwork\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sidebarDevToolsLogging\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.flutterPropertyEditor.state.hidden\":\"[{\\\"id\\\":\\\"flutterPropertyEditor\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.geminiChat.state.hidden\":\"[{\\\"id\\\":\\\"cloudcode.gemini.chatView\\\",\\\"isHidden\\\":false}]\",\"remote.tunnels.toRestore.ssh-remote+7b22686f73744e616d65223a226465696d6f73227d.-43048982\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":43343,\\\"localPort\\\":43343,\\\"localAddress\\\":\\\"127.0.0.1:43343\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:43343\\\"},\\\"protocol\\\":\\\"http\\\",\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"User Forwarded\\\"}},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":41207,\\\"localPort\\\":41207,\\\"localAddress\\\":\\\"127.0.0.1:41207\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:41207\\\"},\\\"protocol\\\":\\\"http\\\",\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"User Forwarded\\\"}}]\",\"remote.tunnels.toRestoreExpiration.ssh-remote+7b22686f73744e616d65223a226465696d6f73227d.-43048982\":\"1752961735829\",\"remote.tunnels.toRestore.dev-container+7b22776f726b737061636550617468223a22643a5c5c6465765c5c73746f72795f74696d65325c5c73746f72795f74696d65222c22646576636f6e7461696e657250617468223a22643a5c5c6465765c5c73746f72795f74696d65325c5c73746f72795f74696d655c5c2e646576636f6e7461696e65725c5c646576636f6e7461696e65722e6a736f6e227d.956672546\":\"[{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":44259,\\\"localPort\\\":44259,\\\"localAddress\\\":\\\"127.0.0.1:44259\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:44259\\\"},\\\"protocol\\\":\\\"http\\\",\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"User Forwarded\\\"}},{\\\"remoteHost\\\":\\\"127.0.0.1\\\",\\\"remotePort\\\":34773,\\\"localPort\\\":34773,\\\"localAddress\\\":\\\"127.0.0.1:34773\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"127.0.0.1:34773\\\"},\\\"protocol\\\":\\\"http\\\",\\\"source\\\":{\\\"source\\\":0,\\\"description\\\":\\\"User Forwarded\\\"}}]\",\"remote.tunnels.toRestoreExpiration.dev-container+7b22776f726b737061636550617468223a22643a5c5c6465765c5c73746f72795f74696d65325c5c73746f72795f74696d65222c22646576636f6e7461696e657250617468223a22643a5c5c6465765c5c73746f72795f74696d65325c5c73746f72795f74696d655c5c2e646576636f6e7461696e65725c5c646576636f6e7461696e65722e6a736f6e227d.956672546\":\"1753217168591\",\"remote.explorerType\":\"dev-container\"}}"}