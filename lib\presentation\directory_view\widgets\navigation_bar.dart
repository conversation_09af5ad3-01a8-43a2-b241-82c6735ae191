import 'dart:io' show Platform;

import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:dartx/dartx.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart' hide Store;
import 'package:flutter_mobx/flutter_mobx.dart';
import 'package:mobx/mobx.dart';

import '../../../app/domain/file.dart';
import '../../../app/domain/path.dart';
import '../../../app/util/log_utils.dart';
import '../../domain/side.dart';
import '../../util/context_extensions.dart';
import '../../util/hooks/hook_utils.dart';
import '../../util/widgets/auto_complete_text_field.dart';
import '../../util/widgets/breadcrumbs.dart';

const ellipsisBreadcrumbIndex = -1;

class NavigationBar extends HookWidget {
  const NavigationBar({super.key, required this.side});

  final Side side;

  @override
  Widget build(BuildContext context) {
    final settingsRepository = context.settingsRepository;
    final directoryViewStore = context.directoryViewStore;
    final navigationBarStore = context.navigationBarStore;

    final reloadFocusNode = useFocusNode(skipTraversal: true);
    final textController = useMemoized(
      () => AutoCompleteTextFieldController(
        text: directoryViewStore.dir.absolutePath,
        calcSuggestions: navigationBarStore.calcAutoCompleteSuggestions,
        stringifySuggestion: (it) => it.absolutePath,
        config: AutoCompleteTextFieldControllerConfig(
          preselectFirstSuggestion: settingsRepository.settings.pathTextfieldSettings.preselectFirstSuggestion,
          showAutoCompletedTextInTheMiddle: settingsRepository.settings.pathTextfieldSettings.showAutoCompletedTextInTheMiddle,
          submitSuggestionOnEnter: settingsRepository.settings.pathTextfieldSettings.submitSuggestionOnEnter,
        ),
      ),
      [settingsRepository.settings.pathTextfieldSettings],
    );

    void confirmInput([String? path]) {
      navigationBarStore.hideInput();
      directoryViewStore.changeDir((path ?? textController.text).asPath(resolve: true));
    }

    final focusNode = useFocusNode(
      debugLabel: 'NavigationBar_${side.name}',
      skipTraversal: true,
      onKeyEvent: (_, event) {
        if (event is KeyDownEvent && event.logicalKey == LogicalKeyboardKey.escape) {
          navigationBarStore.hideInput();
          return KeyEventResult.handled;
        }
        return KeyEventResult.ignored;
      },
    );
    useListener(focusNode, () {
      if (!focusNode.hasFocus) {
        navigationBarStore.hideInput();
      }
    });

    useEffect(() {
      return autorun((_) {
        if (navigationBarStore.isInputVisible) {
        textController.text = directoryViewStore.dir.absolutePath +
            (settingsRepository.settings.pathTextfieldSettings.addPathSeparatorOnShow ? RawPath.pathSeparator : '');
        textController.selection = TextSelection(baseOffset: 0, extentOffset: textController.text.length);
        focusNode.requestFocus();
      } else {
        focusNode.unfocus();
      }
      }).call;
    }, []);

    return Observer(
      builder: (context) {
        final hoveredBreadcrumbIndex = navigationBarStore.hoveredBreadcrumbIndex;
        if (navigationBarStore.isInputVisible) {
          return SizedBox(
            height: 28,
            child: AutoCompleteTextField<File>(
              onSubmitted: confirmInput,
              focusNode: focusNode,
              controller: textController,
              inputDecoration: InputDecoration(
                isDense: true,
                contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 7.5),
                suffixIcon: TextButton(
                  onPressed: confirmInput,
                  style: TextButton.styleFrom(shape: const BeveledRectangleBorder()),
                  child: Icon(Icons.check, color: context.primaryColor),
                ),
              ),
            ),
          );
        } else {
          final elements = directoryViewStore.dir.elements;
          return GestureDetector(
            key: Key('navigation-bar-empty-space-${side.name}'),
            behavior: HitTestBehavior.translucent,
            onTap: navigationBarStore.showInput,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    child: Breadcrumbs(
                      items: elements,
                      itemBuilder: (context, e, index) {
                        final lastItem = index == elements.length - 1;
                        final onlyItem = elements.length == 1;
                        return TextSpan(
                          text: !onlyItem && e.isRoot && Platform.isWindows ? e.name.removeSuffix(RawPath.pathSeparator) : e.name,
                          onEnter: (_) => navigationBarStore.setBreadcrumbHover(index, e),
                          onExit: (_) => navigationBarStore.setBreadcrumbHover(null, e),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () => !lastItem ? directoryViewStore.changeDir(e) : navigationBarStore.showInput(),
                          mouseCursor: lastItem || onlyItem ? SystemMouseCursors.text : SystemMouseCursors.click,
                          style: lastItem || index == hoveredBreadcrumbIndex ? TextStyle(color: context.theme.colorScheme.secondary) : null,
                        );
                      },
                      separator: RawPath.pathSeparator,
                      collapsedElementBuilder: (context, collapse) => TextSpan(
                        text: "...",
                        onEnter: (_) => navigationBarStore.setBreadcrumbHover(ellipsisBreadcrumbIndex, null),
                        onExit: (_) => navigationBarStore.setBreadcrumbHover(null, null),
                        recognizer: TapGestureRecognizer()..onTap = collapse,
                        mouseCursor: SystemMouseCursors.click,
                        style: ellipsisBreadcrumbIndex == hoveredBreadcrumbIndex ? TextStyle(color: context.theme.colorScheme.secondary) : null,
                      ),
                      style: context.titleMedium,
                    ),
                  ),
                ),
                TextButton(
                  focusNode: reloadFocusNode,
                  onPressed: directoryViewStore.reload,
                  style: TextButton.styleFrom(foregroundColor: context.primaryColor, shape: const BeveledRectangleBorder()),
                  child: const Icon(Icons.refresh),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  static final logger = loggerFor(NavigationBar, Level.FINE);
}
