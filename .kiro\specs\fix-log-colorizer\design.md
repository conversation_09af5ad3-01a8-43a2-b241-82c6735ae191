# Design Document

## Overview

The log colorizer fix involves correcting the debug tag naming system, fixing the bracket/brace content parsing logic, and ensuring proper level numbering for nested structures. The core issue is that the current implementation uses inconsistent tag names and has parsing bugs that prevent correct AST generation.

## Architecture

The log colorizer consists of three main components:
1. **LogTokenizer** - Converts input strings into tokens
2. **LogParser** - Converts tokens into an Abstract Syntax Tree (AST)
3. **LogColorizer** - Converts AST nodes into colorized output with debug tags

The fix will focus primarily on the LogColorizer component, with some adjustments to the LogParser for better content handling.

## Components and Interfaces

### LogColorizer Class

The LogColorizer class needs the following fixes:

#### Debug Tag Mapping
- Function calls: `func`
- Parentheses: `paren_N` where N is the nesting level (0, 1, 2, ...)
- Parameter keys: `paramKey` (or `resultKey` for result parameters)
- Parameter values: `paramVal` (or `resultVal` for result values)
- Symbols: `symbol` (for =, :, ,, ->)
- Brackets: `bracket_N` where N is the nesting level
- Braces: `brace_N` where N is the nesting level
- Null values: `null`

#### Level Tracking
The colorizer needs to properly track nesting levels:
- Start at level 0 for top-level structures
- Increment levels when entering nested structures
- Pass correct levels to nested colorizers

### Content Parsing Fixes

#### Bracket Content Parsing
For content like `[file1.txt, file2.txt]`:
- Split on commas
- Create individual `paramVal` nodes for each item
- Create `symbol` nodes for commas
- Handle whitespace appropriately

#### Brace Content Parsing
For content like `{recursive: true}`:
- Parse as key-value pairs when possible
- Use `paramKey` and `paramVal` for internal structure
- Handle complex nested content

## Data Models

### AST Node Types
The existing AST node types are sufficient:
- `FunctionCallNode` - for function calls
- `ParameterNode` - for key-value pairs
- `BracketGroupNode` - for bracketed content
- `LiteralNode` - for simple text content
- `RootNode` - for multiple nodes
- `ArrowNode` - for arrow operations

### Colorizer State
The LogColorizer needs to track:
- `debug` flag
- `parenLevel` - current parenthesis nesting level
- `bracketLevel` - current bracket nesting level  
- `braceLevel` - current brace nesting level

## Error Handling

The fix should maintain existing error handling:
- Graceful handling of malformed input
- Fallback to literal nodes for unparseable content
- Preservation of original text when parsing fails

## Testing Strategy

The fix will be validated against the existing comprehensive test suite:
- All 29 existing tests must pass
- Test cases cover function calls, parameters, brackets, braces, arrows, and result statements
- Debug output format must match expected strings exactly

## Implementation Plan

1. **Fix Debug Tag Names** - Update all `_wrap` calls to use correct tag names
2. **Fix Level Numbering** - Ensure levels start at 0 and increment properly
3. **Fix Content Parsing** - Improve bracket and brace content handling
4. **Fix Parameter Recognition** - Ensure key-value pairs are properly identified
5. **Test and Iterate** - Run tests and fix remaining issues