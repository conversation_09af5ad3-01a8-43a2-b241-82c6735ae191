import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../app/persistence/data_repository.dart';
import '../../app/util/json_serializer.dart';
import '../../app/util/log_utils.dart';
import 'command_id.dart';
import 'keybind.dart';

part '.gen/keybind_repository.freezed.dart';
part '.gen/keybind_repository.g.dart';

/// Repository for managing keybinds
class KeyBindRepository extends ChangeNotifier {
  KeyBindRepository._();

  static Future<KeyBindRepository> create(DataRepository dataRepository) async {
    final repository = KeyBindRepository._();
    await dataRepository.register<KeyBinds>(
      name: "keybindings",
      createDefault: repository._createDefault,
      snapshot: repository._snapshot,
      restore: repository._restore,
      serializer: KeyBinds.serializer,
    );
    return repository;
  }

  KeyBinds _keyBinds = const KeyBinds([]);

  /// Get all keybinds
  List<KeyBind> get keyBinds => _keyBinds.keyBinds;

  /// Add a keybind
  void addKeybind(KeyBind keyBind) {
    _keyBinds = _keyBinds.addKeybind(keyBind);
    notifyListeners();
  }

  /// Remove a keybind
  void removeKeybind(KeyBind keyBind) {
    _keyBinds = _keyBinds.removeKeybind(keyBind);
    notifyListeners();
  }

  /// Update all keybinds
  void updateKeybinds(List<KeyBind> keyBinds) {
    _keyBinds = _keyBinds.copyWith(keyBinds: keyBinds);
    notifyListeners();
  }

  KeyBinds _createDefault() {
    if (kDebugMode) {
      logger.info('_createDefault(): Creating default keybinds');
    }
    return KeyBinds([
      KeyBind(commandId: CommandId.showCommandPalette, key: 'ctrl+shift+p'),
      KeyBind(commandId: CommandId.openNavigationBar, key: 'f1'),
      KeyBind(commandId: CommandId.renameFile, key: 'f2'),
      KeyBind(commandId: CommandId.copyFiles, key: 'f5'),
      KeyBind(commandId: CommandId.copyFiles, key: 'ctrl+c'),
      KeyBind(commandId: CommandId.moveFiles, key: 'f6'),
      KeyBind(commandId: CommandId.moveFiles, key: 'ctrl+x'),
      KeyBind(commandId: CommandId.deleteFiles, key: 'delete'),
      KeyBind(commandId: CommandId.navigateToParent, key: 'backspace'),
      KeyBind(commandId: CommandId.navigateUp, key: 'up'),
      KeyBind(commandId: CommandId.navigateDown, key: 'down'),
      KeyBind(commandId: CommandId.navigateBack, key: 'alt+left'),
      KeyBind(commandId: CommandId.navigateForward, key: 'alt+right'),
      KeyBind(commandId: CommandId.switchPane, key: 'tab'),
      KeyBind(commandId: CommandId.executeFile, key: 'enter'),
      KeyBind(commandId: CommandId.toggleSelection, key: 'space'),
      KeyBind(commandId: CommandId.refreshDirectory, key: 'ctrl+f'),
    ]);
  }

  KeyBinds _snapshot() => _keyBinds;

  void _restore(KeyBinds snapshot) {
    _keyBinds = snapshot;
    notifyListeners();
  }

  @override
  @visibleForTesting
  void dispose() {
    _keyBinds = const KeyBinds([]);
    super.dispose();
  }

  static final logger = loggerFor(KeyBindRepository, Level.INFO);
}

@immutable
@freezed
@JsonSerializable()
class KeyBinds with _$KeyBinds {
  const KeyBinds(this.keyBinds);

  @override
  final List<KeyBind> keyBinds;

  KeyBinds addKeybind(KeyBind keyBind) {
    final keyBinds = List<KeyBind>.from(this.keyBinds);
    keyBinds.add(keyBind);
    return KeyBinds(keyBinds);
  }

  KeyBinds removeKeybind(KeyBind keyBind) {
    final keyBinds = List<KeyBind>.from(this.keyBinds);
    keyBinds.remove(keyBind);
    return KeyBinds(keyBinds);
  }

  factory KeyBinds.fromJson(Map<String, dynamic> json) => _$KeyBindsFromJson(json);
  Map<String, dynamic> toJson() => _$KeyBindsToJson(this);

  static final serializer =
      JsonSerializer<KeyBinds>(toJson: (instance) => _$KeyBindsToJson(instance), fromJson: (json) => _$KeyBindsFromJson(json));

  @override
  String toString() => 'KeyBinds(keyBinds: $keyBinds)';
}
