import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';
import 'package:queue/queue.dart';

import '../domain/errors.dart';
import '../domain/path.dart';
import '../file_system/file_system.dart';
import '../notification/notification_repository.dart';
import '../util/duration_utils.dart';
import '../util/json_serializer.dart';
import '../util/log_utils.dart';

part '.gen/data_repository.g.dart';

const jsonEncoder = JsonEncoder.withIndent('  ');

class DataRepository {
  final DataRepositoryConfig _config;
  final FileSystem _fileSystem;
  final NotificationRepository _notificationRepository;

  final Queue _queue = Queue();

  // This exists only to make sure that a spec isn't registered twice.
  final Map<String, _DataRepositorySpec> _specs = {};

  DataRepository(this._config, this._fileSystem, this._notificationRepository);

  Future<VoidCallback> register<T>({
    required String name,
    required T Function() createDefault,
    // This will run in a reactive context, meaning it will register observers and
    // re-run whenever any of them changes
    required T Function() snapshot,
    required void Function(T snapshot) restore,
    required JsonSerializer<T> serializer,
  }) async {
    if (_specs.containsKey(name)) {
      throw AppException.internalError("'$name' is already registered!");
    }

    final path = _config.userDataDir.child("$name.json");
    final spec = _DataRepositorySpec<T>(
      this,
      name: name,
      path: path,
      createDefault: createDefault,
      snapshot: snapshot,
      restore: restore,
      serializer: serializer,
    );
    _specs[name] = spec;

    await _mkdir();

    final T? stateFromFile = await spec.readFile();
    final T state = stateFromFile ?? createDefault();
    bool fileExisted = stateFromFile != null;

    final error = spec.tryRestore(state, !fileExisted);
    if (error != null) {
      if (fileExisted) {
        // TODO: i18n
        _notificationRepository.notifyError("$path contains invalid data, resetting...");
        fileExisted = false;

        // Try again with the default state
        spec.tryRestore(createDefault(), true);
      } else {
        // This should never happen, it means we encountered an error when hydrating with the
        // default state. There is no way to recover.
        assert(false, "Failed to hydrate $path with default state");
      }
    }

    // If the file existed, we don't want to persist the initial state - we just read it.
    spec.trackChanges(skipFirstSerialize: fileExisted);

    // Return a disposer that will dispose of the spec and remove it from the map.
    return () {
      spec.dispose();
      _specs.remove(name);
    };
  }

  @visibleForTesting
  Future<T> readSnapshot<T>({
    required String name,
    required JsonSerializer<T> serializer,
  }) async {
    final path = _config.userDataDir.child("$name.json");
    final spec = _DataRepositorySpec<T>(
      this,
      name: name,
      path: path,
      createDefault: () => throw UnimplementedError("createDefault is not implemented for readSnapshot"),
      snapshot: () => throw UnimplementedError("snapshot is not implemented for readSnapshot"),
      restore: (_) => throw UnimplementedError("restore is not implemented for readSnapshot"),
      serializer: serializer,
    );

    final T? state = await spec.readFile();
    if (state == null) {
      throw AppException.invalidPersistedState("$path doesn't exist!");
    }
    return state;
  }

  Future<void> _mkdir() => _fileSystem.mkdir(_config.userDataDir, recursive: true);

  Future<void> _writeFile(RawPath path, String content) => _fileSystem.writeFile(path, content);

  @visibleForTesting
  void dispose() {
    _queue.dispose();
    for (final spec in _specs.values) {
      spec.dispose();
    }
    _specs.clear();
  }

  static final logger = loggerFor(DataRepository, Level.INFO);
}

class DataRepositoryConfig<T> {
  final RawPath userDataDir;

  DataRepositoryConfig({required this.userDataDir});

  factory DataRepositoryConfig.from({required String userDataDir}) => DataRepositoryConfig(userDataDir: userDataDir.asPath(resolve: true));
}

class _DataRepositorySpec<T> = DataRepositorySpecBase<T> with _$_DataRepositorySpec<T>;

abstract class DataRepositorySpecBase<T> with Store {
  final DataRepository repository;
  final String name;
  final RawPath path;

  final T Function() createDefault;

  // This will run in a reactive context, meaning it will register observers and
  // re-run whenever any of them changes.
  final T Function() snapshot;

  // This will run as an action.
  final void Function(T t) restore;
  final JsonSerializer<T> serializer;

  late final ReactionDisposer disposer;

  DataRepositorySpecBase(
    this.repository, {
    required this.name,
    required this.path,
    required this.createDefault,
    required this.snapshot,
    required this.restore,
    required this.serializer,
  });

  Future<T?> readFile() async {
    final stopwatch = kDebugMode ? Stopwatch() : null;
    try {
      if (kDebugMode) {
        DataRepository.logger.finer("$path Reading...");
        stopwatch!.start();
      }
      final content = await repository._fileSystem.readFile(path);
      final deserialized = serializer.fromJson(json.decode(content));
      if (kDebugMode) {
        DataRepository.logger.finer("$path Reading... Done [${stopwatch!.elapsed.humanReadable}]");
      }
      return deserialized;
    } catch (e) {
      if (kDebugMode) {
        DataRepository.logger.finer("$path Reading... Error: File doesn't exist or is corrupted, it will be created.");
      }
      return null;
    } finally {
      if (kDebugMode) {
        stopwatch!.stop();
      }
    }
  }

  @action
  Object? tryRestore(T state, bool isDefault) {
    try {
      if (kDebugMode) {
        DataRepository.logger.finer("$path Restoring state${isDefault ? " to default" : ""}...");
      }
      restore(state);
      if (kDebugMode) {
        DataRepository.logger.finer("$path Restoring state${isDefault ? " to default" : ""}... Done.");
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        DataRepository.logger.finer("$path Restoring state${isDefault ? " to default" : ""}... Error:", e);
      }
      return e;
    }
  }

  void trackChanges({required bool skipFirstSerialize}) {
    Map<String, dynamic>? prev = skipFirstSerialize ? serializer.toJson(snapshot()) : null;
    disposer = autorun((_) {
      final current = serializer.toJson(snapshot());
      if (const DeepCollectionEquality().equals(prev, current)) {
        return;
      }

      prev = current;
      final json = jsonEncoder.convert(current);

      final stopwatch = kDebugMode ? Stopwatch() : null;
      if (kDebugMode) {
        DataRepository.logger.finest("$path Writing...");
        stopwatch!.start();
      }
      repository._queue.add(() async {
        try {
          await repository._writeFile(path, json);
          if (kDebugMode) {
            DataRepository.logger.finest("$path Writing... Done [${stopwatch!.elapsed.humanReadable}]");
          }
        } catch (e) {
          if (kDebugMode) {
            DataRepository.logger.severe("$path Writing... Error:", e);
          }
          await repository._mkdir();
          await repository._writeFile(path, json);
        } finally {
          if (kDebugMode) {
            stopwatch!.stop();
          }
        }
      });
    });
  }

  void dispose() => disposer();
}
