import 'package:freezed_annotation/freezed_annotation.dart';

import '../util/json_serializer.dart';

part '.gen/settings.freezed.dart';
part '.gen/settings.g.dart';

@freezed
@JsonSerializable()
class WindowSettings with _$WindowSettings {
  const WindowSettings({
    this.width,
    this.height,
    this.x,
    this.y,
    this.isMaximized = false,
  });

  /// The window width in pixels
  @override
  final int? width;

  /// The window height in pixels
  @override
  final int? height;

  /// The window x position
  @override
  final int? x;

  /// The window y position
  @override
  final int? y;

  /// Whether the window is maximized
  @override
  final bool isMaximized;

  factory WindowSettings.fromJson(Map<String, dynamic> json) => _$WindowSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$WindowSettingsToJson(this);

  static final serializer = JsonSerializer<WindowSettings>(
    toJson: (instance) => _$WindowSettingsToJson(instance),
    fromJson: (json) => _$WindowSettingsFromJson(json),
  );
}

@freezed
@JsonSerializable()
class PathTextfieldSettings with _$PathTextfieldSettings {
  const PathTextfieldSettings({
    this.preselectFirstSuggestion = false,
    this.showAutoCompletedTextInTheMiddle = false,
    this.addPathSeparatorOnShow = true,
    this.submitSuggestionOnEnter = false,
    this.addPathSeparatorToSuggestions = true,
  });

  /// Whether to automatically select the first suggestion possible.
  @override
  final bool preselectFirstSuggestion;

  /// Whether to show the grey auto-complete character suggestions mid word.
  /// If false, only shows them at the end of the word.
  /// This behavior can be very confusing, so it is disabled by default.
  @override
  final bool showAutoCompletedTextInTheMiddle;

  /// Whether to add a path separator to the end of the current directory when the textfield is
  /// initially shown.
  @override
  final bool addPathSeparatorOnShow;

  /// Whether pressing enter with a selected suggestion will submit the suggestion or fill in its
  /// text in the textfield.
  /// If false, "enter" will behave the same as "tab" when a suggestion is selected and submitting
  /// by pressing enter is only possible if no suggestion is selected.
  @override
  final bool submitSuggestionOnEnter;

  /// Whether to add a path separator to the end of suggestions.
  @override
  final bool addPathSeparatorToSuggestions;

  factory PathTextfieldSettings.fromJson(Map<String, dynamic> json) => _$PathTextfieldSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$PathTextfieldSettingsToJson(this);

  static final serializer = JsonSerializer<PathTextfieldSettings>(
    toJson: (instance) => _$PathTextfieldSettingsToJson(instance),
    fromJson: (json) => _$PathTextfieldSettingsFromJson(json),
  );
}

@freezed
@JsonSerializable()
class Settings with _$Settings {
  const Settings({
    this.pathTextfieldSettings = const PathTextfieldSettings(),
    this.windowSettings = const WindowSettings(),
  });

  @override
  final PathTextfieldSettings pathTextfieldSettings;

  @override
  final WindowSettings windowSettings;

  factory Settings.fromJson(Map<String, dynamic> json) => _$SettingsFromJson(json);
  Map<String, dynamic> toJson() => _$SettingsToJson(this);

  static final serializer = JsonSerializer<Settings>(
    toJson: (instance) => _$SettingsToJson(instance),
    fromJson: (json) => _$SettingsFromJson(json),
  );
}
