import 'lib/app/util/log_colorizer.dart';

void main() {
  final input = "processData(files=[file1.txt, file2.txt])";
  final result = LogColorizer.colorString(input, debug: true);
  print('Input: $input');
  print('Output: $result');

  // Let's also test standalone brackets
  final input2 = "[PathsBase] [D:\\Temp] Fetching...";
  final result2 = LogColorizer.colorString(input2, debug: true);
  print('\nInput2: $input2');
  print('Output2: $result2');

  // Test standalone result
  final input3 = "Result: success";
  final result3 = LogColorizer.colorString(input3, debug: true);
  print('\nInput3: $input3');
  print('Output3: $result3');
}
