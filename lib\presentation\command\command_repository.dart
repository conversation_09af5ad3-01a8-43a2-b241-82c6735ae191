import 'package:meta/meta.dart';

import '../../app/util/quick_score.dart';
import 'command.dart';
import 'command_context.dart';
import 'command_id.dart';

/// Repository for all available commands in the application
class CommandRepository {
  CommandRepository._({required Map<CommandId, Command> commands})
      : _commands = commands,
        _quickScore = QuickScore<Command>(
          commands.values.toList(),
          {
            'id': (command) => command.id.value,
            'label': (command) => command.label,
            'group': (command) => command.group,
            'description': (command) => command.description ?? '',
          },
        );

  final Map<CommandId, Command> _commands;
  final QuickScore<Command> _quickScore;

  /// Create a repository with default commands
  factory CommandRepository.create() {
    // Define common context restrictions
    final inFileList = const CommandContext(inFileList: true);

    // Register all commands
    final commandsList = [
      // File operations
      Command(
        id: CommandId.renameFile,
        label: 'Rename',
        group: 'File',
        description: 'Rename the selected file or folder',
        requiredContext: inFileList,
      ),
      Command(
        id: CommandId.copyFiles,
        label: 'Copy',
        group: 'File',
        description: 'Copy selected files or folders',
        requiredContext: inFileList,
      ),
      Command(
        id: CommandId.moveFiles,
        label: 'Move',
        group: 'File',
        description: 'Move selected files or folders',
        requiredContext: inFileList,
      ),
      Command(
        id: CommandId.deleteFiles,
        label: 'Delete',
        group: 'File',
        description: 'Delete selected files or folders',
        requiredContext: inFileList,
      ),

      // Navigation commands
      Command(
        id: CommandId.navigateToParent,
        label: 'Go to Parent Directory',
        group: 'Navigation',
        description: 'Navigate to the parent directory',
        requiredContext: inFileList,
      ),
      Command(
        id: CommandId.navigateUp,
        label: 'Navigate Up',
        group: 'Navigation',
        description: 'Move selection up',
        requiredContext: inFileList,
      ),
      Command(
        id: CommandId.navigateDown,
        label: 'Navigate Down',
        group: 'Navigation',
        description: 'Move selection down',
        requiredContext: inFileList,
      ),
      Command(
        id: CommandId.navigateBack,
        label: 'Navigate Back',
        group: 'Navigation',
        description: 'Go back to the previous directory',
      ),
      Command(
        id: CommandId.navigateForward,
        label: 'Navigate Forward',
        group: 'Navigation',
        description: 'Go forward to the next directory',
      ),

      Command(
        id: CommandId.openNavigationBar,
        label: 'Open Navigation Bar',
        group: 'Navigation',
        description: 'Open the navigation bar for path input',
      ),

      // Pane commands
      Command(
        id: CommandId.switchPane,
        label: 'Switch Pane',
        group: 'Pane',
        description: 'Switch between left and right panes',
      ),

      // File execution
      Command(
        id: CommandId.executeFile,
        label: 'Execute',
        group: 'File',
        description: 'Open or execute the selected file',
        requiredContext: inFileList,
      ),
      Command(
        id: CommandId.toggleSelection,
        label: 'Toggle Selection',
        group: 'File',
        description: 'Toggle selection of the current file',
        requiredContext: inFileList,
      ),

      // Directory operations
      Command(
        id: CommandId.refreshDirectory,
        label: 'Refresh',
        group: 'Directory',
        description: 'Refresh the current directory',
      ),

      // Command palette
      Command(
        id: CommandId.showCommandPalette,
        label: 'Show Command Palette',
        group: 'Command',
        description: 'Show the command palette',
      ),
    ];

    // Add all commands to the map
    final commands = <CommandId, Command>{};
    for (final command in commandsList) {
      commands[command.id] = command;
    }

    return CommandRepository._(commands: commands);
  }

  /// Get all registered commands
  List<Command> getAllCommands() {
    return _commands.values.toList();
  }

  /// Get a command by its ID
  Command? getCommand(CommandId id) {
    return _commands[id];
  }

  /// Search commands using QuickScore
  List<QuickScoreResult<Command>> searchCommands(String query) {
    if (query.isEmpty) {
      // Return all commands with a perfect score
      return getAllCommands().map((command) => QuickScoreResult(command, key: 'label', score: 1.0, matches: const [])).toList();
    }

    // Use QuickScore to search commands
    return _quickScore.search(query);
  }

  @visibleForTesting
  void dispose() {}
}
