# Implementation Plan

- [x] 1. Fix debug tag names in colorizer methods


  - Update `_colorizeFunctionCall` to use correct parenthesis level tags (`paren_0`, `paren_1`, etc.)
  - Update `_colorizeParameter` to use `paramKey`/`resultKey` and `paramVal`/`resultVal` tags
  - Update all symbol colorization to use `symbol` tag instead of specific names
  - Update `_colorizeBracketGroup` to use correct level tags (`bracket_0`, `brace_0`, etc.)
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_




- [x] 2. Fix bracket and brace content parsing logic




  - Fix brace parsing in `_parseNode` to correctly capture content between braces
  - Update bracket content parsing to handle comma-separated values properly





  - Ensure nested structure parsing maintains correct AST hierarchy
  - _Requirements: 2.1, 2.2, 2.3, 2.4_





- [ ] 3. Fix level numbering and nesting
  - Ensure bracket/brace/paren levels start at 0 for top-level structures




  - Fix nested colorizer creation to pass correct incremented levels
  - Update level tracking logic to handle complex nested structures





  - _Requirements: 1.1, 1.4, 1.5, 2.4_




- [ ] 4. Fix comma-separated value handling
  - Update `_colorizeCommaSeparatedValues` to create proper paramVal and symbol nodes
  - Fix whitespace handling in comma-separated lists
  - Ensure proper spacing preservation in output
  - _Requirements: 2.1, 2.3_

- [x] 5. Fix standalone result statement parsing

  - Ensure standalone `Result: value` statements are parsed as ParameterNode
  - Update result parameter detection to work with both function parameters and standalone statements
  - Fix result array colorization to use resultVal tags
  - _Requirements: 3.1, 3.2, 3.3_



- [ ] 6. Fix arrow operation handling
  - Update arrow parsing to use symbol tag
  - Ensure arrow spacing normalization works correctly
  - Fix complex arrow expressions with proper nesting
  - _Requirements: 4.1, 4.2, 4.3_

- [ ] 7. Run comprehensive test validation
  - Execute all 29 test cases to identify remaining issues
  - Fix any remaining tag name or parsing issues
  - Ensure all tests pass with exact string matching
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 2.2, 2.3, 2.4, 3.1, 3.2, 3.3, 4.1, 4.2, 4.3_