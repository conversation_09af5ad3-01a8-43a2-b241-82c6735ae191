import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

import '../../app/util/log_utils.dart';
import 'command.dart';
import 'command_context.dart';
import 'command_context_repository.dart';
import 'command_dispatcher.dart';
import 'command_id.dart';
import 'command_repository.dart';
import 'keybind.dart';
import 'keybind_repository.dart';

/// Manages keyboard shortcuts and their mapping to commands
class KeyBindManager {
  KeyBindManager(
    this._keyBindRepository,
    this._commandRepository,
    this._commandContextRepository,
    this._commandDispatcher,
  ) {
    _buildContextKeybindMap();

    // Listen for changes to keybinds and rebuild the map
    _keyBindRepository.addListener(_buildContextKeybindMap);
  }

  final KeyBindRepository _keyBindRepository;
  final CommandRepository _commandRepository;
  final CommandContextRepository _commandContextRepository;
  final CommandDispatcher _commandDispatcher;

  // Map of context states to applicable keybinds for faster lookup
  final Map<CommandContext, List<KeyBind>> _contextKeybindMap = {};

  // List of keybinds for commands that apply in any context
  final List<KeyBind> _universalKeybinds = [];

  /// Get all keybinds
  List<KeyBind> get keyBinds => _keyBindRepository.keyBinds;

  /// Add a keybind
  void addKeybind(KeyBind keyBind) {
    _keyBindRepository.addKeybind(keyBind);
  }

  /// Remove a keybind
  void removeKeybind(KeyBind keyBind) {
    _keyBindRepository.removeKeybind(keyBind);
  }

  /// Handle a key event
  KeyEventResult handle(KeyEvent event) {
    CommandContext context = _commandContextRepository.currentContext;
    Command? command = _findCommand(event, context);
    if (command == null) {
      if (kDebugMode) {
        logger.fine('handle(${event.logicalKey.keyLabel}): No command matched in context: $context');
      }
      return KeyEventResult.ignored;
    }

    // Dispatch the command
    final handled = _commandDispatcher.dispatchCommand(command);
    return handled ? KeyEventResult.handled : KeyEventResult.ignored;
  }

  /// Build a map of context states to applicable keybinds for faster lookup
  void _buildContextKeybindMap() {
    _contextKeybindMap.clear();
    _universalKeybinds.clear();

    // Group keybinds by their command's required context
    if (kDebugMode) {
      logger.fine('Keybinds: ${keyBinds.length} entries');
    }
    for (final keyBind in keyBinds) {
      if (kDebugMode) {
        logger.fine('  $keyBind');
      }

      final command = _commandRepository.getCommand(keyBind.commandId);
      if (command == null) continue;

      final requiredContext = command.requiredContext;
      if (requiredContext == null) {
        // Command applies in any context, add to universal list
        _universalKeybinds.add(keyBind);
      } else {
        // Add to specific context
        _contextKeybindMap.putIfAbsent(requiredContext, () => []).add(keyBind);
      }
    }
  }

  /// Find a command that matches the key event and context
  Command? _findCommand(KeyEvent event, CommandContext context) {
    final contextKeyBinds = _getKeyBindsForContext(context);
    for (final keyBind in contextKeyBinds) {
      if (keyBind.matches(event)) {
        final command = _commandRepository.getCommand(keyBind.commandId);
        if (command != null) {
          return command;
        } else {
          if (kDebugMode) {
            logger.warning("_findCommand(${event.logicalKey.keyLabel}, context=$context) Command of keybind doesn't exist: $keyBind");
          }
        }
      }
    }
    return null;
  }

  /// Get the relevant keybinds for a specific context
  // TODO: Lazy cache this.
  List<KeyBind> _getKeyBindsForContext(CommandContext context) {
    final matches = _contextKeybindMap[context];
    if (_universalKeybinds.isEmpty) {
      return matches ?? [];
    }
    if (matches == null) {
      return _universalKeybinds;
    }
    return [..._universalKeybinds, ...matches];
  }

  /// Get the keybind for a specific command
  KeyBind? getKeybindForCommand(CommandId commandId) {
    for (final keyBind in keyBinds) {
      if (keyBind.commandId == commandId) {
        return keyBind;
      }
    }
    return null;
  }

  @visibleForTesting
  void dispose() {
    _keyBindRepository.removeListener(_buildContextKeybindMap);
    _keyBindRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    _commandRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    _commandContextRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    _commandDispatcher.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    _commandContextRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
  }

  static final logger = loggerFor(KeyBindManager, Level.INFO);
}
