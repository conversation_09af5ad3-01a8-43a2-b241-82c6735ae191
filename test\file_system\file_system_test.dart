import 'dart:async';
import 'dart:io' as io;

import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/app/file_system/file_system.dart';
import 'package:qfiler/app/file_system/rust/rust_file_system_adapter.dart';
import 'package:qfiler/app/task/task.dart';
import 'package:qfiler/presentation/util/setup.dart';
import 'package:qfiler/rust/.gen/api/domain.dart' as domain;
import 'package:test/test.dart';

import '../mocks/mock_file_system.dart';

void main() {
  // Test data for parameterized tests
  final testCases = [
    {
      'name': 'InMemoryFileSystem',
      'factory': () => InMemoryFileSystem(),
      'needsTempDir': false,
    },
    {
      'name': 'RustFileSystemAdapter',
      'factory': () => RustFileSystemAdapter(),
      'needsTempDir': true,
    },
  ];

  setUpAll(() async {
    final rustCrateName = 'rust_lib_qfiler';
    final dylibPath = p.join(
      io.Directory.current.path, // Project root
      'rust',
      'target',
      'debug',
      '$rustCrateName${io.Platform.isWindows ? '.dll' : io.Platform.isMacOS ? '.dylib' : '.so'}',
    );
    await setupRust(dylibPath: dylibPath);
  });

  for (final testCase in testCases) {
    group('${testCase['name']} Tests', () {
      late FileSystem fs;
      late String testDirPath;

      setUp(() async {
        fs = (testCase['factory'] as FileSystem Function())();

        if (testCase['needsTempDir'] as bool) {
          // Create a temporary directory for file system tests that need real paths
          final tempDir = await io.Directory.systemTemp.createTemp('qfiler_test_');
          testDirPath = tempDir.path;
        } else {
          // Use a virtual path for in-memory file systems
          testDirPath = '/test_root';
          // For InMemoryFileSystem, ensure the root directory exists
          await fs.mkdir(RawPath(testDirPath), recursive: true);
        }
      });

      Future<void> verifyPath(RawPath path, domain.PathStatsType expectedType) async {
        final stats = await fs.stat(path);
        expect(stats.type, expectedType, reason: 'For path "$path", expected type $expectedType but got ${stats.type}');
      }

      Future<void> verifyFileExists(RawPath path) => verifyPath(path, domain.PathStatsType.file);
      Future<void> verifyDirectoryExists(RawPath path) => verifyPath(path, domain.PathStatsType.directory);
      Future<void> verifyNotExists(RawPath path) => verifyPath(path, domain.PathStatsType.unknown);

      tearDown(() async {
        if (testCase['needsTempDir'] as bool) {
          // Clean up the temporary directory
          final tempDir = io.Directory(testDirPath);
          if (await tempDir.exists()) {
            await tempDir.delete(recursive: true);
          }
        }
      });

      RawPath getTestPath(String relativePath) {
        if (testCase['needsTempDir'] as bool) {
          return RawPath(p.join(testDirPath, relativePath));
        } else {
          return RawPath(p.posix.join(testDirPath, relativePath));
        }
      }

      group('writeFile & readFile', () {
        test('reads and writes file content correctly', () async {
          final filePath = getTestPath('rw_test.txt');
          const content1 = 'Hello, world!';
          await fs.writeFile(filePath, content1);
          expect(await fs.readFile(filePath), content1);

          const content2 = 'Goodbye, world!';
          await fs.writeFile(filePath, content2);
          expect(await fs.readFile(filePath), content2, reason: 'File content should be updated');
        });

        test('handles file operations with unicode content', () async {
          final unicodePath = getTestPath('unicode_file.txt');
          const unicodeContent = 'Hello 世界 🌍 Здравствуй мир 🚀 مرحبا بالعالم';

          await fs.writeFile(unicodePath, unicodeContent);
          expect(await fs.readFile(unicodePath), unicodeContent);

          final copyPath = getTestPath('unicode_copy.txt');
          await fs.copyFile(
            unicodePath,
            copyPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
          );

          expect(await fs.readFile(copyPath), unicodeContent);
        });

        test('handles binary file content', () async {
          final binaryPath = getTestPath('binary_file.bin');

          // Create binary content
          final binaryData = Uint8List.fromList(List.generate(256, (i) => i));
          final binaryString = String.fromCharCodes(binaryData);

          await fs.writeFile(binaryPath, binaryString);
          final readContent = await fs.readFile(binaryPath);

          expect(readContent.codeUnits, binaryData);
        });

        test('handles very long file paths', () async {
          // Create nested directories to test long paths
          final longDirPath = List.generate(10, (i) => 'very_long_directory_name_$i').join('/');
          final longPath = getTestPath('$longDirPath/long_filename.txt');

          await fs.mkdir(getTestPath(longDirPath), recursive: true);
          await fs.writeFile(longPath, 'content in long path');

          expect(await fs.readFile(longPath), 'content in long path');
        });

        test('handles concurrent file operations', () async {
          final futures = <Future>[];

          // Create multiple files concurrently
          for (int i = 0; i < 10; i++) {
            futures.add(fs.writeFile(
              getTestPath('concurrent_file_$i.txt'),
              'concurrent content $i',
            ));
          }

          await Future.wait(futures);

          // Verify all files were created
          for (int i = 0; i < 10; i++) {
            final content = await fs.readFile(getTestPath('concurrent_file_$i.txt'));
            expect(content, 'concurrent content $i');
          }
        });

        test('throws FileSystemError_NotFound when reading non-existent file', () async {
          final nonExistentPath = getTestPath('non_existent.txt');
          expectLater(() => fs.readFile(nonExistentPath), throwsA(isA<FileSystemError_NotFound>()));
        });

        test('throws FileSystemError_PermissionDenied when writing to directory', () async {
          final dirPath = getTestPath('dir_for_write');
          await fs.mkdir(dirPath);
          expectLater(() => fs.writeFile(dirPath, 'content'), throwsA(isA<FileSystemError_PermissionDenied>()));
        });

        test('throws FileSystemError_PermissionDenied when reading directory', () async {
          final dirPath = getTestPath('dir_for_read');
          await fs.mkdir(dirPath);
          expectLater(() => fs.readFile(dirPath), throwsA(isA<FileSystemError_PermissionDenied>()));
        });
      });

      group('stat', () {
        test('handles file stats correctly', () async {
          final filePath = getTestPath('stat_test_file.txt');
          final dirPath = getTestPath('stat_test_dir');

          const fileContent = 'content for stat test';
          await fs.writeFile(filePath, fileContent);
          await fs.mkdir(dirPath);

          // Test file stats
          final fileStats = await fs.stat(filePath);
          expect(fileStats.type, equals(domain.PathStatsType.file), reason: 'File type must be exactly PathStatsType.file');
          expect(BigInt.from(fileStats.size!), equals(BigInt.from(fileContent.length)),
              reason: 'File size must exactly match content length: expected ${fileContent.length}, got ${fileStats.size}');

          // Test directory stats
          final dirStats = await fs.stat(dirPath);
          expect(dirStats.type, equals(domain.PathStatsType.directory), reason: 'Directory type must be exactly PathStatsType.directory');

          // Test stats consistency after file modification
          const newContent = 'modified content for stat test with more bytes';
          await fs.writeFile(filePath, newContent);
          final modifiedStats = await fs.stat(filePath);
          expect(modifiedStats.type, equals(domain.PathStatsType.file), reason: 'File type must remain file after modification');
          expect(BigInt.from(modifiedStats.size!), equals(BigInt.from(newContent.length)),
              reason: 'File size must exactly match new content length: expected ${newContent.length}, got ${modifiedStats.size}');
        });

        test('returns stats with error, unknown type, and zero dates for non-existent path', () async {
          final nonExistentPath = getTestPath('non_existent_for_stat_error_test');
          final resultPath = await fs.stat(nonExistentPath);

          expect(resultPath.stats.type, equals(domain.PathStatsType.unknown), reason: 'Type should be unknown for non-existent path');

          final zeroDateTime = DateTime.fromMillisecondsSinceEpoch(0, isUtc: true);
          expect(resultPath.stats.createTime, equals(zeroDateTime), reason: 'Create time should be zero for non-existent path');
          expect(resultPath.stats.updateTime, equals(zeroDateTime), reason: 'Update time should be zero for non-existent path');
          expect(resultPath.stats.accessTime, equals(zeroDateTime), reason: 'Access time should be zero for non-existent path');

          expect(resultPath.stats.size, isNull, reason: 'Size should be null for non-existent path');

          expect(resultPath.stats.error, isA<FileSystemError_NotFound>(), reason: 'Error should be FileSystemError_NotFound');
        });
      });

      group('list directory', () {
        test('lists directory contents', () async {
          final dir1Path = getTestPath('dir1');
          final file1Path = getTestPath('file1.txt');
          await fs.mkdir(dir1Path);
          await fs.writeFile(file1Path, 'content1');

          final entries = await (await fs.list(RawPath(testDirPath))).toList();
          expect(entries.length, 2);
          expect(entries.any((e) => e.path.absolutePath == dir1Path.absolutePath), true);
          expect(entries.any((e) => e.path.absolutePath == file1Path.absolutePath), true);
        });

        test('lists directory with many files and folders', () async {
          final int numFiles = 100;
          final int numDirs = 5;
          final List<RawPath> createdPaths = [];

          // Create files
          for (int i = 0; i < numFiles; i++) {
            final filePath = getTestPath('many_files_file_$i.txt');
            await fs.writeFile(filePath, 'content for file $i');
            createdPaths.add(filePath);
          }

          // Create directories
          for (int i = 0; i < numDirs; i++) {
            final dirPath = getTestPath('many_files_dir_$i');
            await fs.mkdir(dirPath);
            createdPaths.add(dirPath);
          }

          final entries = await (await fs.list(RawPath(testDirPath))).toList();
          expect(entries.length, numFiles + numDirs, reason: 'Should list all created files and directories');

          // Verify all created paths are in the listed entries
          for (final createdPath in createdPaths) {
            expect(entries.any((e) => e.path.absolutePath == createdPath.absolutePath), true,
                reason: 'Path ${createdPath.absolutePath} should be listed');
          }
        });

        if (io.Platform.isWindows) {
          test('correctly handles listing root drive with trailing separator', () async {
            if (fs is InMemoryFileSystem) {
              (fs as InMemoryFileSystem).addEntry(RawPath('C:\\test.txt'));
            }
            final entries = await (await fs.list(RawPath('C:\\'))).toList();
            expect(entries.length, greaterThan(0));

            // Check that all entries start with C:\
            for (final entry in entries) {
              expect(entry.path.absolutePath.startsWith('C:\\'), true);
            }
          });

          test('correctly handles listing root drive without trailing separator', () async {
            if (fs is InMemoryFileSystem) {
              (fs as InMemoryFileSystem).addEntry(RawPath('C:\\test.txt'));
            }
            final entries = await (await fs.list(RawPath('C:'))).toList();
            expect(entries.length, greaterThan(0));

            // Check that all entries start with C:\
            for (final entry in entries) {
              expect(entry.path.absolutePath.startsWith('C:\\'), true);
            }
          });
        }

        test('handles files with special characters in names', () async {
          final specialPath = getTestPath('file with spaces & symbols!@#.txt');
          const content = 'content with special filename';
          await fs.writeFile(specialPath, content);

          expect(await fs.readFile(specialPath), content);

          final entries = await (await fs.list(RawPath(testDirPath))).toList();
          expect(entries.any((e) => e.path.absolutePath == specialPath.absolutePath), true);
        });

        test('throws FileSystemError_NotFound for non-existent directory', () async {
          final nonExistentPath = getTestPath('non_existent');
          expectLater(() async => await (await fs.list(nonExistentPath)).toList(), throwsA(isA<FileSystemError_NotFound>()));
        });

        test('throws FileSystemError_NotADirectory for a file path', () async {
          final filePath = getTestPath('a_file.txt');
          await fs.writeFile(filePath, 'content');
          expectLater(() async => await (await fs.list(filePath)).toList(), throwsA(isA<FileSystemError_NotADirectory>()));
        });
      });

      group('copyFile', () {
        test('copies files with overwrite control', () async {
          final sourcePath = getTestPath('source.txt');
          final destPath = getTestPath('dest.txt');
          const content1 = 'test content v1';
          await fs.writeFile(sourcePath, content1);

          await fs.copyFile(
              sourcePath, destPath, const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false));
          expect(await fs.readFile(destPath), content1);

          const content2 = 'test content v2';
          await fs.writeFile(sourcePath, content2);
          expectLater(
            () => fs.copyFile(
                sourcePath, destPath, const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false)),
            throwsA(isA<FileSystemError_AlreadyExists>()),
          );

          await fs.copyFile(sourcePath, destPath, const CopyOptions(overwriteIfExists: true, preserveTimestamps: true, preserveMetadata: false));
          expect(await fs.readFile(destPath), content2, reason: 'Destination file should be overwritten');
        });

        test('preserves timestamps when requested', () async {
          final sourcePath = getTestPath('source_preserve_ts.txt');
          final destPath = getTestPath('dest_preserve_ts.txt');
          const content = 'timestamp content';
          await fs.writeFile(sourcePath, content);

          final createTimestamp = DateTime.utc(2023, 1, 1, 10, 0, 0, 0);
          final modifyTimestamp = DateTime.utc(2023, 1, 2, 11, 10, 20, 0);
          final accessTimestamp = DateTime.utc(2023, 1, 3, 12, 20, 40, 0);
          await fs.setTimestamps(
            sourcePath,
            createTime: createTimestamp,
            modifyTime: modifyTimestamp,
            accessTime: accessTimestamp,
          );

          // Re-stat to confirm setTimestamps worked as expected on source before copy
          final sourceStatsAfterSet = (await fs.stat(sourcePath)).stats;
          expect(sourceStatsAfterSet.createTime, equals(createTimestamp),
              reason:
                  'Source createTime after explicit set should be ${createTimestamp.toIso8601String()}, but was ${sourceStatsAfterSet.createTime.toIso8601String()}');
          expect(sourceStatsAfterSet.updateTime, equals(modifyTimestamp),
              reason:
                  'Source updateTime after explicit set should be ${modifyTimestamp.toIso8601String()}, but was ${sourceStatsAfterSet.updateTime.toIso8601String()}');
          expect(sourceStatsAfterSet.accessTime, equals(accessTimestamp),
              reason:
                  'Source accessTime after explicit set should be ${accessTimestamp.toIso8601String()}, but was ${sourceStatsAfterSet.accessTime.toIso8601String()}');

          await fs.copyFile(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false),
          );

          final destStats = (await fs.stat(destPath)).stats;

          // For preserved timestamps, all timestamps should match the original fixed source timestamp exactly.
          expect(destStats.createTime, equals(createTimestamp),
              reason:
                  'Destination createTime ${destStats.createTime.toUtc()} should match original source createTime ${createTimestamp.toUtc()}');
          expect(destStats.updateTime, equals(modifyTimestamp),
              reason:
                  'Destination updateTime ${destStats.updateTime.toUtc()} should match original source updateTime ${modifyTimestamp.toUtc()}');
          expect(destStats.accessTime, equals(accessTimestamp),
              reason:
                  'Destination accessTime ${destStats.accessTime.toUtc()} should match original source accessTime ${accessTimestamp.toUtc()}');
        });

        test('copies files without preserving timestamps', () async {
          final sourcePath = getTestPath('source_update_ts.txt');
          final destPath = getTestPath('dest_update_ts.txt');
          const content = 'timestamp content';
          await fs.writeFile(sourcePath, content);

          final originalSourceCreateTimestamp = DateTime.utc(2023, 2, 2, 11, 0, 0, 0);
          final originalSourceModifyTimestamp = DateTime.utc(2023, 2, 3, 12, 10, 20, 0);
          final originalSourceAccessTimestamp = DateTime.utc(2023, 2, 4, 13, 20, 40, 0);
          await fs.setTimestamps(
            sourcePath,
            createTime: originalSourceCreateTimestamp,
            modifyTime: originalSourceModifyTimestamp,
            accessTime: originalSourceAccessTimestamp,
          );
          // Re-stat to confirm setTimestamps worked as expected on source
          final sourceStatsAfterSet = (await fs.stat(sourcePath)).stats;
          expect(sourceStatsAfterSet.createTime, equals(originalSourceCreateTimestamp),
              reason:
                  'Source createTime after explicit set should be ${originalSourceCreateTimestamp.toIso8601String()}, but was ${sourceStatsAfterSet.createTime.toIso8601String()}');
          expect(sourceStatsAfterSet.updateTime, equals(originalSourceModifyTimestamp),
              reason:
                  'Source updateTime after explicit set should be ${originalSourceModifyTimestamp.toIso8601String()}, but was ${sourceStatsAfterSet.updateTime.toIso8601String()}');
          expect(sourceStatsAfterSet.accessTime, equals(originalSourceAccessTimestamp),
              reason:
                  'Source accessTime after explicit set should be ${originalSourceAccessTimestamp.toIso8601String()}, but was ${sourceStatsAfterSet.accessTime.toIso8601String()}');

          final timeOfCopyApprox = DateTime.now().toUtc(); // Approximate time of copy operation, captured just before copy

          await fs.copyFile(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
          );

          final destStats = (await fs.stat(destPath)).stats;

          // Destination createTime and accessTime should around the time of copy.
          // updateTime should remain the same.
          // Allow a slightly larger delta here to account for the copy operation itself.
          const copyTimeDelta = Duration(seconds: 3);
          expect(
            destStats.createTime.isAfter(timeOfCopyApprox.subtract(copyTimeDelta)) &&
                destStats.createTime.isBefore(timeOfCopyApprox.add(copyTimeDelta)),
            isTrue,
            reason:
                'Destination createTime ${destStats.createTime.toUtc()} should be close to the time of copy ${timeOfCopyApprox.toUtc()} (within ${copyTimeDelta.inSeconds}s)',
          );
          expect(
            destStats.accessTime.isAfter(timeOfCopyApprox.subtract(copyTimeDelta)) &&
                destStats.accessTime.isBefore(timeOfCopyApprox.add(copyTimeDelta)),
            isTrue,
            reason:
                'Destination accessTime ${destStats.accessTime.toUtc()} should be close to the time of copy ${timeOfCopyApprox.toUtc()} (within ${copyTimeDelta.inSeconds}s)',
          );
          expect(destStats.updateTime, equals(originalSourceModifyTimestamp),
              reason:
                  'Destination updateTime ${destStats.updateTime.toUtc()} should match original source updateTime ${originalSourceModifyTimestamp.toUtc()} after copy');
        });

        test('copies files and preserves millisecond-precision timestamps', () async {
          final sourcePath = getTestPath('source_preserve_ms_ts.txt');
          final destPath = getTestPath('dest_preserve_ms_ts.txt');
          const content = 'millisecond timestamp content';
          await fs.writeFile(sourcePath, content);

          final createTimestampMs = DateTime.utc(2023, 7, 7, 10, 30, 15, 123); // Millisecond precision
          final modifyTimestampMs = DateTime.utc(2023, 7, 8, 11, 35, 25, 456);
          final accessTimestampMs = DateTime.utc(2023, 7, 9, 12, 40, 35, 789);
          await fs.setTimestamps(
            sourcePath,
            createTime: createTimestampMs,
            modifyTime: modifyTimestampMs,
            accessTime: accessTimestampMs,
          );

          // Re-stat to confirm setTimestamps worked as expected on source before copy
          final sourceStatsAfterSet = (await fs.stat(sourcePath)).stats;
          expect(sourceStatsAfterSet.createTime, equals(createTimestampMs),
              reason:
                  'Source createTime after explicit set should be ${createTimestampMs.toIso8601String()}, but was ${sourceStatsAfterSet.createTime.toIso8601String()}');
          expect(sourceStatsAfterSet.updateTime, equals(modifyTimestampMs),
              reason:
                  'Source updateTime after explicit set should be ${modifyTimestampMs.toIso8601String()}, but was ${sourceStatsAfterSet.updateTime.toIso8601String()}');
          expect(sourceStatsAfterSet.accessTime, equals(accessTimestampMs),
              reason:
                  'Source accessTime after explicit set should be ${accessTimestampMs.toIso8601String()}, but was ${sourceStatsAfterSet.accessTime.toIso8601String()}');

          await fs.copyFile(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false),
          );

          final destStats = (await fs.stat(destPath)).stats;
          // For preserved timestamps, all timestamps should match the original source timestamp exactly.
          expect(destStats.createTime, equals(createTimestampMs),
              reason:
                  'Destination createTime ${destStats.createTime.toUtc()} should match original source createTime ${createTimestampMs.toUtc()}');
          expect(destStats.updateTime, equals(modifyTimestampMs),
              reason:
                  'Destination updateTime ${destStats.updateTime.toUtc()} should match original source updateTime ${modifyTimestampMs.toUtc()}');
          expect(destStats.accessTime, equals(accessTimestampMs),
              reason:
                  'Destination accessTime ${destStats.accessTime.toUtc()} should match original source accessTime ${accessTimestampMs.toUtc()}');
        });

        // FIXME: Implement this test.
        test('preserveMetadata option works correctly', () async {
          final sourcePath = getTestPath('source_metadata.txt');
          final destPath = getTestPath('dest_metadata.txt');
          await fs.writeFile(sourcePath, 'content with metadata');

          // Copy with preserveMetadata: true
          await fs.copyFile(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: true),
          );

          expect(await fs.readFile(destPath), 'content with metadata');
        });

        test('handles empty files', () async {
          final emptyFilePath = getTestPath('empty_file.txt');
          await fs.writeFile(emptyFilePath, '');

          // Verify empty file stats
          final stats = await fs.stat(emptyFilePath);
          expect(stats.size, equals(0), reason: 'Empty file size must be exactly zero');
          expect(stats.type, equals(domain.PathStatsType.file), reason: 'Empty file must be recognized as file type');
          expect(await fs.readFile(emptyFilePath), equals(''), reason: 'Empty file content must be empty string');

          final copyPath = getTestPath('empty_copy.txt');
          await fs.copyFile(
            emptyFilePath,
            copyPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
          );

          // Verify copied empty file
          final copyStats = await fs.stat(copyPath);
          expect(copyStats.size, equals(0), reason: 'Copied empty file size must be exactly zero');
          expect(copyStats.type, equals(domain.PathStatsType.file), reason: 'Copied empty file must be recognized as file type');
          expect(await fs.readFile(copyPath), equals(''), reason: 'Copied empty file content must be empty string');
        });

        test('throws FileSystemError_NotFound when copying non-existent file', () async {
          final nonExistentPath = getTestPath('non_existent.txt');
          final destPath = getTestPath('dest.txt');
          expectLater(
            () => fs.copyFile(
                nonExistentPath, destPath, const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false)),
            throwsA(isA<FileSystemError_NotFound>()),
          );
        });

        test('throws FileSystemError_PermissionDenied when copying to directory', () async {
          final sourcePath = getTestPath('source_file.txt');
          final destDirPath = getTestPath('dest_dir');
          await fs.writeFile(sourcePath, 'content');
          await fs.mkdir(destDirPath);
          expectLater(
            () => fs.copyFile(
                sourcePath, destDirPath, const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false)),
            throwsA(isA<FileSystemError_PermissionDenied>()),
          );
        });
      });

      group('rename', () {
        test('renames files successfully', () async {
          final oldPath = getTestPath('old_file.txt');
          final newPath = getTestPath('new_file.txt');
          await fs.writeFile(oldPath, 'content');
          await fs.rename(oldPath, newPath);
          await verifyFileExists(newPath);
          await verifyNotExists(oldPath);
        });

        test('renames directories successfully', () async {
          final oldPath = getTestPath('old_d');
          final newPath = getTestPath('new_d');
          await fs.mkdir(oldPath);
          await fs.writeFile(getTestPath('old_d/file_in_old.txt'), 'in old');
          await fs.mkdir(newPath);

          await fs.rename(oldPath, newPath);
          await verifyFileExists(getTestPath('new_d/file_in_old.txt'));
          await verifyNotExists(oldPath);
        });

        test('overwrites existing file when renaming', () async {
          final oldPath = getTestPath('old_f.txt');
          final newPath = getTestPath('new_f.txt');
          await fs.writeFile(oldPath, 'old content');
          await fs.writeFile(newPath, 'new content');

          await fs.rename(oldPath, newPath);
          expect(await fs.readFile(newPath), 'old content', reason: 'New file should contain old file content');
          await verifyNotExists(oldPath);
        });

        test('renames file to a different path (moves)', () async {
          final sourceDirPath = getTestPath('source_dir_for_move');
          final targetDirPath = getTestPath('target_dir_for_move');
          await fs.mkdir(sourceDirPath);
          await fs.mkdir(targetDirPath);

          final oldFilePath = getTestPath('source_dir_for_move/file_to_move.txt');
          final newFilePath = getTestPath('target_dir_for_move/moved_file.txt');
          const fileContent = 'content to be moved';

          await fs.writeFile(oldFilePath, fileContent);
          await fs.rename(oldFilePath, newFilePath);

          await verifyFileExists(newFilePath);
          expect(await fs.readFile(newFilePath), fileContent, reason: 'Moved file should retain its content');
          await verifyNotExists(oldFilePath);
        });

        test('renames directory to a different path (moves)', () async {
          final sourceParentDirPath = getTestPath('source_parent_dir_for_move');
          final targetParentDirPath = getTestPath('target_parent_dir_for_move');
          await fs.mkdir(sourceParentDirPath);
          await fs.mkdir(targetParentDirPath);

          final oldDirPath = getTestPath('source_parent_dir_for_move/dir_to_move');
          final newDirPath = getTestPath('target_parent_dir_for_move/moved_dir');
          final innerFilePathOld = getTestPath('source_parent_dir_for_move/dir_to_move/inner_file.txt');
          final innerFilePathNew = getTestPath('target_parent_dir_for_move/moved_dir/inner_file.txt');
          const fileContent = 'inner file content';

          await fs.mkdir(oldDirPath);
          await fs.writeFile(innerFilePathOld, fileContent);

          await fs.rename(oldDirPath, newDirPath);

          await verifyDirectoryExists(newDirPath);
          await verifyFileExists(innerFilePathNew);
          expect(await fs.readFile(innerFilePathNew), fileContent, reason: 'File inside moved directory should retain its content');
          await verifyNotExists(oldDirPath);
          await verifyNotExists(innerFilePathOld);
        });

        test('throws FileSystemError_PermissionDenied when renaming file to existing directory', () async {
          final oldPath = getTestPath('source_file.txt');
          final newPathIsDir = getTestPath('target_dir');
          await fs.writeFile(oldPath, 'content');
          await fs.mkdir(newPathIsDir);
          expectLater(() => fs.rename(oldPath, newPathIsDir), throwsA(isA<FileSystemError_PermissionDenied>()));
        });

        // TODO: For whatever reason, this does not fail on my windows machine.
        // test('throws FileSystemError_PermissionDenied when renaming directory to existing file', () async {
        //   final oldPathIsDir = getTestPath('source_dir');
        //   final newPathIsFile = getTestPath('target_file.txt');
        //   await fs.mkdir(oldPathIsDir);
        //   await fs.writeFile(newPathIsFile, 'content');
        //   expectLater(() => fs.rename(oldPathIsDir, newPathIsFile), throwsA(isA<FileSystemError_PermissionDenied>()));
        // });

        test('throws FileSystemError_NotFound when renaming non-existent file', () async {
          final nonExistentPath = getTestPath('non_existent.txt');
          final newPath = getTestPath('new_file.txt');
          expectLater(() => fs.rename(nonExistentPath, newPath), throwsA(isA<FileSystemError_NotFound>()));
        });

        test('renaming file to a different partition should fail (e.g., C: to D:)', () async {
          // This test is specific to Windows and assumes a D: drive is available.
          if (!io.Platform.isWindows) {
            print('Skipping cross-partition rename test on non-Windows platform.');
            return;
          }

          final oldPath = getTestPath('file_to_fail_cross_move.txt');
          const content = 'cross-partition content';
          await fs.writeFile(oldPath, content);

          final newPathOnDifferentDrive = RawPath('D:\\qfiler_test_cross_rename_fail.txt');

          addTearDown(() async {
            // Cleanup destination file on D: if it somehow got created
            final destFile = io.File(newPathOnDifferentDrive.absolutePath);
            if (await destFile.exists()) {
              try {
                await destFile.delete();
              } catch (e) {
                print('Error deleting destination file in teardown: $e');
              }
            }
          });

          // Basic check if D: drive seems available.
          // A more robust check might involve trying to create a temporary file on D:.
          if (!await io.Directory('D:\\').exists()) {
            markTestSkipped('Skipping test: D: drive root does not appear to be available. Cannot test cross-partition rename.');
            return;
          }

          expectLater(
            () => fs.rename(oldPath, newPathOnDifferentDrive),
            throwsA(isA<FileSystemError_CrossesDevices>()),
            reason: 'Renaming across partitions should throw FileSystemError_Other.',
          );
        });
      });

      group('deleteFile', () {
        test('deletes files successfully', () async {
          final filePath = getTestPath('file_to_unlink.txt');
          await fs.writeFile(filePath, 'content');
          await verifyFileExists(filePath);
          await fs.deleteFile(filePath);
          await verifyNotExists(filePath);
        });

        test('throws FileSystemError_NotFound when deleting non-existent file', () async {
          final nonExistentPath = getTestPath('non_existent.txt');
          expectLater(() => fs.deleteFile(nonExistentPath), throwsA(isA<FileSystemError_NotFound>()));
        });

        test('throws FileSystemError_PermissionDenied when deleting directory', () async {
          final dirPath = getTestPath('dir_for_unlink');
          await fs.mkdir(dirPath);
          expectLater(() => fs.deleteFile(dirPath), throwsA(isA<FileSystemError_PermissionDenied>()));
        });
      });

      group('setTimestamps', () {
        // FIXME: Test setting only some of the timestamps.
        test('sets file timestamps', () async {
          final filePath = getTestPath('timestamps_test_file.txt');
          await fs.writeFile(filePath, 'timestamp test content');

          // Define precise, distinct timestamps in the past.
          final targetCreateTime = DateTime.utc(2022, 5, 20, 12, 34, 56, 0);
          final targetModifyTime = DateTime.utc(2022, 5, 21, 13, 35, 57, 0);
          final targetAccessTime = DateTime.utc(2022, 5, 22, 14, 36, 58, 0);

          await fs.setTimestamps(
            filePath,
            createTime: targetCreateTime,
            modifyTime: targetModifyTime,
            accessTime: targetAccessTime,
          );

          final stats = (await fs.stat(filePath)).stats;

          expect(stats.createTime, equals(targetCreateTime),
              reason: 'CreateTime ${stats.createTime.toIso8601String()} should be precisely set to ${targetCreateTime.toIso8601String()}');
          expect(stats.updateTime, equals(targetModifyTime),
              reason: 'UpdateTime ${stats.updateTime.toIso8601String()} should be precisely set to ${targetModifyTime.toIso8601String()}');
          expect(stats.accessTime, equals(targetAccessTime),
              reason: 'AccessTime ${stats.accessTime.toIso8601String()} should be precisely set to ${targetAccessTime.toIso8601String()}');
        });

        test('sets directory timestamps', () async {
          final dirPath = getTestPath('timestamps_test_dir');
          await fs.mkdir(dirPath);

          // Define precise, distinct timestamps in the past.
          final targetCreateTime = DateTime.utc(2023, 7, 15, 10, 20, 30, 0);
          final targetModifyTime = DateTime.utc(2023, 7, 16, 11, 21, 31, 0);
          final targetAccessTime = DateTime.utc(2023, 7, 17, 12, 22, 32, 0);

          await fs.setTimestamps(
            dirPath,
            createTime: targetCreateTime,
            modifyTime: targetModifyTime,
            accessTime: targetAccessTime,
          );

          final stats = (await fs.stat(dirPath)).stats;

          expect(stats.createTime, equals(targetCreateTime),
              reason: 'CreateTime ${stats.createTime.toIso8601String()} should be precisely set to ${targetCreateTime.toIso8601String()}');
          expect(stats.updateTime, equals(targetModifyTime),
              reason: 'UpdateTime ${stats.updateTime.toIso8601String()} should be precisely set to ${targetModifyTime.toIso8601String()}');
          expect(stats.accessTime, equals(targetAccessTime),
              reason: 'AccessTime ${stats.accessTime.toIso8601String()} should be precisely set to ${targetAccessTime.toIso8601String()}');
        });

        test('throws FileSystemError_NotFound for non-existent path', () async {
          final path = getTestPath('non_existent_path');
          final now = DateTime.now();
          expectLater(
              () => fs.setTimestamps(
                    path,
                    createTime: now,
                    modifyTime: now,
                    accessTime: now,
                  ),
              throwsA(isA<FileSystemError_NotFound>()));
        });
      });

      group('mkdir', () {
        test('creates directories successfully', () async {
          final dirPath = getTestPath('new_dir');
          await fs.mkdir(dirPath);
          await verifyDirectoryExists(dirPath);
        });

        test('creates nested directories with recursive flag', () async {
          final nestedDirPath = getTestPath('level1/level2/level3');
          await fs.mkdir(nestedDirPath, recursive: true);
          await verifyDirectoryExists(nestedDirPath);
        });

        test('throws FileSystemError_AlreadyExists if path is an existing file', () async {
          final filePath = getTestPath('existing_file.txt');
          await fs.writeFile(filePath, 'content');
          expectLater(() => fs.mkdir(filePath), throwsA(isA<FileSystemError_AlreadyExists>()));
        });

        test('throws FileSystemError_AlreadyExists if directory already exists', () async {
          final dirPath = getTestPath('existing_dir');
          await fs.mkdir(dirPath);
          expectLater(() => fs.mkdir(dirPath), throwsA(isA<FileSystemError_AlreadyExists>()));
        });

        test('throws FileSystemError_NotFound if parent does not exist and recursive is false', () async {
          final nestedDirPath = getTestPath('non_existent_parent/new_dir');
          expectLater(() => fs.mkdir(nestedDirPath), throwsA(isA<FileSystemError_NotFound>()));
        });
      });

      group('rmdir', () {
        test('removes empty directories successfully', () async {
          final dirPath = getTestPath('empty_dir');
          await fs.mkdir(dirPath);
          await verifyDirectoryExists(dirPath);
          await fs.rmdir(dirPath);
          await verifyNotExists(dirPath);
        });

        test('removes non-empty directories with recursive flag', () async {
          final dirPath = getTestPath('recursive_dir');
          final childFilePath = getTestPath('recursive_dir/child_dir/file.txt');
          await fs.mkdir(getTestPath('recursive_dir/child_dir'), recursive: true);
          await fs.writeFile(childFilePath, 'content');

          await fs.rmdir(dirPath, recursive: true);
          await verifyNotExists(dirPath);
          await verifyNotExists(childFilePath);
        });

        test('handles deeply nested directory operations', () async {
          final deepPath = List.generate(20, (i) => 'level_$i').join('/');
          final deepDirPath = getTestPath(deepPath);
          final deepFilePath = getTestPath('$deepPath/deep_file.txt');

          await fs.mkdir(deepDirPath, recursive: true);
          await fs.writeFile(deepFilePath, 'content in deep directory');

          expect(await fs.readFile(deepFilePath), 'content in deep directory');

          // Test recursive directory removal
          await fs.rmdir(getTestPath('level_0'), recursive: true);
          await verifyNotExists(deepFilePath);
        });

        test('throws FileSystemError_DirectoryNotEmpty for non-empty directory without recursive flag', () async {
          final dirPath = getTestPath('non_empty_dir');
          await fs.mkdir(dirPath);
          await fs.writeFile(getTestPath('non_empty_dir/file.txt'), 'content');
          expectLater(() => fs.rmdir(dirPath), throwsA(isA<FileSystemError_DirectoryNotEmpty>()));
        });

        test('throws FileSystemError_NotFound for non-existent directory when removing', () async {
          final dirPath = getTestPath('non_existent_dir');
          expectLater(() => fs.rmdir(dirPath), throwsA(isA<FileSystemError_NotFound>()));
        });

        test('throws FileSystemError_NotADirectory if path is a file when removing', () async {
          final filePath = getTestPath('file.txt');
          await fs.writeFile(filePath, 'content');
          expectLater(() => fs.rmdir(filePath), throwsA(isA<FileSystemError_NotADirectory>()));
        });
      });

      group('copyFileWithProgress', () {
        test('copies small files with single progress callback when chunk size is larger than file size', () async {
          final sourcePath = getTestPath('source_progress_small.txt');
          final destPath = getTestPath('dest_progress_small.txt');
          const content = 'test content for progress';
          await fs.writeFile(sourcePath, content);

          final expectedFileSize = BigInt.from(content.length);
          final chunkSize = 1024 * 64; // 64KB
          var progressCallCount = 0;
          final progressValues = <BigInt>[];

          final op = await fs.copyFileWithProgress(
              sourcePath, destPath, const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false), (progress) {
            progressCallCount++;
            progressValues.add(progress.bytesCopied);
          }, chunkSize: chunkSize);
          await op.finished();

          expect(await fs.readFile(destPath), content);

          expect(progressCallCount, equals(1),
              reason:
                  'Small file (${content.length} bytes) with chunk size ($chunkSize bytes) should have exactly 1 progress report, got: $progressCallCount');
          expect(progressValues.length, equals(1), reason: 'Should have exactly one progress value');
          expect(progressValues.first, equals(expectedFileSize),
              reason: 'Single progress report must equal exact file size: ${progressValues.first} != $expectedFileSize');
        });

        test('copies a file with multiple progress callbacks', () async {
          final sourcePath = getTestPath('source_large_custom.txt');
          final destPath = getTestPath('dest_large_custom.txt');

          // Create a file that's exactly 1000 bytes for easy calculation
          final content = List.generate(1000, (i) => String.fromCharCode(65 + (i % 26))).join();
          await fs.writeFile(sourcePath, content);

          const chunkSize = 150; // 150 bytes per chunk
          final expectedChunks = (content.length / chunkSize).ceil(); // 7 chunks (6 full + 1 partial)
          var progressCallCount = 0;
          final progressValues = <BigInt>[];

          final op = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
            (progress) {
              progressCallCount++;
              progressValues.add(progress.bytesCopied);
            },
            chunkSize: chunkSize,
          );
          await op.finished();

          expect(await fs.readFile(destPath), content);

          expect(progressCallCount, equals(expectedChunks),
              reason:
                  'File size ${content.length} bytes with chunk size $chunkSize should produce exactly $expectedChunks progress reports, got: $progressCallCount');
          expect(progressValues.length, equals(expectedChunks), reason: 'Should have exactly $expectedChunks progress values');

          // Verify each progress value matches expected chunk boundaries
          final expectedProgressValues = <BigInt>[
            BigInt.from(150), // Chunk 1: 150 bytes
            BigInt.from(300), // Chunk 2: 300 bytes
            BigInt.from(450), // Chunk 3: 450 bytes
            BigInt.from(600), // Chunk 4: 600 bytes
            BigInt.from(750), // Chunk 5: 750 bytes
            BigInt.from(900), // Chunk 6: 900 bytes
            BigInt.from(1000), // Chunk 7: 1000 bytes (final, partial chunk of 100 bytes)
          ];

          for (int i = 0; i < progressValues.length; i++) {
            expect(progressValues[i], equals(expectedProgressValues[i]),
                reason: 'Progress at chunk ${i + 1} should be exactly ${expectedProgressValues[i]}, got: ${progressValues[i]}');
          }
        });

        test('copies file when file size is exactly divisible by chunk size', () async {
          final sourcePath = getTestPath('source_exact_multiple.txt');
          final destPath = getTestPath('dest_exact_multiple.txt');

          // Create a file that's exactly 3 times the chunk size (no partial chunk)
          const chunkSize = 20;
          final content = 'A' * (chunkSize * 3); // 60 bytes exactly
          await fs.writeFile(sourcePath, content);

          const expectedChunks = 3;
          var progressCallCount = 0;
          final progressValues = <BigInt>[];

          final op = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
            (progress) {
              progressCallCount++;
              progressValues.add(progress.bytesCopied);
            },
            chunkSize: chunkSize,
          );
          await op.finished();

          expect(await fs.readFile(destPath), content);

          expect(progressCallCount, equals(expectedChunks),
              reason:
                  'File with exact multiple of chunk size (${content.length} bytes ÷ $chunkSize = $expectedChunks) should produce exactly $expectedChunks progress reports, got: $progressCallCount');

          final expectedProgressValues = [
            BigInt.from(20), // Chunk 1
            BigInt.from(40), // Chunk 2
            BigInt.from(60), // Chunk 3
          ];

          for (int i = 0; i < progressValues.length; i++) {
            expect(progressValues[i], equals(expectedProgressValues[i]),
                reason: 'Progress at chunk ${i + 1} should be exactly ${expectedProgressValues[i]}, got: ${progressValues[i]}');
          }
        });

        test('handles overwriteIfExists', () async {
          final sourcePath = getTestPath('source_overwrite_progress.txt');
          final destPath = getTestPath('dest_overwrite_progress.txt');

          // Create source and destination files
          const newContent = 'new content for overwrite test';
          const oldContent = 'old content that will be replaced';
          await fs.writeFile(sourcePath, newContent);
          await fs.writeFile(destPath, oldContent);

          final expectedFileSize = BigInt.from(newContent.length);
          const chunkSize = 8;
          final expectedChunks = (newContent.length / chunkSize).ceil();
          var progressCallCount = 0;
          final progressValues = <BigInt>[];

          final op = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: true, preserveTimestamps: false, preserveMetadata: false),
            (progress) {
              progressCallCount++;
              progressValues.add(progress.bytesCopied);
            },
            chunkSize: chunkSize,
          );
          await op.finished();

          expect(await fs.readFile(destPath), newContent);

          expect(progressCallCount, equals(expectedChunks),
              reason:
                  'Overwrite operation with file size ${newContent.length} bytes and chunk size $chunkSize should produce exactly $expectedChunks progress reports, got: $progressCallCount');
          expect(progressValues.length, equals(expectedChunks), reason: 'Should have exactly $expectedChunks progress values during overwrite');

          // Verify each progress value is exactly as expected
          for (int i = 0; i < progressValues.length - 1; i++) {
            final expectedProgress = BigInt.from((i + 1) * chunkSize);
            expect(progressValues[i], equals(expectedProgress),
                reason: 'Overwrite progress at chunk ${i + 1} should be exactly $expectedProgress, got: ${progressValues[i]}');
          }

          // Final progress must equal source file size (not destination file size)
          expect(progressValues.last, equals(expectedFileSize),
              reason: 'Final overwrite progress must equal source file size: ${progressValues.last} != $expectedFileSize');
        });

        test('preserves timestamps when requested', () async {
          final sourcePath = getTestPath('source_preserve_ts_prog.dat');
          final destPath = getTestPath('dest_preserve_ts_prog.dat');
          const sourceContent = 'content for preserve timestamps test with progress';
          await fs.writeFile(sourcePath, sourceContent);

          final sourceCreateTime = DateTime.utc(2023, 3, 3, 12, 0, 0, 0);
          final sourceModifyTime = DateTime.utc(2023, 3, 4, 13, 10, 20, 0);
          final sourceAccessTime = DateTime.utc(2023, 3, 5, 14, 20, 40, 0);
          await fs.setTimestamps(
            sourcePath,
            createTime: sourceCreateTime,
            modifyTime: sourceModifyTime,
            accessTime: sourceAccessTime,
          );
          // Re-stat to confirm setTimestamps worked as expected on source
          final sourceStats = (await fs.stat(sourcePath)).stats;
          expect(sourceStats.createTime, equals(sourceCreateTime));
          expect(sourceStats.updateTime, equals(sourceModifyTime));
          expect(sourceStats.accessTime, equals(sourceAccessTime));

          const chunkSize = 8;
          final expectedChunks = (sourceContent.length / chunkSize).ceil();
          var progressCallCount = 0;
          final progressValues = <BigInt>[];

          final op = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false),
            (progress) {
              progressCallCount++;
              progressValues.add(progress.bytesCopied);
            },
            chunkSize: chunkSize,
          );
          await op.finished();

          // Read stats before verification, because verification modifies the last access time
          final destStats = (await fs.stat(destPath)).stats;

          await verifyFileExists(destPath);
          expect(await fs.readFile(destPath), sourceContent);

          expect(progressCallCount, equals(expectedChunks),
              reason: 'Preserve TS: Expected $expectedChunks progress reports, got $progressCallCount. Values: $progressValues');
          expect(progressValues.length, equals(expectedChunks));
          for (int i = 0; i < progressValues.length; i++) {
            final expectedProgress = BigInt.from(((i + 1) * chunkSize).clamp(0, sourceContent.length));
            expect(progressValues[i], equals(expectedProgress),
                reason: 'Preserve TS: Progress at chunk ${i + 1} should be $expectedProgress, got: ${progressValues[i]}');
          }

          expect(destStats.createTime, equals(sourceCreateTime),
              reason: 'Destination createTime ${destStats.createTime.toUtc()} should match original ${sourceCreateTime.toUtc()}');
          expect(destStats.updateTime, equals(sourceModifyTime),
              reason: 'Destination updateTime ${destStats.updateTime.toUtc()} should match original ${sourceModifyTime.toUtc()}');
        });

        test('does not preserve timestamps when not requested', () async {
          final sourcePath = getTestPath('source_update_ts_prog.dat');
          final destPath = getTestPath('dest_update_ts_prog.dat');
          const sourceContent = 'content for update timestamps test with progress';
          await fs.writeFile(sourcePath, sourceContent);

          final sourceCreateTimestamp = DateTime.utc(2023, 4, 4, 13, 0, 0, 0);
          final sourceModifyTimestamp = DateTime.utc(2023, 4, 5, 14, 10, 20, 0);
          final sourceAccessTimestamp = DateTime.utc(2023, 4, 6, 15, 20, 40, 0);
          await fs.setTimestamps(
            sourcePath,
            createTime: sourceCreateTimestamp,
            modifyTime: sourceModifyTimestamp,
            accessTime: sourceAccessTimestamp,
          );
          final sourceStats = (await fs.stat(sourcePath)).stats;
          expect(sourceStats.createTime, equals(sourceCreateTimestamp));
          expect(sourceStats.updateTime, equals(sourceModifyTimestamp));
          expect(sourceStats.accessTime, equals(sourceAccessTimestamp));

          final timeOfCopyApprox = DateTime.now().toUtc();
          const chunkSize = 7;
          final expectedChunks = (sourceContent.length / chunkSize).ceil();
          var progressCallCount = 0;
          final progressValues = <BigInt>[];

          final op = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
            (progress) {
              progressCallCount++;
              progressValues.add(progress.bytesCopied);
            },
            chunkSize: chunkSize,
          );
          await op.finished();

          await verifyFileExists(destPath);
          expect(await fs.readFile(destPath), sourceContent);

          expect(progressCallCount, equals(expectedChunks),
              reason: 'Update TS: Expected $expectedChunks progress reports, got $progressCallCount. Values: $progressValues');
          expect(progressValues.length, equals(expectedChunks));
          for (int i = 0; i < progressValues.length; i++) {
            final expectedProgress = BigInt.from(((i + 1) * chunkSize).clamp(0, sourceContent.length));
            expect(progressValues[i], equals(expectedProgress),
                reason: 'Update TS: Progress at chunk ${i + 1} should be $expectedProgress, got: ${progressValues[i]}');
          }

          // Timestamp assertions
          final destStats = (await fs.stat(destPath)).stats;
          const copyTimeDelta = Duration(seconds: 3); // Allow a small delta for timing

          // Check updateTime
          expect(
            destStats.updateTime.isAfter(sourceModifyTimestamp.add(const Duration(seconds: 1))), // Ensure it's clearly after
            isTrue,
            reason:
                'Destination updateTime ${destStats.updateTime.toUtc()} should be newer than original source updateTime ${sourceModifyTimestamp.toUtc()}',
          );
          expect(
            destStats.updateTime.isBefore(timeOfCopyApprox.add(copyTimeDelta)) &&
                destStats.updateTime.isAfter(timeOfCopyApprox.subtract(copyTimeDelta)),
            isTrue,
            reason:
                'Destination updateTime ${destStats.updateTime.toUtc()} should be close to copy time ${timeOfCopyApprox.toUtc()} (within ${copyTimeDelta.inSeconds}s)',
          );

          // Check createTime (similar logic to updateTime for non-preserved)
          expect(
            destStats.createTime.isAfter(sourceCreateTimestamp.add(const Duration(seconds: 1))),
            isTrue,
            reason:
                'Destination createTime ${destStats.createTime.toUtc()} should be newer than original source createTime ${sourceCreateTimestamp.toUtc()}',
          );
          expect(
            destStats.createTime.isBefore(timeOfCopyApprox.add(copyTimeDelta)) &&
                destStats.createTime.isAfter(timeOfCopyApprox.subtract(copyTimeDelta)),
            isTrue,
            reason:
                'Destination createTime ${destStats.createTime.toUtc()} should be close to copy time ${timeOfCopyApprox.toUtc()} (within ${copyTimeDelta.inSeconds}s)',
          );

          // Check accessTime (similar logic, though access can be more variable)
          // For simplicity, we'll check it's at least newer than original.
          expect(
            destStats.accessTime.isAfter(sourceAccessTimestamp.add(const Duration(seconds: 1))),
            isTrue,
            reason:
                'Destination accessTime ${destStats.accessTime.toUtc()} should be newer than original source accessTime ${sourceAccessTimestamp.toUtc()}',
          );
        });

        test('preserves millisecond-precision timestamps when requested', () async {
          final sourcePath = getTestPath('source_preserve_ms_ts_prog.dat');
          final destPath = getTestPath('dest_preserve_ms_ts_prog.dat');
          const sourceContent = 'content for millisecond-precision timestamps test'; // Small content
          await fs.writeFile(sourcePath, sourceContent);

          final sourceCreateTimeMs = DateTime.utc(2023, 8, 8, 11, 45, 25, 456);
          final sourceModifyTimeMs = DateTime.utc(2023, 8, 9, 12, 50, 30, 567);
          final sourceAccessTimeMs = DateTime.utc(2023, 8, 10, 13, 55, 35, 678);
          await fs.setTimestamps(
            sourcePath,
            createTime: sourceCreateTimeMs,
            modifyTime: sourceModifyTimeMs,
            accessTime: sourceAccessTimeMs,
          );
          final sourceStatsAfterSet = (await fs.stat(sourcePath)).stats;
          expect(sourceStatsAfterSet.createTime, equals(sourceCreateTimeMs));
          expect(sourceStatsAfterSet.updateTime, equals(sourceModifyTimeMs));
          expect(sourceStatsAfterSet.accessTime, equals(sourceAccessTimeMs));

          const chunkSize = 5; // Yet another chunk size
          final expectedChunks = (sourceContent.length / chunkSize).ceil();
          var progressCallCount = 0;
          final progressValues = <BigInt>[];

          final op = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: true, preserveMetadata: false),
            (progress) {
              progressCallCount++;
              progressValues.add(progress.bytesCopied);
            },
            chunkSize: chunkSize,
          );
          await op.finished();

          // Read stats before verification, because verification modifies the last access time
          final destStats = (await fs.stat(destPath)).stats;

          await verifyFileExists(destPath);
          expect(await fs.readFile(destPath), sourceContent);

          expect(progressCallCount, equals(expectedChunks),
              reason: 'Preserve MS TS: Expected $expectedChunks progress reports, got $progressCallCount. Values: $progressValues');
          expect(progressValues.length, equals(expectedChunks));
          for (int i = 0; i < progressValues.length; i++) {
            final expectedProgress = BigInt.from(((i + 1) * chunkSize).clamp(0, sourceContent.length));
            expect(progressValues[i], equals(expectedProgress),
                reason: 'Preserve MS TS: Progress at chunk ${i + 1} should be $expectedProgress, got: ${progressValues[i]}');
          }

          expect(destStats.createTime, equals(sourceCreateTimeMs),
              reason: 'Destination createTime ${destStats.createTime.toUtc()} should match original ${sourceCreateTimeMs.toUtc()}');
          expect(destStats.updateTime, equals(sourceModifyTimeMs),
              reason: 'Destination updateTime ${destStats.updateTime.toUtc()} should match original ${sourceModifyTimeMs.toUtc()}');
        });

        test('handles immediate cancellation', () async {
          final sourcePath = getTestPath('source_cancel.txt');
          final destPath = getTestPath('dest_cancel.txt');

          // Create a moderately large file (10KB)
          final content = List.generate(10000, (i) => 'x').join();
          await fs.writeFile(sourcePath, content);

          // Start and immediately cancel
          final operation = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
            (progress) {},
            chunkSize: 1000,
          );
          await operation.cancel();

          expectLater(operation.finished(), throwsA(isA<CancellationError>()));
        });

        test('handles cancellation after 2nd progress report', () async {
          final sourcePath = getTestPath('source_cancel.txt');
          final destPath = getTestPath('dest_cancel.txt');

          // Create a moderately large file (10KB)
          final content = List.generate(10000, (i) => 'x').join();
          await fs.writeFile(sourcePath, content);

          var progressCallCount = 0;

          // Start and cancel after 2nd progress report
          late CopyWithProgressOperation operation;
          operation = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
            (progress) async {
              progressCallCount++;
              if (progressCallCount > 1) {
                await operation.cancel();
              }
            },
            chunkSize: 1000,
          );

          expectLater(operation.finished(), throwsA(isA<CancellationError>()));
        });

        test('handles error after 2nd progress report', () async {
          final sourcePath = getTestPath('source_error.txt');
          final destPath = getTestPath('dest_error.txt');

          // Create a moderately large file (10KB)
          final content = List.generate(10000, (i) => 'x').join();
          await fs.writeFile(sourcePath, content);

          var progressCallCount = 0;

          // Start the copy operation. The error will be triggered in the callback.
          final operation = await fs.copyFileWithProgress(
            sourcePath,
            destPath,
            const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false),
            (progress) {
              progressCallCount++;
              if (progressCallCount == 2) {
                // Simulate the source file being deleted mid-copy.
                // Do not await this; the error should be caught by the operation's future.
                fs.deleteFile(sourcePath);
              }
            },
            chunkSize: 1000,
          );

          // The test should expect the operation to fail with a NotFound error.
          await expectLater(operation.finished(), throwsA(isA<FileSystemError_NotFound>()));
        });

        test('throws error when destination exists and overwrite is false', () async {
          final sourcePath = getTestPath('source_no_overwrite.txt');
          final destPath = getTestPath('dest_no_overwrite.txt');

          await fs.writeFile(sourcePath, 'source content');
          await fs.writeFile(destPath, 'existing content');

          final operation = await fs.copyFileWithProgress(sourcePath, destPath,
              const CopyOptions(overwriteIfExists: false, preserveTimestamps: false, preserveMetadata: false), (progress) {});

          expectLater(operation.finished(), throwsA(isA<FileSystemError_AlreadyExists>()));
        });
      });
    });
  }
}
