use chrono::{DateTime, Utc};
use flutter_rust_bridge::frb;

use crate::api::error::FileSystemError;

#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum PathStatsType {
    File,
    Directory,
    Link,
    Socket,
    Pipe,
    Unknown,
}

#[derive(Debug, Clone, PartialEq, Eq)]
#[frb(dart_metadata=("freezed"))]
#[frb(json_serializable)]
pub struct PathStats {
    pub type_: PathStatsType,
    pub create_time: DateTime<Utc>,
    pub modify_time: DateTime<Utc>,
    pub access_time: DateTime<Utc>,
    pub size: Option<u64>,
    pub error: Option<FileSystemError>,
}

#[derive(Debug, Clone, PartialEq, Eq)]
#[frb(dart_metadata=("freezed"))]
#[frb(json_serializable)]
pub struct CopyOptions {
    pub overwrite_if_exists: bool,
    pub preserve_timestamps: bool,
    pub preserve_metadata: bool,
}

/// Commands to control an ongoing copy operation.
#[derive(Debug, <PERSON><PERSON>, Copy)]
pub(crate) enum CopyControlCommand {
    Pause,
    Resume,
    Cancel,
}

/// A pausable file copy operation with progress reporting.
#[frb(opaque)]
pub struct CopyWithProgressOperation {
    // pub updates: StreamSink<CopyWithProgressUpdate>,
    pub(crate) control_tx: tokio::sync::mpsc::Sender<CopyControlCommand>,
}

#[derive(Debug, Clone, PartialEq, Eq)]
pub enum CopyWithProgressUpdate {
    Progress(ProgressReport),
    Error(FileSystemError),
    Finished(Option<FileSystemError>),
}

/// Reports the progress of a file copy operation.
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
#[frb(dart_metadata=("freezed"))]
pub struct ProgressReport {
    pub bytes_copied: u64,
}

pub(crate) enum FileSource {
    File(tokio::fs::File),
    Path(String),
}