import 'package:qfiler/app/util/log_colorizer.dart';
import 'package:test/test.dart';

void main() {
  group('LogColorizer', () {
    group('Basic Function Calls', () {
      test('Basic function call with parameters', () {
        final input = "processData(files=[file1.txt, file2.txt], options={recursive: true})";
        // Overview:
        // 'processData(' is func, paren_0.
        // 'files=' is paramKey, symbol.
        // Value '[file1.txt, file2.txt]': bracket_1. 'file1.txt' (paramVal), ',' (symbol), 'file2.txt' (paramVal).
        // ', ' between params: ',' (symbol), ' ' (plaintext).
        // 'options=' is paramKey, symbol.
        // Value '{recursive: true}': brace_1. 'recursive: true' (paramVal).
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:paramVal]file2.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol] [c:paramKey]options[/c:paramKey][c:symbol]=[/c:symbol][c:brace_1]{[/c:brace_1][c:paramVal]recursive: true[/c:paramVal][c:brace_1]}[/c:brace_1][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Function call with nested brackets and mixed param value', () {
        final input = "complexFunc(param1=[1, 2, [3, 4]], param2=test[5])";
        // Overview:
        // 'param1=' value: bracket_1. Inner: '1'(pV), ','(sym), ' 2'(pV, space included), ','(sym), ' '(pV, space is a segment), bracket_2 for '[3,4]'.
        // 'param2=' value 'test[5]': 'test'(pV), bracket_1 for '[5]'.
        final expected =
            "[c:func]complexFunc[/c:func][c:paren_0]([/c:paren_0][c:paramKey]param1[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]1[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:paramVal]2[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:bracket_2][[/c:bracket_2][c:paramVal]3[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:paramVal]4[/c:paramVal][c:bracket_2]][/c:bracket_2][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol] [c:paramKey]param2[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]test[/c:paramVal][c:paren_0])[/c:paren_0][c:bracket_0][[/c:bracket_0][c:paramVal]5[/c:paramVal][c:bracket_0]][/c:bracket_0])";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Function call with standalone value parameters', () {
        final input = "_calcFile(0_temp12, doesn't exist)";
        // Overview: Params '0_temp12'(pV) and " doesn't exist"(pV, space included). Comma is symbol.
        final expected =
            "[c:func]_calcFile[/c:func][c:paren_0]([/c:paren_0][c:paramVal]0_temp12[/c:paramVal][c:symbol],[/c:symbol] [c:paramVal]doesn't exist[/c:paramVal][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Arrow Operations and Complex Parameters', () {
      test('Arrow operation inside a nested function call parameter', () {
        final input = "[FileOpExecutor] execute(RenameOperation([D:\\Temp\\0_temp1] -> [D:\\Temp\\0_temp12])): Completed Successfully [34ms]";
        // Overview: 'execute' param is 'RenameOperation(...)'. 'RenameOperation' param is '[A] -> [B]'.
        // Brackets for paths 'A'/'B' are bracket_2. '->' is symbol. Path contents are paramVals.
        final expected =
            "[c:bracket_0][[/c:bracket_0][c:paramVal]FileOpExecutor[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:func]execute[/c:func][c:paren_0]([/c:paren_0][c:paramVal]RenameOperation([D:\\Temp\\0_temp1] -> [D:\\Temp\\0_temp12])[/c:paramVal][c:paren_0])[/c:paren_0]: [c:paramVal]Completed Successfully [/c:paramVal][c:bracket_0][[/c:bracket_0][c:paramVal]34ms[/c:paramVal][c:bracket_0]][/c:bracket_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Arrow operation following function calls, with trailing unmatched parenthesis', () {
        final input = "[D:\\Temp] _applyFinishedOperation(0_temp1) RenameOperation([D:\\Temp\\0_temp1])->)";
        // Overview: '->' is symbol. Trailing ')' is plaintext.
        final expected =
            "[c:bracket_0][[/c:bracket_0]D:\\Temp[c:bracket_0]][/c:bracket_0] [c:func]_applyFinishedOperation[/c:func][c:paren_0]([/c:paren_0][c:paramVal]0_temp1[/c:paramVal][c:paren_0])[/c:paren_0] [c:func]RenameOperation[/c:func][c:paren_0]([/c:paren_0][c:bracket_1][[/c:bracket_1][c:paramVal]D:\\Temp\\0_temp1[/c:paramVal][c:bracket_1]][/c:bracket_1][c:paren_0])[/c:paren_0][c:symbol]->[/c:symbol])";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Arrow operation with multiple functions and trailing structures', () {
        final input = "remove(0_temp1) RenameOperation([D:\\Temp\\0_temp1]) -> [D:\\Temp\\0_temp12])";
        // Overview: '->' (symbol), then bracket_0, then plaintext ')'.
        final expected =
            "[c:func]remove[/c:func][c:paren_0]([/c:paren_0][c:paramVal]0_temp1[/c:paramVal][c:paren_0])[/c:paren_0] [c:func]RenameOperation[/c:func][c:paren_0]([/c:paren_0][c:bracket_1][[/c:bracket_1][c:paramVal]D:\\Temp\\0_temp1[/c:paramVal][c:bracket_1]][/c:bracket_1][c:paren_0])[/c:paren_0] [c:symbol] -> [/c:symbol][c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp\\0_temp12[/c:paramVal][c:bracket_0]][/c:bracket_0])";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Bracket and Brace Handling', () {
      test('Standalone bracketed paths', () {
        final input = "[PathsBase] [D:\\Temp] Fetching...";
        // Overview: Simple top-level bracket_0 structures.
        final expected =
            "[c:bracket_0][[/c:bracket_0]PathsBase[c:bracket_0]][/c:bracket_0] [c:bracket_0][[/c:bracket_0]D:\\Temp[c:bracket_0]][/c:bracket_0] Fetching...";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Nested brackets in parameter value', () {
        final input = "nestedBrackets(data=[[1, 2], [3, [4, 5]]])";
        // Overview: 'data=' value: outer bracket_1.
        // Inner lists: '1'(pV), ','(sym), ' 2'(pV), ','(sym), ' '(pV), bracket_2 for '[3,4]'.
        // Deepest list: '3'(pV), ','(sym), ' 4'(pV).
        final expected =
            "[c:func]nestedBrackets[/c:func][c:paren_0]([/c:paren_0][c:paramKey]data[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:bracket_2][[/c:bracket_2][c:paramVal]1[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:paramVal]2[/c:paramVal][c:bracket_2]][/c:bracket_2][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:bracket_2][[/c:bracket_2][c:paramVal]3[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:bracket_3][[/c:bracket_3][c:paramVal]4[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:paramVal]5[/c:paramVal][c:bracket_3]][/c:bracket_3][c:bracket_2]][/c:bracket_2][c:bracket_1]][/c:bracket_1][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Null and Result Values in Parameters', () {
      test('Null as a parameter value', () {
        final input = "processData(files=[file1.txt, file2.txt], options=null)";
        // Overview: 'options=null'. 'null' is [c:null].
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] [/c:paramVal][c:paramVal]file2.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol] [c:paramKey]options[/c:paramKey][c:symbol]=[/c:symbol][c:null]null[/c:null][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Result keyword as a parameter value', () {
        final input = "processData(files=[file1.txt], result=success)";
        // Overview: 'result=success'. 'result'(resultKey), 'success'(resultVal).
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol] [c:resultKey]result[/c:resultKey][c:symbol]=[/c:symbol][c:resultVal]success[/c:resultVal][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Capitalized Result keyword as a parameter', () {
        final input = "processData(files=[file1.txt], Result=success)";
        // Overview: 'Result=success'. 'Result'(resultKey), 'success'(resultVal).
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol] [c:resultKey]Result[/c:resultKey][c:symbol]=[/c:symbol][c:resultVal]success[/c:resultVal][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Result with array value containing result keywords', () {
        final input = "processData(files=[file1.txt], result=[success, partial])";
        // Overview: 'result=' value bracket_1. Inner: 'success'(rV), ','(sym), ' partial'(rV).
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol] [c:resultKey]result[/c:resultKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:resultVal]success[/c:resultVal][c:symbol],[/c:symbol][c:resultVal] partial[/c:resultVal][c:bracket_1]][/c:bracket_1][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Specific Syntactical Cases', () {
      test('Function parameter with internal bracket and brace structures', () {
        final input = "[DirectoryViewStore] [D:\\Temp] _trackFileChanges([D:\\Temp\\0_temp1]{pendingAdd})";
        // Overview: Param is '[path]{status}'. Bracket_1 has pV 'path'. Brace_1 has pV 'status'.
        final expected =
            "[c:bracket_0][[/c:bracket_0][c:paramVal]DirectoryViewStore[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:func]_trackFileChanges[/c:func][c:paren_0]([/c:paren_0][c:bracket_1][[/c:bracket_1][c:paramVal]D:\\Temp\\0_temp1[/c:paramVal][c:bracket_1]][/c:bracket_1][c:paramVal]{pendingAdd}[/c:paramVal][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Function parameter with space between internal structures, and trailing unmatched parens', () {
        final input = "[DirectoryViewStore] [D:\\Temp] _trackFileChanges([D:\\Temp\\0_temp1] {FileLifecycle.pendingRemove})))";
        // Overview: Param is '[path] {status}'. Space is paramVal segment. Trailing ')))' are unmatched paren_0.
        final expected =
            "[c:bracket_0][[/c:bracket_0][c:paramVal]DirectoryViewStore[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:func]_trackFileChanges[/c:func][c:paren_0]([/c:paren_0][c:bracket_1][[/c:bracket_1][c:paramVal]D:\\Temp\\0_temp1[/c:paramVal][c:bracket_1]][/c:bracket_1][c:paramVal] {FileLifecycle.pendingRemove}[/c:paramVal][c:paren_0])[/c:paren_0]))";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Function parameter that is a mix of bracketed value and another function call', () {
        final input = "[JobPlanner] plan([0KMJPEWYHZAYG]Job(TaskType.rename)): Planning...";
        // Overview: Param is '[ID]Job(param)'. Bracket_1 has pV 'ID'. 'Job' is func, its paren is paren_1.
        final expected =
            "[c:bracket_0][[/c:bracket_0][c:paramVal]JobPlanner[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:func]plan[/c:func][c:paren_0]([/c:paren_0][c:bracket_1][[/c:bracket_1][c:paramVal]0KMJPEWYHZAYG[/c:paramVal][c:bracket_1]][/c:bracket_1][c:func]Job[/c:func][c:paren_1]([/c:paren_1][c:paramVal]TaskType.rename[/c:paramVal][c:paren_1])[/c:paren_1][c:paren_0])[/c:paren_0]: [c:paramVal]Planning...[/c:paramVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Windows path with drive colon as a parameter value', () {
        final input = "[D:\\Temp] Received FileSystemMoveEvent('D:\\Temp\\0_temp12', isDirectory=true, destination=D:\\Temp\\0_temp1)";
        // Overview: Function name can have spaces. Params are string literal (pV), k=v, k=v.
        final expected =
            "[c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:func]Received FileSystemMoveEvent[/c:func][c:paren_0]([/c:paren_0][c:paramVal]'D:\\Temp\\0_temp12'[/c:paramVal][c:symbol],[/c:symbol] [c:paramKey]isDirectory[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]true[/c:paramVal][c:symbol],[/c:symbol] [c:paramKey]destination[/c:paramKey][c:symbol]=[/c:symbol][c:paramVal]D:\\Temp\\0_temp1[/c:paramVal][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Arrow symbol between standalone brackets (no space before second bracket)', () {
        final input = "[DirectoryViewStore] [D:\\Temp] Adjusting focus: [D:\\Temp\\123] ->[D:\\Temp\\123]: Target found in files list";
        // Overview: '->' is symbol. Brackets are bracket_0.
        final expected =
            "[c:bracket_0][[/c:bracket_0][c:paramVal]DirectoryViewStore[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:paramVal]Adjusting focus: [/c:paramVal][c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp\\123[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:symbol] -> [/c:symbol][c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp\\123[/c:paramVal][c:bracket_0]][/c:bracket_0]: [c:paramVal]Target found in files list[/c:paramVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Arrow symbol between multiple standalone brackets', () {
        final input =
            "[DirectoryViewStore] [D:\\Temp] Adjusting focus: [D:\\Temp\\0_temp12] -> [2][D:\\Temp\\0_temp2]: Target not found, fallback to closest match";
        // Overview: '-> ' is symbol (includes trailing space).
        final expected =
            "[c:bracket_0][[/c:bracket_0][c:paramVal]DirectoryViewStore[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:paramVal]Adjusting focus: [/c:paramVal][c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp\\0_temp12[/c:paramVal][c:bracket_0]][/c:bracket_0] [c:symbol] -> [/c:symbol][c:bracket_0][[/c:bracket_0][c:paramVal]2[/c:paramVal][c:bracket_0]][/c:bracket_0][c:bracket_0][[/c:bracket_0][c:paramVal]D:\\Temp\\0_temp2[/c:paramVal][c:bracket_0]][/c:bracket_0]: [c:paramVal]Target not found[/c:paramVal], [c:paramVal]fallback to closest match[/c:paramVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Filenames With Spaces and Internal Parentheses', () {
      test('Filename with spaces and hyphens, followed by standalone Result', () {
        final input = "[D:\\Temp] _calcFile(123 - Copy, exists) Result: FileLifecycle.exists";
        // Overview: Standalone 'Result: value' -> resultKey, symbol, resultVal.
        final expected =
            "[c:bracket_0][[/c:bracket_0]D:\\Temp[c:bracket_0]][/c:bracket_0] [c:func]_calcFile[/c:func][c:paren_0]([/c:paren_0][c:paramVal]123 - Copy[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] exists[/c:paramVal][c:paren_0])[/c:paren_0] [c:resultKey]Result[/c:resultKey][c:symbol]:[/c:symbol][c:resultVal] FileLifecycle.exists[/c:resultVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Filename with internal parentheses as part of paramVal', () {
        final input = "_calcFile(document (2023).pdf, exists)";
        // Overview: ParamVal 'document (2023).pdf' is segmented by internal parens.
        // 'document '(pV), '('(paren_1), '2023'(pV), ')'(paren_1), '.pdf'(pV).
        final expected =
            "[c:func]_calcFile[/c:func][c:paren_0]([/c:paren_0][c:paramVal]document [/c:paramVal][c:paren_1]([/c:paren_1][c:paramVal]2023[/c:paramVal][c:paren_1])[/c:paren_1][c:paramVal].pdf[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] exists[/c:paramVal][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Filename with multiple internal parentheses', () {
        final input = "_calcFile(report (final) (v2).docx, exists)";
        // Overview: ParamVal 'report (final) (v2).docx' is segmented.
        final expected =
            "[c:func]_calcFile[/c:func][c:paren_0]([/c:paren_0][c:paramVal]report [/c:paramVal][c:paren_1]([/c:paren_1][c:paramVal]final[/c:paramVal][c:paren_1])[/c:paren_1][c:paramVal] [/c:paramVal][c:paren_1]([/c:paren_1][c:paramVal]v2[/c:paramVal][c:paren_1])[/c:paren_1][c:paramVal].docx[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] exists[/c:paramVal][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Parameter Formats (key=value vs key: value as paramVal)', () {
      test('key=value format with structured values', () {
        final input = "processData(files=[file1.txt], options={recursive: true})";
        // Same as Basic Function Calls #1.
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol] [c:paramKey]options[/c:paramKey][c:symbol]=[/c:symbol][c:brace_1]{[/c:brace_1][c:paramVal]recursive: true[/c:paramVal][c:brace_1]}[/c:brace_1][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('key: value format (treated as single value parameter with internal structure)', () {
        final input = "processData(files=[file1.txt], options: {recursive: true})";
        // Overview: Param 'options: {recursive: true}' is "just value".
        // Segments: 'options'(pV), ':'(sym), ' '(pV), brace_1 around '{recursive: true}'(pV).
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:symbol],[/c:symbol][c:paramVal] options[/c:paramVal][c:symbol]:[/c:symbol][c:paramVal] [/c:paramVal][c:brace_1]{[/c:brace_1][c:paramVal]recursive: true[/c:paramVal][c:brace_1]}[/c:brace_1][c:paren_0])[/c:paren_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Standalone Result Values (Not function parameters)', () {
      test('Standalone result: success', () {
        final input = "Result: success";
        // Overview: 'Result'(rK), ':'(sym), ' success'(rV).
        final expected = "[c:resultKey]Result[/c:resultKey][c:symbol]:[/c:symbol][c:resultVal] success[/c:resultVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Standalone result: with non-keyword value', () {
        final input = "result: FileLifecycle.exists";
        // Overview: 'result'(rK), ':'(sym), ' FileLifecycle.exists'(rV).
        final expected = "[c:resultKey]result[/c:resultKey][c:symbol]:[/c:symbol][c:resultVal] FileLifecycle.exists[/c:resultVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Standalone result: with array of result keywords', () {
        final input = "RESULT: [success, partial]";
        // Overview: 'RESULT'(rK), ':'(sym), ' '(plaintext), bracket_0.
        // Inner: 'success'(rV), ','(sym), ' partial'(rV).
        final expected =
            "[c:resultKey]RESULT[/c:resultKey][c:symbol]:[/c:symbol] [c:bracket_0][[/c:bracket_0][c:resultVal]success[/c:resultVal][c:symbol],[/c:symbol][c:resultVal] partial[/c:resultVal][c:bracket_0]][/c:bracket_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Standalone result: with object value and internal splitting', () {
        final input = "Results: {status: success, code: 200}";
        // Overview: 'Results'(rK), ':'(sym), ' '(plaintext), brace_0.
        // Inner: 'status'(rV), ':'(sym), ' success'(rV), ','(sym), ' code: 200'(rV).
        final expected =
            "[c:resultKey]Results[/c:resultKey][c:symbol]:[/c:symbol] [c:brace_0]{[/c:brace_0][c:resultVal]status[/c:resultVal][c:symbol]:[/c:symbol][c:resultVal] success[/c:resultVal][c:symbol],[/c:symbol][c:resultVal] code: 200[/c:resultVal][c:brace_0]}[/c:brace_0]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });

    group('Inline Result Values (Following a function call)', () {
      test('Inline result with specific keyword', () {
        final input = "[Files] [D:\\Temp] _calcFile(0_temp1, doesn't exist) Result: removed";
        // Overview: After func call, ' Result: removed'.
        final expected =
            "[c:bracket_0][[/c:bracket_0]Files[c:bracket_0]][/c:bracket_0] [c:bracket_0][[/c:bracket_0]D:\\Temp[c:bracket_0]][/c:bracket_0] [c:func]_calcFile[/c:func][c:paren_0]([/c:paren_0][c:paramVal]0_temp1[/c:paramVal][c:symbol],[/c:symbol][c:paramVal] doesn't exist[/c:paramVal][c:paren_0])[/c:paren_0] [c:resultKey]Result[/c:resultKey][c:symbol]:[/c:symbol][c:resultVal] removed[/c:resultVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });

      test('Inline result with non-keyword value', () {
        final input = "processData(files=[file1.txt]) Result: FileLifecycle.exists";
        // Overview: After func call, ' Result: FileLifecycle.exists'.
        final expected =
            "[c:func]processData[/c:func][c:paren_0]([/c:paren_0][c:paramKey]files[/c:paramKey][c:symbol]=[/c:symbol][c:bracket_1][[/c:bracket_1][c:paramVal]file1.txt[/c:paramVal][c:bracket_1]][/c:bracket_1][c:paren_0])[/c:paren_0] [c:resultKey]Result[/c:resultKey][c:symbol]:[/c:symbol][c:resultVal] FileLifecycle.exists[/c:resultVal]";
        final actual = LogColorizer.colorString(input, debug: true);
        expect(actual, equals(expected));
      });
    });
  });
}
