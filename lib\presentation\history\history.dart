import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:mobx/mobx.dart';

import '../../../app/domain/path.dart';
import '../../../app/util/collection_utils.dart';
import '../../app/util/observable_utils.dart';
import './directory_state.dart';

part '.gen/history.g.dart';

const maxHistoryEntries = 30;

@JsonSerializable()
class History extends _History with _$History {
  History(Iterable<DirectoryState> history, {int currentIndex = 0}) : super._(history, currentIndex);

  factory History.single(RawPath path, {RawPath? focusedPath, int focusedRowIndex = 0}) =>
      History([DirectoryState(path, focusedPath: focusedPath, focusedRowIndex: focusedRowIndex)], currentIndex: 0);

  History clone() => History(history.map((e) => e.copyWith()).toList(), currentIndex: _currentIndex);

  factory History.fromJson(Map<String, dynamic> json) => _$HistoryFromJson(json);
  Map<String, dynamic> toJson() => _$HistoryToJson(this);
}

@StoreConfig(hasToString: false)
abstract class _History with Store {
  final history = ObservableList<DirectoryState>();

  @observable
  int _currentIndex;

  _History._(Iterable<DirectoryState> history, this._currentIndex) {
    assert(history.isNotEmpty, "Trying to create an empty history!");
    if (history.isEmpty) {
      history = [DirectoryState(RawPath.currentWorkingDir)];
    }
    this.history.addAll(history);
  }

  // FIXME: Temporarily exposed to get json_serializable to include it in the generated code.
  // FIXME: Newer version of json_serializable should fix this..
  int get currentIndex => _currentIndex;

  DirectoryState get current {
    if (_currentIndex >= history.length) {
      assert(false, "History index out of bounds: $_currentIndex");
      _setCurrentIndex(history.length);
    }
    return history[_currentIndex];
  }

  DirectoryState? get prev => _currentIndex > 0 ? history[_currentIndex - 1] : null;

  DirectoryState? get next => _currentIndex < history.length - 1 ? history[_currentIndex + 1] : null;

  int get length => history.length;

  @action
  void setFocusedPath(RawPath focusedPath, int focusedRowIndex) => current.setFocusedPath(focusedPath, focusedRowIndex);

  @action
  void push(DirectoryState entry) {
    _currentIndex++;
    if (_currentIndex < length) {
      // Truncate history from currentIndex onwards
      history.removeRange(_currentIndex, length);
    } else if (_currentIndex >= maxHistoryEntries) {
      history.removeRange(0, _currentIndex - maxHistoryEntries + 1);
      _currentIndex = maxHistoryEntries - 1;
    }
    history.add(entry);
  }

  @action
  void goBack() {
    if (_currentIndex > 0) {
      _currentIndex--;
    }
  }

  @action
  void goForward() {
    if (_currentIndex < length - 1) {
      _currentIndex++;
    }
  }

  DirectoryState? findLast(bool Function(DirectoryState) predicate, [int? fromIndex]) => history.lastWhereFromIndex(predicate, fromIndex);

  DirectoryState? findPrev(bool Function(DirectoryState) predicate) => findLast(predicate, _currentIndex - 1);

  @action
  void _setCurrentIndex(int currentIndex) => _currentIndex = currentIndex;

  @action
  void replace(History history) {
    this.history.replace(history.history);
    _setCurrentIndex(history.currentIndex);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is _History && other._currentIndex == _currentIndex && listEquals(other.history, history);
  }

  @override
  int get hashCode => Object.hash(_currentIndex, Object.hashAll(history));

  @override
  String toString() => "History{index=$_currentIndex} $history";
}
