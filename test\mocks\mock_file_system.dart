import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;
import 'dart:math' as math;

import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';
import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/app/file_system/file_system.dart';
import 'package:qfiler/app/task/task.dart';
import 'package:qfiler/rust/.gen/api/domain.dart' as domain;

/// A fake implementation of the FileSystem for testing purposes.
/// This allows simulating file system operations without interacting with the actual file system.
class InMemoryFileSystem implements FileSystem {
  // In-memory representation of the file system: path -> content (for files) or null (for directories)
  final Map<String, _FileSystemEntry> _entries = {};

  // Helper to normalize paths (e.g., handle different separators)
  String _normalizePath(RawPath path) {
    var normalized = p.normalize(path.absolutePath).toLowerCase().replaceAll('\\', '/');

    // Remove trailing slash unless it's the root directory
    if (normalized.length > 1 && normalized.endsWith('/')) {
      normalized = normalized.substring(0, normalized.length - 1);
    }

    return normalized;
  }

  String _getDriveLetter(String normalizedPath) {
    // Extract drive letter from normalized path (e.g., "C:/" from "C:/some/path")
    if (normalizedPath.length >= 2 && normalizedPath[1] == ':') {
      return normalizedPath.substring(0, 2).toUpperCase();
    }
    return ''; // No drive letter (Unix-style path)
  }

  // Helper to get a file system entry, returning null if not found
  // This method is kept for future use
  // ignore: unused_element


  // Helper to ensure parent directory exists by checking and throwing errors if not valid.
  // Ensures parent directory hierarchy exists, creating it if necessary.
  void _createParentDirHierarchyIfNeeded(RawPath path) {
    final parentPath = path.parent;
    if (parentPath != null && parentPath.absolutePath != path.absolutePath) {
      // Check it's not root
      _createParentDirHierarchyIfNeeded(parentPath); // Recursive call for the parent's parent

      final normalizedParent = _normalizePath(parentPath);
      final parentEntry = _entries[normalizedParent];

      if (parentEntry == null) {
        _entries[normalizedParent] = _FileSystemEntry(isDirectory: true);
      } else if (!parentEntry.isDirectory) {
        // This is an error: a file exists where a directory is needed in the hierarchy.
        throw FileSystemError_NotADirectory(message: 'Not a directory: $parentPath');
      }
    }
  }

  // Check if a path is a root path (like '/', 'C:\', etc.)
  bool _isRootPath(RawPath path) {
    final normalized = _normalizePath(path);
    return normalized == '/' || (normalized.length == 2 && normalized.endsWith(':'));
  }

  // Strict check: ensures parent directory exists and is a directory, throws if not.
  // Used by mkdir non-recursive.
  void _ensureParentDirExists(RawPath path) {
    final parentPath = path.parent;
    // Check if parentPath is not null and different from the current path (to avoid issues with root)
    if (parentPath != null && parentPath.absolutePath != path.absolutePath && !_isRootPath(path)) {
      final normalizedParent = _normalizePath(parentPath);
      final parentEntry = _entries[normalizedParent];
      if (parentEntry == null) {
        throw FileSystemError_NotFound(message: 'Path not found: $parentPath');
      }
      if (!parentEntry.isDirectory) {
        throw FileSystemError_NotADirectory(message: 'Not a directory: $parentPath');
      }
    }
    // If parentPath is null (path is likely a root or similar) or if parent exists and is a directory, it's fine.
  }

  @override
  Future<CopyWithProgressOperation> copyFileWithProgress(
      RawPath source, RawPath dest, CopyOptions options, FutureOr<void> Function(ProgressReport) onProgress,
      {int chunkSize = FileSystem.defaultCopyChunkSize}) async {
    // For the mock, we'll simulate the copy operation with progress callbacks
    final normalizedSource = _normalizePath(source);
    final normalizedDest = _normalizePath(dest);

    _createParentDirHierarchyIfNeeded(dest);

    // Create the operation that will handle the copy with progress
    // Pass validation parameters to the operation so it can handle errors internally
    final operation = _MockCopyWithProgressOperation(
      source: source,
      dest: dest,
      normalizedSource: normalizedSource,
      normalizedDest: normalizedDest,
      options: options,
      onProgress: onProgress,
      chunkSize: chunkSize,
      entries: _entries,
    );

    // The operation starts automatically via Future.microtask in the constructor
    return operation;
  }

  @override
  Future<void> copyFile(RawPath sourcePath, RawPath destinationPath, CopyOptions options) async {
    final normalizedSource = _normalizePath(sourcePath);
    final normalizedDest = _normalizePath(destinationPath);

    final sourceEntry = _entries[normalizedSource];
    if (sourceEntry == null || sourceEntry.isDirectory) {
      throw FileSystemError_NotFound(message: 'Source file not found: ${sourcePath.absolutePath}');
    }

    final destEntry = _entries[normalizedDest];
    if (destEntry != null) {
      if (destEntry.isDirectory) {
        throw FileSystemError_PermissionDenied(message: 'Is a directory: $destinationPath');
      } else {
        if (!options.overwriteIfExists) {
          // If it's not a directory and it exists, it must be a file.
          throw FileSystemError_AlreadyExists(message: 'File already exists: $destinationPath');
        }
      }
    }

    _createParentDirHierarchyIfNeeded(destinationPath); // Ensure parent hierarchy exists or is created

    final newEntry = _FileSystemEntry(
      content: sourceEntry.content,
      createDate: options.preserveTimestamps ? sourceEntry.createDate : DateTime.now(),
      updateDate: sourceEntry.updateDate, // updateDate is copied even when not preserving timestamps
      accessDate: options.preserveTimestamps ? sourceEntry.accessDate : DateTime.now(),
      isDirectory: false,

    );

    _entries[normalizedDest] = newEntry;
  }

  @override
  Future<Stream<Path>> list(RawPath path) async {
    final normalizedPathString = _normalizePath(path);
    var entry = _entries[normalizedPathString];

    // Handle Windows root drives - create them automatically if they don't exist
    if (entry == null && _isWindowsRootDrive(normalizedPathString)) {
      _entries[normalizedPathString] = _FileSystemEntry(isDirectory: true);
      entry = _entries[normalizedPathString];
    }

    if (entry == null) {
      return Stream.error(FileSystemError_NotFound(message: 'Path not found: $path'));
    }
    if (!entry.isDirectory) {
      return Stream.error(FileSystemError_NotADirectory(message: 'Not a directory: $path'));
    }

    final children = _entries.entries.where((mapEntry) {
      final childPathString = mapEntry.key;
      if (childPathString == normalizedPathString) return false; // Not a child of itself

      // Check if childPathString is a direct child of normalizedPathString
      final parentOfChild = _normalizePath(RawPath(p.dirname(childPathString)));
      return parentOfChild == normalizedPathString;
    }).map((mapEntry) {
      final childPathString = mapEntry.key;
      final childEntry = mapEntry.value;

      // Convert normalized path back to Windows format if on Windows AND it's a Windows path
      String displayPath = childPathString;
      if (io.Platform.isWindows && childPathString.length >= 2 && childPathString[1] == ':') {
        displayPath = childPathString.replaceAll('/', '\\');
        // Ensure drive letter is uppercase
        displayPath = displayPath[0].toUpperCase() + displayPath.substring(1);
      }

      final childRawPath = RawPath(displayPath);

      final stats = PathStats(
        type: childEntry.isDirectory ? domain.PathStatsType.directory : domain.PathStatsType.file,
        createTime: childEntry.createDate,
        updateTime: childEntry.updateDate,
        accessTime: childEntry.accessDate,
        size: childEntry.isDirectory ? null : childEntry.content?.length,
        // error: childEntry.errorOnAccess ? Exception('Simulated access error') : null, // Removed usage of non-existent field
      );
      return Path(childRawPath, stats); // Construct Path
    });

    return Stream.fromIterable(children);
  }

  // Helper to check if a path is a Windows root drive (like "c:" or "c:/")
  bool _isWindowsRootDrive(String normalizedPath) {
    return normalizedPath.length == 2 && normalizedPath.endsWith(':');
  }

  @override
  Future<void> mkdir(RawPath path, {bool recursive = false}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry != null) {
      // If entry exists, it's an error, regardless of whether it's a file or directory.
      // The tests expect FileAlreadyExistsError in both cases for mkdir.
      throw FileSystemError_AlreadyExists(message: 'Already exists: $path');
    }

    if (recursive) {
      RawPath currentPath = path;
      while (currentPath.parent != null && currentPath.absolutePath != currentPath.parent!.absolutePath) {
        final parentPath = currentPath.parent!;
        final normalizedParent = _normalizePath(parentPath);
        final parentEntry = _entries[normalizedParent];

        if (parentEntry != null && !parentEntry.isDirectory) {
          throw io.FileSystemException('Parent path exists and is not a directory', parentPath.absolutePath);
        }
        if (parentEntry == null) {
          _entries[normalizedParent] = _FileSystemEntry(isDirectory: true);
        }
        currentPath = parentPath;
      }
    } else {
      // If not recursive, ensure parent exists. The _ensureParentDirExists method will throw if not.
      _ensureParentDirExists(path);
    }

    _entries[normalizedPath] = _FileSystemEntry(isDirectory: true);
  }

  @override
  Future<String> readFile(RawPath path) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      throw FileSystemError_NotFound(message: 'File not found: $path');
    }

    if (entry.isDirectory) {
      throw FileSystemError_PermissionDenied(message: 'Is a directory: $path');
    }

    return utf8.decode(entry.content!);
  }

  @override
  Future<void> rename(RawPath sourcePath, RawPath destinationPath) async {
    final normalizedSource = _normalizePath(sourcePath);
    final normalizedDest = _normalizePath(destinationPath);

    final sourceEntry = _entries[normalizedSource];
    if (sourceEntry == null) {
      throw FileSystemError_NotFound(message: 'Path not found: $sourcePath');
    }

    // Check for cross-device rename (different drive letters on Windows)
    final sourceDrive = _getDriveLetter(normalizedSource);
    final destDrive = _getDriveLetter(normalizedDest);
    if (sourceDrive != destDrive) {
      throw FileSystemError_CrossesDevices(message: 'Cannot rename across different drives: $sourcePath to $destinationPath');
    }

    final destEntry = _entries[normalizedDest];
    if (destEntry != null) {
      if (destEntry.isDirectory && !sourceEntry.isDirectory) {
        // Cannot rename file to existing directory
        throw FileSystemError_PermissionDenied(message: 'Cannot rename file to existing directory: $destinationPath');
      }
      // Allow overwriting existing files or directories - remove the existing entry and its children
      if (destEntry.isDirectory) {
        _entries.removeWhere((path, _) => path == normalizedDest || path.startsWith('$normalizedDest/'));
      } else {
        _entries.remove(normalizedDest);
      }
    }

    _ensureParentDirExists(destinationPath); // Use original destinationPath for parent check

    // If renaming a directory, we need to move all its children too
    if (sourceEntry.isDirectory) {
      final childrenToMove = <String, _FileSystemEntry>{};

      // Find all children of the source directory
      for (final entry in _entries.entries) {
        final childPath = entry.key;
        if (childPath.startsWith('$normalizedSource/')) {
          final relativePath = childPath.substring(normalizedSource.length + 1);
          final newChildPath = '$normalizedDest/$relativePath';
          childrenToMove[newChildPath] = entry.value;
        }
      }

      // Remove all old entries (directory and its children)
      _entries.removeWhere((path, _) => path == normalizedSource || path.startsWith('$normalizedSource/'));

      // Add the directory and its children at the new location
      _entries[normalizedDest] = sourceEntry;
      childrenToMove.forEach((newPath, entry) {
        _entries[newPath] = entry;
      });
    } else {
      // For files, simple move
      _entries[normalizedDest] = sourceEntry;
      _entries.remove(normalizedSource);
    }
  }

  @override
  Future<void> rmdir(RawPath path, {bool recursive = false}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      throw FileSystemError_NotFound(message: 'Path not found: $path');
    }

    if (!entry.isDirectory) {
      throw FileSystemError_NotADirectory(message: 'Not a directory: $path');
    }

    // Check if directory is empty if not recursive
    if (!recursive) {
      final children = _entries.keys.where((p) => p.startsWith('$normalizedPath/') && p != normalizedPath).toList();
      if (children.isNotEmpty) {
        throw FileSystemError_DirectoryNotEmpty(message: 'Directory not empty: $path');
      }
    }

    // Remove the directory and its children if recursive
    _entries.removeWhere((p, e) => p == normalizedPath || (recursive && p.startsWith('$normalizedPath/')));
  }

  @override
  Future<void> setTimestamps(RawPath path, {DateTime? createTime, DateTime? modifyTime, DateTime? accessTime}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      throw FileSystemError_NotFound(message: 'Path not found: $path');
    }

    _entries[normalizedPath] = _FileSystemEntry(
      content: entry.content,
      createDate: createTime ?? entry.createDate,
      updateDate: modifyTime ?? entry.updateDate,
      accessDate: accessTime ?? entry.accessDate,
      isDirectory: entry.isDirectory,
    );
  }

  @override
  Future<Path> stat(RawPath path, {domain.PathStatsType fallbackType = domain.PathStatsType.unknown}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      // Return a fake Path with unknown type for non-existent paths
      return Path(
        path, // Use the RawPath object as positional argument
        PathStats(
          // Create PathStats
          type: domain.PathStatsType.unknown,
          createTime: zeroDate,
          updateTime: zeroDate,
          accessTime: zeroDate,
          size: null,
          error: FileSystemError_NotFound(message: 'Path not found: $path'),
        ),
      );
    }

    // Return a fake Path object with basic stats
    return Path(
      path, // Use the RawPath object as positional argument
      PathStats(
        // Create PathStats
        type: entry.isDirectory ? domain.PathStatsType.directory : domain.PathStatsType.file,
        createTime: entry.createDate,
        updateTime: entry.updateDate,
        accessTime: entry.accessDate,
        size: entry.isDirectory ? 0 : entry.content?.length ?? 0,
      ),
    );
  }

  @override
  Future<void> deleteFile(RawPath path) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      throw FileSystemError_NotFound(message: 'File not found: $path');
    }

    if (entry.isDirectory) {
      throw FileSystemError_PermissionDenied(message: 'Is a directory: $path');
    }

    _entries.remove(normalizedPath);
  }

  @override
  Future<void> writeFile(RawPath path, String content) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry != null) {
      if (entry.isDirectory) {
        throw FileSystemError_PermissionDenied(message: 'Is a directory: $path');
      }
      // Actually remove the existing entry when overwriteIfExists is true
      _entries.remove(normalizedPath);
    }

    _createParentDirHierarchyIfNeeded(path); // Ensure parent hierarchy exists or is created

    _entries[normalizedPath] = _FileSystemEntry(
      content: Uint8List.fromList(utf8.encode(content)),
      createDate: DateTime.now(), // Simulate creation date
      updateDate: DateTime.now(), // Simulate update date
      isDirectory: false,
    );
  }

  // Helper method to add entries to the fake file system for testing setup
  void addEntry(RawPath path, {bool isDirectory = false, String? content}) {
    final normalizedPath = _normalizePath(path);
    _createParentDirHierarchyIfNeeded(path); // Ensure parent hierarchy exists or is created
    _entries[normalizedPath] = _FileSystemEntry(
      isDirectory: isDirectory,
      content: content != null ? Uint8List.fromList(content.codeUnits) : null,
      createDate: DateTime.now(),
      updateDate: DateTime.now(),
    );
  }

  // Helper method to remove entries from the fake file system for testing
  void removeEntry(RawPath path) {
    final normalizedPath = _normalizePath(path);
    _entries.remove(normalizedPath);
  }

  // Helper method to clear the fake file system
  void clear() {
    _entries.clear();
  }


}

/// Represents an entry in the fake file system.
class _FileSystemEntry {
  Uint8List? content;
  DateTime createDate;
  DateTime updateDate;
  DateTime accessDate;
  bool isDirectory;
  int get size => isDirectory ? 0 : (content?.length ?? 0);

  _FileSystemEntry({
    this.content,
    DateTime? createDate,
    DateTime? updateDate,
    DateTime? accessDate,
    this.isDirectory = false,
  })  : createDate = createDate ?? DateTime.now(),
        updateDate = updateDate ?? DateTime.now(),
        accessDate = accessDate ?? DateTime.now();
}

// Zero date for unknown files
final DateTime zeroDate = DateTime.utc(1970, 1, 1);

/// Mock implementation of CopyWithProgressOperation for testing
class _MockCopyWithProgressOperation implements CopyWithProgressOperation {
  final RawPath source;
  final RawPath dest;
  final String normalizedSource;
  final String normalizedDest;
  final CopyOptions options;
  final FutureOr<void> Function(ProgressReport) onProgress;
  final int chunkSize;
  final Map<String, _FileSystemEntry> entries;

  bool _isPaused = false;
  final Completer<void> _completer = Completer<void>();

  _MockCopyWithProgressOperation({
    required this.source,
    required this.dest,
    required this.normalizedSource,
    required this.normalizedDest,
    required this.options,
    required this.onProgress,
    required this.chunkSize,
    required this.entries,
  }) {
    // Use a more robust async error handling pattern.
    // The _performCopy method is responsible for completing the completer.
    Future.microtask(_performCopy);
  }

  @override
  Future<void> finished() => _completer.future;

  bool get isPaused => _isPaused;

  bool get isCompleted => _completer.isCompleted;

  @override
  Future<void> pause() async {
    assert(!_completer.isCompleted, 'Cannot pause: operation is already completed');
    assert(!_isPaused, 'Cannot pause: operation is already paused');

    _isPaused = true;
  }

  @override
  Future<void> resume() async {
    assert(!_completer.isCompleted, 'Cannot resume: operation is already completed');
    assert(_isPaused, 'Cannot resume: operation is not paused');

    _isPaused = false;
    // The _performCopy method will continue from where it was paused
  }

  @override
  Future<void> cancel() async {
    // Allow multiple cancel calls - just ignore if already completed
    if (_completer.isCompleted) {
      return;
    }

    _isPaused = false;
    // Use a microtask to ensure the error is reported in a separate event loop turn.
    Future.microtask(() => _completer.completeError(CancellationError()));
  }

  Future<void> _performCopy() async {
    try {
      // Validate source file
      final sourceEntry = entries[normalizedSource];
      if (sourceEntry == null || sourceEntry.isDirectory) {
        throw FileSystemError_NotFound(message: 'Source file not found: ${source.absolutePath}');
      }

      // Validate destination
      final destEntry = entries[normalizedDest];
      if (destEntry != null) {
        if (destEntry.isDirectory) {
          throw FileSystemError_IsADirectory(message: 'Is a directory: $dest');
        } else if (!options.overwriteIfExists) {
          throw FileSystemError_AlreadyExists(message: 'File already exists: $dest');
        }
      }
      // Create the entry now so it exists during the copy
      final now = DateTime.now();
      entries[normalizedDest] = _FileSystemEntry(
        content: Uint8List(0),
        createDate: now,
        updateDate: now,
        accessDate: now,
        isDirectory: false,
      );

      final content = sourceEntry.content ?? Uint8List(0);
      final totalSize = content.length;

      // Calculate expected number of chunks
      final expectedChunks = totalSize == 0 ? 1 : (totalSize / chunkSize).ceil();

      // Simulate progress in chunks
      int bytesProcessed = 0;

      // Process in chunks and report progress
      for (int chunkIndex = 0; chunkIndex < expectedChunks; chunkIndex++) {
        if (_completer.isCompleted) return;

        while (_isPaused && !_completer.isCompleted) {
          await Future.delayed(const Duration(milliseconds: 10));
        }

        if (_completer.isCompleted) return;

        // Check that source file still exists
        if (!entries.containsKey(normalizedSource)) {
          // Complete the future with an error instead of throwing to avoid uncaught async errors in tests.
          _completer.completeError(FileSystemError.notFound(
            message: 'Source file was deleted during copy: ${source.absolutePath}',
          ));
          return; // Stop the operation
        }

        bytesProcessed = math.min((chunkIndex + 1) * chunkSize, totalSize);
        await onProgress(ProgressReport(bytesCopied: BigInt.from(bytesProcessed)));
      }

      // If we got here, the copy is successful. Finalize the destination entry.
      final finalDestEntry = entries[normalizedDest]!;
      finalDestEntry.content = content;
      if (options.preserveTimestamps) {
        finalDestEntry.createDate = sourceEntry.createDate;
        finalDestEntry.updateDate = sourceEntry.updateDate;
        finalDestEntry.accessDate = sourceEntry.accessDate;
      } else {
        finalDestEntry.updateDate = DateTime.now();
      }

      _completer.complete();
    } catch (e, st) {
      if (!_completer.isCompleted) {
        _completer.completeError(e, st);
        return; // Prevent re-throwing the error into the zone.
      }
    }
  }
}
