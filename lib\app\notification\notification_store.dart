import 'package:flutter/foundation.dart';
import 'package:mobx/mobx.dart';

import '../util/log_utils.dart';

part '.gen/notification_store.g.dart';

/// Represents a notification type
enum NotificationType {
  error,
  warning,
}

/// Represents a notification
class Notification {
  /// The message of the notification
  final String message;

  /// The type of the notification
  final NotificationType type;

  /// Creates a new notification
  Notification({
    required this.message,
    required this.type,
  });
}

/// Store for managing notifications
class NotificationStore = NotificationStoreBase with _$NotificationStore;

abstract class NotificationStoreBase with Store {
  /// The list of notifications
  @observable
  ObservableList<Notification> notifications = ObservableList<Notification>();

  /// Notifies an error
  @action
  void notifyError(Object error) {
    if (kDebugMode) {
      logger.severe(error);
    }
    _notifyErrorOrString(error, NotificationType.error);
  }

  /// Notifies a warning
  @action
  void notifyWarn(Object warning) {
    if (kDebugMode) {
      logger.warning(warning);
    }
    _notifyErrorOrString(warning, NotificationType.warning);
  }

  /// Notifies an error or string
  void _notifyErrorOrString(Object errorOrString, NotificationType type) {
    final message = errorOrString is Error ? errorOrString.toString() : errorOrString.toString();
    addNotification(Notification(message: message, type: type));
  }

  /// Adds a notification
  @action
  void addNotification(Notification notification) {
    notifications.add(notification);
  }

  /// Clears all notifications
  @action
  void clearNotifications() {
    notifications.clear();
  }

  @visibleForTesting
  @action
  void dispose() {
    notifications.clear();
  }

  static final logger = loggerFor(NotificationStoreBase);
}
