import 'package:class_to_string/class_to_string.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part '.gen/command_context.freezed.dart';

/// Represents the context in which a command is executed
@immutable
@freezed
class CommandContext with _$CommandContext {
  const CommandContext({
    this.inFileList = false,
    this.inNavigationBarInput = false,
    this.inDialog = false,
    this.inContextMenu = false,
    this.inSearch = false,
    this.inCommandPalette = false,
    this.isRenaming = false,
  });

  static const defaultContext = CommandContext(
    inFileList: true,
    inDialog: false,
    inContextMenu: false,
    inSearch: false,
    inCommandPalette: false,
    isRenaming: false,
  );

  /// Whether the file list has focus
  @override
  final bool inFileList;

  /// Whether the navigation bar input has focus
  @override
  final bool inNavigationBarInput;

  /// Whether a dialog is open
  @override
  final bool inDialog;

  /// Whether a context menu is open
  @override
  final bool inContextMenu;

  /// Whether the search is active
  @override
  final bool inSearch;

  /// Whether the command palette is open
  @override
  final bool inCommandPalette;

  /// Whether a file is being renamed
  @override
  final bool isRenaming;

  @override
  String toString() => (ClassToString.flat('CommandContext')
        ..addIfExist('inFileList', inFileList ? true : null)
        ..addIfExist('inDialog', inDialog ? true : null)
        ..addIfExist('inContextMenu', inContextMenu ? true : null)
        ..addIfExist('inSearch', inSearch ? true : null)
        ..addIfExist('inCommandPalette', inCommandPalette ? true : null)
        ..addIfExist('isRenaming', isRenaming ? true : null))
      .toString();
}
