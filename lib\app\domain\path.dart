import 'dart:io' show Platform;

import 'package:collection/collection.dart';
import 'package:dartx/dartx.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:path/path.dart' as p;

import '../../rust/.gen/api/domain.dart';
import '../util/string_utils.dart';

part '.gen/path.freezed.dart';

@immutable
class RawPath implements Comparable<RawPath> {
  final String absolutePath;

  RawPath(String absolutePath) : absolutePath = Platform.isWindows ? absolutePath.capitalize() : absolutePath;

  // FIXME: This is the reason that paths are getting lowercased. Why was this necessary again..?
  // factory Path.resolve(String absolutePath) => Path(p.canonicalize(absolutePath));
  factory RawPath.resolve(String absolutePath) => RawPath(p.normalize(absolutePath));

// private _nativeAbsolutePath?: string
// get nativeAbsolutePath(): string {
// // This is more efficient than a lazyValue when there are lots of files involved
// if (!this._nativeAbsolutePath) {
// this._nativeAbsolutePath = nativePath.normalize(this.absolutePath)
// }
// return this._nativeAbsolutePath
// }

  // 'path' doesn't consider C: to be a root path, but we do.
  bool get _isWindowsShortRoot => Platform.isWindows && absolutePath.length == 2 && absolutePath[1] == ':';

  late final String root = _isWindowsShortRoot ? absolutePath : p.rootPrefix(absolutePath);
  late final String dir = p.dirname(absolutePath);
  late final String name = p.basename(absolutePath);
  late final String nameWithoutExtension = p.withoutExtension(absolutePath);
  late final String extension = p.extension(absolutePath);
  late final bool isRoot = absolutePath == root;

  late final List<String> elementNames = p.split(absolutePath);
  late final List<RawPath> elements = (() {
    RawPath? currentDir;
    return elementNames.map((e) {
      currentDir = currentDir != null ? currentDir!.child(e) : RawPath(p.join(root, e));
      return currentDir!;
    }).toList();
  })();

  late final RawPath? parent = isRoot ? null : RawPath(dir);

  RawPath child(String name) => RawPath(p.join(absolutePath, name));
  RawPath sibling(String name) => RawPath(p.join(dir, name));
  RawPath append(String addition) => RawPath(absolutePath + addition);

  bool isParentOf(RawPath path) => absolutePath == path.dir && !path.isRoot;
  bool isChildOf(RawPath path) => path.isParentOf(this);
  bool isAncestorOf(RawPath path) => this == path || path.absolutePath.startsWith(p.join(absolutePath, p.separator));
  bool isSiblingOf(RawPath path) => dir == path.dir;
  bool startsWith(String prefix) => absolutePath.startsWith(prefix);
  bool endsWith(String suffix) => absolutePath.endsWithReversed(suffix);

  // From this path -> target path
  RawPath relativeTo(RawPath path) => RawPath(p.relative(path.absolutePath, from: absolutePath));
  //
  // // From this path -> target path
  // List<Path> relativeTo(Path path) {
  //   final elements = p.split(p.relative(path.absolutePath, from: absolutePath));
  //   Path currentDir = this;
  //   return elements.map((e) {
  //     currentDir = currentDir.child(e);
  //     return currentDir;
  //   }).toList();
  // }

  Path withStats(PathStats stats) => Path(this, stats);

  RawPath resolved() => RawPath.resolve(absolutePath);

  @override
  int compareTo(RawPath other) => (Platform.isWindows ? absolutePath.compareToIgnoreCase : absolutePath.compareTo)(other.absolutePath);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RawPath &&
          runtimeType == other.runtimeType &&
          // TODO: This only ignores ascii case, non-ascii things will not behave correctly. But let those be my problems.
          (Platform.isWindows ? absolutePath.equalsIgnoreCase(other.absolutePath) : absolutePath == other.absolutePath);

  @override
  int get hashCode {
    // On windows the equality operator is case insensitive,
    // so we must have the hashCode produce the same hash for the same path with different casing.
    // This might produce slightly bigger lists in hashmaps when the same path with different casing is used, but that's not a big deal.
    // TODO: This only ignores ascii case, non-ascii things will not behave correctly. But let those be my problems.
    return hashIgnoreAsciiCase(absolutePath);
  }

  @override
  String toString() => "[$absolutePath]";

  static final currentWorkingDir = RawPath.resolve(p.current);
  static final Null = RawPath("");
  static final String pathSeparator = Platform.pathSeparator;
// static nativePathSeparator = nativePath.sep

  factory RawPath.fromJson(String json) => RawPath.resolve(json);
  String toJson() => absolutePath;
}

@immutable
@freezed
class Path with _$Path, PathUtils, StatsUtils implements Comparable<Path> {
  const Path(this.path, this.stats);

  factory Path.fake(RawPath path, bool isDirectory) => Path(path, PathStats.fake(isDirectory));

  @override
  final RawPath path;

  @override
  final PathStats stats;

  Path resolved() => copyWith(path: path.resolved(), stats: stats);
  Path withSize(int size) => copyWith(stats: stats.withSize(size));
  Path withError(Exception error) => copyWith(stats: stats.withError(error));

  @override
  int compareTo(Path other) => path.compareTo(other.path);

  @override
  String toString() => isDirectory ? path.toString() : path.absolutePath.toString();
}

@immutable
@freezed
class PathStats with _$PathStats {
  const PathStats({
    required this.type,
    required this.createTime,
    required this.updateTime,
    required this.accessTime,
    required this.size,
    this.error,
  });

  factory PathStats.fake(bool isDirectory) => isDirectory ? fakeDir : fakeFile;

  static final PathStats fakeFile = PathStats(
    type: PathStatsType.file,
    createTime: zeroDate,
    updateTime: zeroDate,
    accessTime: zeroDate,
    size: null,
    error: null,
  );

  static final PathStats fakeDir = fakeFile.copyWith(type: PathStatsType.directory);

  @override
  final PathStatsType type;

  @override
  final DateTime createTime;

  @override
  final DateTime updateTime;

  @override
  final DateTime accessTime;

  @override
  final int? size;

  @override
  final Exception? error;

  bool get isFile => type == PathStatsType.file || type == PathStatsType.socket || type == PathStatsType.pipe;
  bool get isDirectory => type == PathStatsType.directory;
  bool get isSymbolicLink => type == PathStatsType.link;
  bool get isSocket => type == PathStatsType.socket;
  bool get isPipe => type == PathStatsType.pipe;
  bool get isError => type == PathStatsType.unknown;

  PathStats withSize(int size) => copyWith(size: size);
  PathStats withError(Exception error) => copyWith(error: error);
}

extension StringExtensions on String {
  RawPath asPath({bool resolve = false}) => resolve ? RawPath.resolve(this) : RawPath(this);
}

final zeroDate = DateTime.fromMicrosecondsSinceEpoch(0);

abstract mixin class PathUtils {
  RawPath get path;

  String get absolutePath => path.absolutePath;
  String get root => path.root;
  String get dir => path.dir;
  String get name => path.name;
  String get nameWithoutExtension => path.nameWithoutExtension;
  String get extension => path.extension;
  bool get isRoot => path.isRoot;
  List<String> get elementNames => path.elementNames;
  List<RawPath> get elements => path.elements;
  RawPath? get parent => path.parent;
}

abstract mixin class StatsUtils {
  PathStats get stats;

  PathStatsType get type => stats.type;
  bool get isFile => stats.isFile;
  bool get isDirectory => stats.isDirectory;
  bool get isSymbolicLink => stats.isSymbolicLink;
  bool get isSocket => stats.isSocket;
  bool get isPipe => stats.isPipe;
  bool get isError => stats.isError;
  DateTime get createTime => stats.createTime;
  DateTime get updateTime => stats.updateTime;
  DateTime get accessTime => stats.accessTime;
  int? get size => stats.size;
  Exception? get error => stats.error;
}
