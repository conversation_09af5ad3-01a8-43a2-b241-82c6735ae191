import 'package:flutter/foundation.dart';

import '../../app/util/log_utils.dart';
import 'command_context.dart';

/// Repository for managing command contexts
class CommandContextRepository {
  CommandContextRepository();

  // Stack of contexts
  final List<CommandContext> _contextStack = [CommandContext.defaultContext];

  // Current context (top of the stack)
  CommandContext get currentContext => _contextStack.last;

  /// Push a new context onto the stack
  void pushContext(CommandContext context) {
    if (kDebugMode) {
      logger.finer('pushContext($context)');
    }
    _contextStack.add(context);
    _logContextChange();
  }

  /// Pop the top context from the stack
  CommandContext popContext([CommandContext? expectedContext]) {
    if (_contextStack.length <= 1) {
      if (kDebugMode) {
        logger.warning('popContext(expectedContext=$expectedContext): Attempted to pop the last context from the stack');
      }
      CommandContext popped = _contextStack.last;
      if (kDebugMode) {
        assert(expectedContext == null || popped == expectedContext, 'Expected context $expectedContext, but got $popped');
      }
      return popped;
    }

    final poppedContext = _contextStack.removeLast();
    if (kDebugMode) {
      assert(expectedContext == null || poppedContext == expectedContext, 'Expected context $expectedContext, but got $poppedContext');
      logger.finer('popContext(expectedContext=$expectedContext): Popped context');
    }
    _logContextChange();
    return poppedContext;
  }

  /// Update the current context
  void updateContext(CommandContext newContext) {
    final updatedContext = currentContext.copyWith(
      inFileList: newContext.inFileList,
      inDialog: newContext.inDialog,
      inContextMenu: newContext.inContextMenu,
      inSearch: newContext.inSearch,
      inCommandPalette: newContext.inCommandPalette,
      isRenaming: newContext.isRenaming,
    );

    _contextStack[_contextStack.length - 1] = updatedContext;
    _logContextChange();
  }

  /// Reset the context stack to the default context
  void resetContext() {
    _contextStack.clear();
    _contextStack.add(CommandContext.defaultContext);
    if (kDebugMode) {
      logger.finer('resetContext(): Reset context to default');
    }
  }

  /// Log the current context
  void _logContextChange() {
    if (kDebugMode) {
      logger.finer('_logContextChange() Context updated: $_contextStack[${_contextStack.length - 1}]');
    }
  }

  @visibleForTesting
  void dispose() {}

  static final logger = loggerFor(CommandContextRepository, Level.INFO);
}
