import 'dart:math';

import 'package:dartx/dartx.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part '.gen/quick_score.freezed.dart';

const defaultConfig = QuickScoreConfig();

// FIXME: Finish this file.
/// A class for scoring and sorting a list of items against a query string.  Each
/// item receives a floating point score between `0` and `1`.
///
/// [items] The list of items to score.  If
/// the list is not a flat array of strings, a `keys` array must be supplied
/// via the second parameter.  QuickScore makes a shallow copy of the `items`
/// array, so changes to it won't have any affect, but changes to the objects
/// referenced by the array need to be passed to the instance by a call to
/// its [setItems()]{@link QuickScore#setItems} method.
///
/// @param {Array<ItemKey>|Options} [options]  If the `items` parameter
/// is an array of flat strings, the `options` parameter can be left out.  If
/// it is a list of objects containing keys that should be scored, the
/// `options` parameter must either be an array of key names or an object
/// containing a `keys` property.
///
/// @param {Array<ItemKey>} [options.keys]  In the simplest case, an array of
/// key names to score on the objects in the `items` array.
///
/// The key names can point to a nested key by passing either a dot-delimited
/// string or an array of sub-keys that specify the path to the value.  So a
/// key `name` of `"foo.bar"` would evaluate to `"baz"` given an object like
/// `{ foo: { bar: "baz" } }`.  Alternatively, that path could be passed as
/// an array, like `["foo", "bar"]`.  In either case, if this sub-key's match
/// produces the highest score for an item in the search results, its
/// `scoreKey` name will be `"foo.bar"`.
///
/// If your items have keys that contain periods, e.g., `"first.name"`, but
/// you don't want these names to be treated as paths to nested keys, simply
/// wrap the name in an array, like `{ keys: ["ssn", ["first.name"],
/// ["last.name"]] }`.
///
/// Instead of a string or string array, an item in `keys` can also be passed
/// as a `{name, scorer}` object, which lets you specify a different scoring
/// function for each key.  The scoring function should behave as described
/// next.
///
/// @param {string} [options.sortKey=options.keys[0]]  An optional key name
/// that will be used to sort items with identical scores.  Defaults to the
/// name of the first item in the `keys` parameter.  If `sortKey` points to
/// a nested key, use a dot-delimited string instead of an array to specify
/// the path.
///
/// @param {number} [options.minimumScore=0]  An optional value that
/// specifies the minimum score an item must have to appear in the results
/// returned from [search()]{@link QuickScore#search}.  Defaults to `0`,
/// so items that don't match the full `query` will not be returned.  This
/// value is ignored if the `query` is empty or undefined, in which case all
/// items are returned, sorted alphabetically and case-insensitively on the
/// `sortKey`, if any.
///
/// @param {TransformStringFunction} [options.transformString]  An optional
/// function that takes a `string` parameter and returns a transformed
/// version of that string.  This function will be called on each of the
/// searchable keys in the `items` array as well as on the `query`
/// parameter to the `search()` method.  The default function calls
/// `toLocaleLowerCase()` on each string, for a case-insensitive search.  The
/// result of this function is cached for each searchable key on each item.
///
/// You can pass a function here to do other kinds of preprocessing, such as
/// removing diacritics from all the strings or converting Chinese characters
/// to pinyin.  For example, you could use the
/// [`latinize`](https://www.npmjs.com/package/latinize) npm package to
/// convert characters with diacritics to the base character so that your
/// users can type an unaccented character in the query while still matching
/// items that have accents or diacritics.  Pass in an `options` object like
/// this to use a custom `transformString()` function:
/// `{ transformString: s => latinize(s.toLocaleLowerCase()) }`
///
/// @param {ScorerFunction} [options.scorer]  An optional function that takes
/// `string` and `query` parameters and returns a floating point number
/// between 0 and 1 that represents how well the `query` matches the
/// `string`.  It defaults to the [quickScore()]{@link quickScore} function
/// in this library.
///
/// If the function gets a third `matches` parameter, it should fill the
/// passed-in array with indexes corresponding to where the query
/// matches the string, as described in the [search()]{@link QuickScore#search}
/// method.
///
/// @param {Config} [options.config]  An optional object that is passed to
/// the scorer function to further customize its behavior.  If the
/// `scorer` function has a `createConfig()` method on it, the `QuickScore`
/// instance will call that with the `config` value and store the result.
/// This can be used to extend the `config` parameter with default values.
@immutable
class QuickScore<T> {
  final List<T> items;
  // final List<QuickScoreFieldDef<T>> fields;
  final Map<String, String Function(T item)> matchableValueExtractors;
  final String Function(String) transformString;
  final int Function(T item1, T item2)? sortTieBreaker;
  final QuickScoreConfig config;

  // FIXME: fieldValues;
  final List<Map<String, String>> _matchableValues;

  QuickScore(
    this.items,
    this.matchableValueExtractors, {
    this.transformString = _defaultTransformString,
    this.sortTieBreaker,
    this.config = defaultConfig,
  })  : assert(matchableValueExtractors.isNotEmpty, 'matchableValueExtractors must not be empty'),
        _matchableValues = items.map((item) {
          return matchableValueExtractors.mapValues((entry) => transformString(entry.value(item)));
        }).toList();

  static QuickScore<String> ofString(
    List<String> items, {
    String Function(String) transformString = _defaultTransformString,
    int Function(String item1, String item2)? sortTieBreaker,
    QuickScoreConfig config = defaultConfig,
  }) {
    return QuickScore<String>(
      items,
      {'value': (item) => item},
      transformString: transformString,
      sortTieBreaker: sortTieBreaker,
      config: config,
    );
  }

  /// Scores the instance's items against the `query` and sorts them from
  /// highest to lowest.
  ///
  /// @param {string} query  The string to score each item against.  The
  /// instance's `transformString()` function is called on this string before
  /// it's matched against each item.
  ///
  /// @returns {Array<ScoredString|ScoredObject>}  When the instance's `items`
  /// are flat strings, an array of [`ScoredString`]{@link ScoredString}
  /// objects containing the following properties is returned:
  ///
  /// - `item`: the string that was scored
  /// - `score`: the floating point score of the string for the current query
  /// - `matches`: an array of arrays that specify the character ranges
  ///   where the query matched the string
  ///
  /// When the `items` are objects, an array of [`ScoredObject`]{@link ScoredObject}
  /// results is returned:
  ///
  /// - `item`: the object that was scored
  /// - `score`: the highest score from among the individual key scores
  /// - `scoreKey`: the name of the key with the highest score, which will be
  ///   an empty string if they're all zero
  /// - `scoreValue`: the value of the key with the highest score, which makes
  ///   it easier to access if it's a nested string
  /// - `scores`: a hash of the individual scores for each key
  /// - `matches`: a hash of arrays that specify the character ranges of the
  ///   query match for each key
  ///
  /// The results array is sorted high to low on each item's score.  Items with
  /// identical scores are sorted alphabetically and case-insensitively on the
  /// `sortKey` option.  Items with scores that are <= the `minimumScore` option
  /// (defaults to `0`) are not returned, unless the `query` is falsy, in which
  /// case all of the items are returned, sorted alphabetically.
  ///
  /// The start and end indices in each [`RangeTuple`]{@link RangeTuple} in the
  /// `matches` array can be used as parameters to the `substring()` method to
  /// extract the characters from each string that match the query.  This can
  /// then be used to format the matching characters with a different color or
  /// style.
  ///
  /// Each `ScoredObject` item also has a `_` property, which caches transformed
  /// versions of the item's strings, and might contain additional internal
  /// metadata in the future.  It can be ignored.
  // Scores the instance's items against the `query` and sorts them from highest to lowest.
  List<QuickScoreResult<T>> search(String query) {
    final transformedQuery = transformString(query);
    if (transformedQuery.isEmpty) {
      return items.mapIndexed((i, item) {
        final entry = _matchableValues[i].entries.first;
        return QuickScoreResult<T>(item,
            key: entry.key, score: 1.0, matches: [Match(start: 0, end: entry.value.length)]);
      }).toList();
    }

    List<QuickScoreResult<T>> results = [];
    for (int i = 0, length = items.length; i < length; i++) {
      final item = items[i];
      double highScore = 0;
      String highScoreKey = "";
      List<Match> highScoreMatches = const [];

      // find the highest score for each searchableValue on this item
      for (final searchableValue in _matchableValues[i].entries) {
        final transformedString = searchableValue.value;
        if (transformedString.isEmpty) {
          continue;
        }

        final key = searchableValue.key;
        final originalString = matchableValueExtractors[key]!(item);
        List<Match> matches = [];
        final score = quickScore(
          string: originalString,
          query: query,
          matches: matches,
          transformedString: transformedString,
          transformedQuery: transformedQuery,
        );

        if (score > highScore) {
          highScore = score;
          highScoreKey = key;
          highScoreMatches = matches;
        }
      }

      if (highScore > config.minimumScore && highScoreKey.isNotEmpty) {
        results.add(QuickScoreResult<T>(item, key: highScoreKey, score: highScore, matches: highScoreMatches));
      }
    }

    if (results.length > 1) {
      // Sort the results based on score.
      results.sort(_compareResults);
    }
    return results;
  }

  /// Compares two items based on their scores, or on their `sortKey` if the
  /// scores are identical.
  int _compareResults(QuickScoreResult<T> a, QuickScoreResult<T> b) {
    int result = b.compareTo(a);
    return result != 0 ? result : sortTieBreaker?.call(a.item, b.item) ?? 0;
  }

  // QuickScoreResult<T> _quickScore({
  //   required T item,
  //   required String key,
  //   required String originalQuery,
  //   required String transformedQuery,
  //   required String transformedString,
  // }) {
  //   final transformedString = searchableValues[key];
  //   final originalString = matchableValueExtractors[key]!(item);
  //   List<Match> matches = [];
  //   return quickScore(
  //       string: originalString,
  //       query: query,
  //       matches: matches,
  //       transformedString: transformedString,
  //       transformedQuery: transformedQuery);
  // }

  /// Scores a string against a query.
  ///
  /// [string] The string to score.
  ///
  /// [query] The query string to score the string parameter against.
  ///
  /// [matches] If supplied, quickScore() will push onto matches an array with
  /// start and end indexes for each substring range of string that matches query.
  /// These indexes can be used to highlight the matching characters in an auto-complete UI.
  ///
  /// [transformedString] A transformed version of the string that will be used for matching.
  /// This defaults to a lowercase version of string, but it could also be used to match
  /// against a string with all the diacritics removed, so an unaccented character in the query
  /// would match an accented one in the string.
  ///
  /// [transformedQuery] A transformed version of query. The same transformation applied to
  /// transformedString should be applied to this parameter, or both can be left as undefined
  /// for the default lowercase transformation.
  ///
  /// [stringRange] The range of characters in string that should be checked for matches
  /// against query. Defaults to the entire string parameter.
  ///
  /// Returns a number between 0 and 1 that represents how well the query matches the
  double quickScore({
    required String string,
    required String query,
    List<Match>? matches,
    String? transformedString,
    String? transformedQuery,
    Range? stringRange,
  }) {
    final transformedStringOrFallback = transformedString ?? transformString(string);
    final transformedQueryOrFallback = transformedQuery ?? transformString(query);
    stringRange ??= Range(location: 0, length: string.length);

    int iterations = 0;

    double calcScore({required Range searchRange, required Range queryRange, required Range fullMatchedRange}) {
      if (queryRange.isEmpty) {
        // deduct some points for all remaining characters
        return config.ignoredScore;
      } else if (queryRange.length > searchRange.length) {
        return 0;
      }

      final initialMatchesLength = matches?.length ?? 0;

      for (var i = queryRange.length; i > 0; i--) {
        if (iterations > config.maxIterations) {
          // a long query that matches the string except for the last
          // character can generate 2^queryLength iterations of this
          // loop before returning 0, so short-circuit that when we've
          // seen too many iterations (bit of an ugly kludge, but it
          // avoids locking up the UI if the user somehow types an
          // edge-case query)
          return 0;
        }

        iterations++;

        final querySubstring = transformedQueryOrFallback.substring(queryRange.location, queryRange.location + i);
        // reduce the length of the search range by the number of chars
        // we're skipping in the query, to make sure there's enough string
        // left to possibly contain the skipped chars
        final matchedRange = getRangeOfSubstring(
          transformedStringOrFallback,
          querySubstring,
          Range(location: searchRange.location, length: searchRange.length - queryRange.length + i),
        );

        if (!matchedRange.isValid) {
          // we didn't find the query substring, so try again with a
          // shorter substring
          continue;
        }

        if (!fullMatchedRange.isValid) {
          fullMatchedRange.location = matchedRange.location;
        } else {
          fullMatchedRange.location = min(fullMatchedRange.location, matchedRange.location);
        }
        fullMatchedRange.length = matchedRange.max() - fullMatchedRange.location;

        matches?.add(matchedRange.asMatch());

        final remainingSearchRange = Range(location: matchedRange.max(), length: searchRange.max() - matchedRange.max());
        final remainingQueryRange = Range(location: queryRange.location + i, length: queryRange.length - i);
        final remainingScore = calcScore(searchRange: remainingSearchRange, queryRange: remainingQueryRange, fullMatchedRange: fullMatchedRange);

        if (remainingScore > 0.0) {
          double score = (remainingSearchRange.location - searchRange.location).toDouble();
          // default to true since we only want to apply a discount if
          // we hit the final else clause below, and we won't get to
          // any of them if the match is right at the start of the
          // searchRange
          bool skippedSpecialChar = true;
          final useSkipReduction =
              config.useSkipReduction(string, query, remainingScore, searchRange, remainingSearchRange, matchedRange, fullMatchedRange);

          if (matchedRange.location > searchRange.location) {
            // some letters were skipped when finding this match, so
            // adjust the score based on whether spaces or capital
            // letters were skipped
            if (useSkipReduction && config.wordSeparators.contains(string[matchedRange.location - 1])) {
              for (var j = matchedRange.location - 2; j >= searchRange.location; j--) {
                if (config.wordSeparators.contains(string[j])) {
                  score--;
                } else {
                  score -= config.skippedScore;
                }
              }
            } else if (useSkipReduction && config.uppercaseLetters.contains(string[matchedRange.location])) {
              for (var j = matchedRange.location - 1; j >= searchRange.location; j--) {
                if (config.uppercaseLetters.contains(string[j])) {
                  score--;
                } else {
                  score -= config.skippedScore;
                }
              }
            } else {
              // reduce the score by the number of chars we've
              // skipped since the beginning of the search range
              score -= matchedRange.location - searchRange.location;
              skippedSpecialChar = false;
            }
          }

          score += config.adjustRemainingScore(
            string,
            query,
            remainingScore,
            skippedSpecialChar,
            searchRange,
            remainingSearchRange,
            matchedRange,
            fullMatchedRange,
          );
          score /= searchRange.length;

          return score;
        } else if (matches != null) {
          // the remaining query does not appear in the remaining
          // string, so strip off any matches we've added during the
          // current call, as they'll be invalid when we start over
          // with a shorter piece of the query
          matches.length = initialMatchesLength;
        }
      }

      return 0;
    }

    if (query.isNotEmpty) {
      return calcScore(
        searchRange: stringRange,
        queryRange: Range(location: 0, length: query.length),
        fullMatchedRange: Range(),
      );
    } else {
      return config.emptyQueryScore;
    }
  }

  QuickScore<T> withItems(List<T> items) {
    return QuickScore<T>(items, matchableValueExtractors, transformString: transformString, sortTieBreaker: sortTieBreaker, config: config);
  }
}

String _defaultTransformString(String string) => string.toLowerCase();

// FIXME: Use this
@immutable
@freezed
class QuickScoreFieldDef<T> with _$QuickScoreFieldDef<T> {
  const QuickScoreFieldDef(this.name, this.extractor);

  @override
  final String name;
  @override
  final String Function(T item) extractor;

}

@immutable
@freezed
class QuickScoreResult<T> with _$QuickScoreResult<T> implements Comparable<QuickScoreResult> {
  const QuickScoreResult(this.item, {required this.key, required this.score, required this.matches});

  @override
  final T item;

  @override
  final String key;

  @override
  final double score;

  @override
  final List<Match> matches;

  @override
  int compareTo(QuickScoreResult other) => score.compareTo(other.score);
}

Range getRangeOfSubstring(String string, String query, Range searchRange) {
  int index = string.indexOf(query, searchRange.location);
  Range result = Range();

  if (index > -1 && index < searchRange.max()) {
    result.location = index;
    result.length = query.length;
  }

  return result;
}

// A class representing a half-open interval of characters.  A range's `location`
// property and `max()` value can be used as arguments for the `substring()`
// method to extract a range of characters.
class Range {
  // Starting index of the range.
  int location;

  // Number of characters in the range.
  int length;

  // Starting index of the range.
  // Number of characters in the range.
  Range({this.location = -1, this.length = 0});

  // Gets the end index of the range, which indicates the character
  // immediately after the last one in the range.
  int max([int? value]) {
    if (value != null) {
      length = value - location;
    }

    // the NSMaxRange() function in Objective-C returns this value
    return location + length;
  }

  // Returns whether the range contains a location >= 0.
  bool get isValid => location > -1;

  Match asMatch() => Match(start: location, end: location + length);

  bool get isEmpty => length == 0;

  // Returns a string representation of the range's open interval.
  @override
  String toString() => location == -1 ? "invalid range" : "[$location, ${max()}]";
}

@immutable
@freezed
class Match with _$Match {
  const Match({required this.start, required this.end});

  @override
  final int start;

  @override
  final int end;
}

// // constants for the base configuration
// final BaseConfigDefaults = {
//   "wordSeparators": "-/\:()<>%._=&[]+ \t\n\r",
//   "uppercaseLetters": (() {
// // create an array of uppercase letters
//     final charCodeA = "A".codeUnitAt(0);
//     final uppercase = [];
//
//     for (int i = 0; i < 26; i++) {
//       uppercase.add(String.fromCharCode(charCodeA + i));
//     }
//
//     return uppercase.join();
//   })(),
//   "ignoredScore": 0.9,
//   "skippedScore": 0.15,
//   "emptyQueryScore": 0,
// // support worst-case queries up to 16 characters and then give up
// // and return 0 for longer queries that may or may not actually match
//   "maxIterations": pow(2, 16),
// };
//
// // finalants for QuickScore configuration
// final QSConfigDefaults = {
//   "longStringLength": 150,
//   "maxMatchStartPct": 0.15,
//   "minMatchDensityPct": 0.75,
//   "maxMatchDensityPct": 0.95,
//   "beginningOfStringPct": 0.1,
// };

// Base class for Configuration
// class Config {
//   final Map<String, dynamic> options;
//
//   Config(Map<String, dynamic> options) : options = Map.from(BaseConfigDefaults)..addAll(options);
//
//   bool useSkipReduction(String string, String query, double remainingScore, Range searchRange,
//           Range remainingSearchRange, Range matchedRange, Range fullMatchedRange) =>
//       true;
//
//   double adjustRemainingScore(
//       String string,
//       String query,
//       double remainingScore,
//       bool skippedSpecialChar,
//       Range searchRange,
//       Range remainingSearchRange,
//       Range matchedRange,
//       Range fullMatchedRange) {
//     // use the original Quicksilver expression for the remainingScore
//     return remainingScore * remainingSearchRange.length;
//   }
// }

class QuickScoreConfig {
  final double ignoredScore;
  final double skippedScore;
  final double emptyQueryScore;
  final double minimumScore;

  final int maxIterations;
  final int longStringLength;
  final double maxMatchStartPct;
  final double minMatchDensityPct;
  final double maxMatchDensityPct;
  final double beginningOfStringPct;

  final String wordSeparators;
  final String uppercaseLetters;

  const QuickScoreConfig({
    this.ignoredScore = 0.9,
    this.skippedScore = 0.15,
    this.emptyQueryScore = 0.0,
    this.minimumScore = 0.0,
    // support worst-case queries up to 16 characters and then give up
    // and return 0 for longer queries that may or may not actually match
    // 2^16 = 65k
    this.maxIterations = 2 << 16,
    this.longStringLength = 150,
    this.maxMatchStartPct = 0.15,
    this.minMatchDensityPct = 0.75,
    this.maxMatchDensityPct = 0.95,
    this.beginningOfStringPct = 0.1,
    this.wordSeparators = "-/:()<>%._=&[]+ \t\n\r",
    this.uppercaseLetters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
  });

  // Method that returns a boolean indicating whether or not to use skip reduction
  bool useSkipReduction(
    String string,
    String query,
    double remainingScore,
    Range searchRange,
    Range remainingSearchRange,
    Range matchedRange,
    Range fullMatchedRange,
  ) {
    int len = string.length;
    bool isShortString = len <= longStringLength;
    double matchStartPercentage = fullMatchedRange.location / len;

    return isShortString || matchStartPercentage < maxMatchStartPct;
  }

  double adjustRemainingScore(
    String string,
    String query,
    double remainingScore,
    bool skippedSpecialChar,
    Range searchRange,
    Range remainingSearchRange,
    Range matchedRange,
    Range fullMatchedRange,
  ) {
    bool isShortString = string.length <= longStringLength;
    double matchStartPercentage = fullMatchedRange.location / string.length;
    double matchRangeDiscount = 1;
    double matchStartDiscount = (1 - matchStartPercentage);

    // discount the remainingScore based on how much larger the match is
    // than the query, unless the match is in the first 10% of the
    // string, the match range isn't too sparse and the whole string is
    // not too long.  also only discount if we didn't skip any whitespace
    // or capitals.
    if (!skippedSpecialChar) {
      matchRangeDiscount = query.length / fullMatchedRange.length;
      matchRangeDiscount =
          (isShortString && matchStartPercentage <= beginningOfStringPct && matchRangeDiscount >= minMatchDensityPct) ? 1 : matchRangeDiscount;
      matchStartDiscount = matchRangeDiscount >= maxMatchDensityPct ? 1 : matchStartDiscount;
    }

    // discount the scores of very long strings
    return remainingScore * min(remainingSearchRange.length, longStringLength) * matchRangeDiscount * matchStartDiscount;
  }
}

// Config createConfig(options) {
//   if (options is Config) {
//     // this is a full-fledged Config instance, so we don't need to do
//     // anything to it
//     return options;
//   } else {
//     // create a complete config from this
//     return QuickScoreConfig(options);
//   }
// }

// final DefaultConfig = createConfig();
// final BaseConfig = Config();
// final QuicksilverConfig = Config(
// // the Quicksilver algorithm returns .9 for empty queries
//     emptyQueryScore: 0.9,
//     adjustRemainingScore: (String string,
//         String query,
//         double remainingScore,
//         bool skippedSpecialChar,
//         Range searchRange,
//         Range remainingSearchRange,
//         Range matchedRange,
//         Range fullMatchedRange) {
//       double score = remainingScore * remainingSearchRange.length;
//
//       if (!skippedSpecialChar) {
//         // the current QuickSilver algorithm reduces the score by half
//         // this value when no special chars are skipped, so add the half
//         // back in to match it
//         score += ((matchedRange.location - searchRange.location) / 2.0);
//       }
//
//       return score;
//     });
