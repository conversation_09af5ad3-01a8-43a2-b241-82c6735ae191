import 'dart:async';

import 'package:flutter/foundation.dart';

import '../domain/file.dart';
import '../domain/path.dart';
import '../file_system/file_repository.dart';
import '../util/duration_utils.dart';
import '../util/log_utils.dart';
import 'pending_file_store.dart';
import 'task.dart';

class JobPlanner {
  JobPlanner({
    required this.fileRepository,
    required this.pendingOperationRepository,
  });

  final FileRepository fileRepository;
  final PendingOperationRepository pendingOperationRepository;

  Future<Job> plan(JobSpec spec) async {
    final stopwatch = kDebugMode ? (Stopwatch()..start()) : null;
    final job = Job(spec: spec);
    if (kDebugMode) {
      logger.finest('plan($job): Planning...');
    }
    try {
      List<File> sources = const [];
      TaskType? taskType;
      RawPath? destination;
      String? newName;

      switch (spec) {
        case CopyJobSpec s:
          sources = s.sources;
          taskType = TaskType.copy;
          destination = s.destinationDir;
          break;
        case MoveJobSpec s:
          sources = s.sources;
          taskType = TaskType.move;
          destination = s.destinationDir;
          break;
        case RenameJobSpec s:
          sources = [s.source];
          taskType = TaskType.rename;
          newName = s.newName;
          break;
        case DeleteJobSpec s:
          sources = s.sources;
          taskType = TaskType.delete;
          break;
        case CreateJobSpec s:
          sources = [s.source];
          taskType = TaskType.create;
          break;
        case CalcDirectorySizeJobSpec s:
          sources = s.sources;
          taskType = TaskType.calcDirectorySize;
          break;
      }

      final buffer = pendingOperationRepository.createBuffer();
      List<Task> tasks = [];
      for (final file in sources) {
        final task = Task(job: job, file: file);
        final rootOp = await _buildOperationTree(
          task: task,
          source: file,
          taskType: taskType,
          buffer: buffer,
          parentDestination: destination,
          newName: newName,
        );
        task.rootOps.add(rootOp);
        tasks.add(task);
      }
      buffer.flush();
      job.tasks.addAll(tasks);
      if (kDebugMode) {
        stopwatch!.stop();
        logger.finest('plan($job) Finished: ${tasks.length} tasks [${stopwatch.elapsed.humanReadable}]');
      }
    } catch (e, stackTrace) {
      // FIXME: Report to notification store
      if (kDebugMode) {
        logger.severe('plan($job): Error', e, stackTrace);
      }
      job.setError(e);
    }
    return job;
  }

  Future<Operation> _buildOperationTree({
    required Task task,
    required File source,
    required TaskType taskType,
    required PendingOperationBuffer buffer,
    RawPath? parentDestination,
    String? newName,
    Operation? parentOperation,
  }) async {
    Operation op;
    int currentSize = source.isFile ? (source.stats.size ?? 0) : 0;
    RawPath? destination;

    switch (taskType) {
      case TaskType.copy:
        destination = parentDestination!.child(source.path.name);
        op = CopyOperation(
          task: task,
          source: source,
          destination: destination,
          parent: parentOperation,
          size: currentSize,
        );
      case TaskType.move:
        destination = parentDestination!.child(source.path.name);
        op = MoveOperation(
          task: task,
          source: source,
          destination: destination,
          parent: parentOperation,
          size: currentSize,
        );
      case TaskType.delete:
        op = DeleteOperation(
          task: task,
          source: source,
          parent: parentOperation,
          size: currentSize,
        );
      case TaskType.rename:
        op = RenameOperation(
          task: task,
          source: source,
          newName: newName!,
          parent: parentOperation,
        );
      case TaskType.create:
        op = CreateFileOperation(
          task: task,
          source: source,
          parent: parentOperation,
          size: source.size ?? 0,
        );
      case TaskType.calcDirectorySize:
        op = CalcDirectorySizeOperation(
          task: task,
          source: source,
          parent: parentOperation,
          size: currentSize,
        );
    }
    buffer.add(op);

    // Only build children if directory and operation is recursive
    if (source.isDirectory && op is! RenameOperation && op is! CreateFileOperation) {
      // FIXME: Use streaming here, dont pre-fetch all children.
      final children = await fileRepository.fetchDir(source.path).future;
      final List<Operation> childOps = [];
      for (final child in children) {
        final childOp = await _buildOperationTree(
          task: task,
          source: child,
          taskType: taskType,
          buffer: buffer,
          parentDestination: destination,
          parentOperation: op,
          newName: newName,
        );
        childOps.add(childOp);
      }
      op.setChildren(childOps);
    }
    return op;
  }

  @visibleForTesting
  void dispose() {
    fileRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    pendingOperationRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
  }

  static final logger = loggerFor(JobPlanner);
}
