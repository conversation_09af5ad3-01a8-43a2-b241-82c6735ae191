{"name": "ykrasik", "settings": "{\"settings\":\"{\\r\\n    \\\"terminal.integrated.shell.windows\\\": \\\"C:\\\\\\\\Program Files\\\\\\\\PowerShell\\\\\\\\7\\\\\\\\pwsh.exe\\\",\\r\\n    \\\"workbench.startupEditor\\\": \\\"newUntitledFile\\\",\\r\\n    \\\"python.formatting.provider\\\": \\\"none\\\",\\r\\n    \\\"[python]\\\": {\\r\\n        \\\"editor.semanticHighlighting.enabled\\\": true,\\r\\n        \\\"editor.codeActionsOnSave\\\": {\\r\\n            \\\"source.fixAll\\\": \\\"explicit\\\",\\r\\n            \\\"source.organizeImports\\\": \\\"explicit\\\"\\r\\n        },\\r\\n        \\\"editor.formatOnSave\\\": true,\\r\\n        \\\"editor.defaultFormatter\\\": \\\"charliermarsh.ruff\\\"\\r\\n    },\\r\\n    \\\"[javascript]\\\": {\\r\\n        // \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\",\\r\\n        \\\"editor.defaultFormatter\\\": \\\"vscode.typescript-language-features\\\"\\r\\n        // \\\"editor.formatOnSave\\\": true\\r\\n    },\\r\\n    \\\"[typescript]\\\": {\\r\\n        // \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\",\\r\\n        \\\"editor.defaultFormatter\\\": \\\"dbaeumer.vscode-eslint\\\"\\r\\n        // \\\"editor.formatOnSave\\\": true\\r\\n    },\\r\\n    \\\"[json]\\\": {\\r\\n        // \\\"editor.defaultFormatter\\\": \\\"esbenp.prettier-vscode\\\",\\r\\n        \\\"editor.defaultFormatter\\\": \\\"dbaeumer.vscode-eslint\\\"\\r\\n        // \\\"editor.formatOnSave\\\": true\\r\\n    },\\r\\n    \\\"[dart]\\\": {\\r\\n        \\\"editor.formatOnSave\\\": true,\\r\\n        \\\"editor.defaultFormatter\\\": \\\"Dart-Code.dart-code\\\",\\r\\n        \\\"editor.codeActionsOnSave\\\": {\\r\\n            \\\"source.organizeImports\\\": \\\"always\\\",\\r\\n        },\\r\\n        \\\"editor.rulers\\\": [\\r\\n            140\\r\\n        ],\\r\\n    },\\r\\n    \\\"dart.lineLength\\\": 150,\\r\\n    \\\"editor.minimap.maxColumn\\\": 160,\\r\\n    \\\"editor.tabSize\\\": 2,\\r\\n    \\\"remote.SSH.remotePlatform\\\": {\\r\\n        \\\"deimos\\\": \\\"linux\\\",\\r\\n        \\\"ykrasik.asuscomm.com\\\": \\\"linux\\\"\\r\\n    },\\r\\n    \\\"workbench.iconTheme\\\": \\\"vscode-icons\\\",\\r\\n    \\\"vscode-home-assistant.hostUrl\\\": \\\"https://homeassistant.ykrasik.com\\\",\\r\\n    \\\"vscode-home-assistant.longLivedAccessToken\\\": \\\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJhZGY1ZjY4MmZlYmI0NDBkODA1NTBkMThjMGU0YzkwZSIsImlhdCI6MTcwNzQzNTQ1NiwiZXhwIjoyMDIyNzk1NDU2fQ.m_K8isbWAk4caczg4PHd-aRAuHfY6fnWCJe-fJVEv8U\\\",\\r\\n    \\\"redhat.telemetry.enabled\\\": false,\\r\\n    \\\"explorer.confirmDragAndDrop\\\": false,\\r\\n    \\\"sqltools.useNodeRuntime\\\": false,\\r\\n    \\\"sqltools.connections\\\": [],\\r\\n    \\\"sqltools.dependencyManager\\\": {\\r\\n        \\\"packageManager\\\": \\\"npm\\\",\\r\\n        \\\"installArgs\\\": [\\r\\n            \\\"install\\\"\\r\\n        ],\\r\\n        \\\"runScriptArgs\\\": [\\r\\n            \\\"run\\\"\\r\\n        ],\\r\\n        \\\"autoAccept\\\": false\\r\\n    },\\r\\n    \\\"sqltools.results.limit\\\": 500,\\r\\n    \\\"sqltools.results.location\\\": \\\"current\\\",\\r\\n    \\\"sqltools.sessionFilesFolder\\\": \\\"sql\\\",\\r\\n    \\\"files.trimTrailingWhitespace\\\": true,\\r\\n    \\\"github.copilot.enable\\\": {\\r\\n        \\\"*\\\": false\\r\\n    },\\r\\n    \\\"files.associations\\\": {\\r\\n        \\\"*.conf\\\": \\\"nginx\\\"\\r\\n    },\\r\\n    \\\"nginx-conf-hint.syntax\\\": \\\"sublime\\\",\\r\\n    \\\"docker.containers.label\\\": \\\"ContainerName\\\",\\r\\n    \\\"docker.containers.sortBy\\\": \\\"Label\\\",\\r\\n    \\\"docker.containers.description\\\": [\\r\\n        \\\"Status\\\"\\r\\n    ],\\r\\n    \\\"gitlens.plusFeatures.enabled\\\": false,\\r\\n    \\\"gitlens.codeLens.enabled\\\": false,\\r\\n    \\\"gitlens.currentLine.enabled\\\": false,\\r\\n    \\\"editor.fontFamily\\\": \\\"'FiraCode Nerd Font', Consolas, 'Courier New', monospace\\\",\\r\\n    \\\"editor.fontLigatures\\\": true,\\r\\n    \\\"files.autoSave\\\": \\\"afterDelay\\\",\\r\\n    \\\"debug.console.fontSize\\\": 14,\\r\\n    \\\"terminal.integrated.fontSize\\\": 14,\\r\\n    \\\"chat.editor.fontSize\\\": 14,\\r\\n    \\\"editor.fontWeight\\\": \\\"100\\\",\\r\\n    \\\"scm.inputFontSize\\\": 14,\\r\\n    \\\"scm.inputFontFamily\\\": \\\"editor\\\",\\r\\n    \\\"terminal.integrated.fontWeight\\\": \\\"200\\\",\\r\\n    \\\"errorLens.fontWeight\\\": \\\"200\\\",\\r\\n    \\\"vsicons.dontShowNewVersionMessage\\\": true,\\r\\n    \\\"[dockerfile]\\\": {\\r\\n        \\\"editor.defaultFormatter\\\": \\\"ms-azuretools.vscode-docker\\\"\\r\\n    },\\r\\n    \\\"python.analysis.autoFormatStrings\\\": true,\\r\\n    \\\"python.analysis.completeFunctionParens\\\": true,\\r\\n    \\\"python.analysis.typeCheckingMode\\\": \\\"strict\\\",\\r\\n    \\\"security.workspace.trust.untrustedFiles\\\": \\\"open\\\",\\r\\n    \\\"terminal.integrated.persistentSessionScrollback\\\": 3000,\\r\\n    \\\"editor.bracketPairColorization.independentColorPoolPerBracketType\\\": true,\\r\\n    \\\"editor.autoClosingBrackets\\\": \\\"beforeWhitespace\\\",\\r\\n    \\\"editor.autoClosingDelete\\\": \\\"always\\\",\\r\\n    \\\"editor.autoClosingQuotes\\\": \\\"beforeWhitespace\\\",\\r\\n    \\\"semanticdiff.diff.compareMovedCode\\\": true,\\r\\n    \\\"semanticdiff.diff.contextLines\\\": 30,\\r\\n    \\\"editor.semanticTokenColorCustomizations\\\": {\\r\\n        \\\"enabled\\\": true,\\r\\n        \\\"rules\\\": {\\r\\n            \\\"class.defaultLibrary\\\": {\\r\\n                \\\"foreground\\\": \\\"#569CD6\\\"\\r\\n            },\\r\\n            \\\"class.builtin\\\": {\\r\\n                \\\"foreground\\\": \\\"#B57EDC\\\"\\r\\n            },\\r\\n            \\\"class\\\": \\\"#7fb06a\\\",\\r\\n            \\\"enum\\\": \\\"#9c9c56\\\",\\r\\n            \\\"typeParameter\\\": {\\r\\n                \\\"foreground\\\": \\\"#7fb06a\\\",\\r\\n                \\\"fontStyle\\\": \\\"bold\\\"\\r\\n            },\\r\\n            \\\"property\\\": \\\"#c4ba62\\\",\\r\\n            // \\\"parameter\\\": \\\"#ad8b58\\\",\\r\\n            \\\"selfParameter\\\": \\\"#B57EDC\\\",\\r\\n            \\\"variable\\\": \\\"#7a99bf\\\",\\r\\n            \\\"variable.static.readonly\\\": {\\r\\n                \\\"foreground\\\": \\\"#B57EDC\\\",\\r\\n                \\\"fontStyle\\\": \\\"italic\\\"\\r\\n            },\\r\\n            \\\"method\\\": \\\"#E76A7D\\\",\\r\\n            \\\"function\\\": \\\"#E76A7D\\\",\\r\\n            \\\"magicFunction\\\": \\\"#629755\\\",\\r\\n            \\\"macro\\\": {\\r\\n                \\\"fontStyle\\\": \\\"bold\\\"\\r\\n            }\\r\\n        }\\r\\n    },\\r\\n    \\\"editor.tokenColorCustomizations\\\": {\\r\\n        \\\"textMateRules\\\": [\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"keyword.control\\\",\\r\\n                    \\\"keyword.operator\\\",\\r\\n                    \\\"constant.language\\\",\\r\\n                    \\\"storage.type.function\\\",\\r\\n                    \\\"punctuation.definition.decorator\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#569CD6\\\",\\r\\n                    \\\"fontStyle\\\": \\\"bold\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"meta.function\\\",\\r\\n                    \\\"meta.function-call\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#E76A7D\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"string.quoted\\\",\\r\\n                    \\\"string.unquoted\\\",\\r\\n                    \\\"punctuation.definition.string\\\",\\r\\n                    \\\"string.template\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#bab51f\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": \\\"variable\\\",\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#7E7E7E\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"punctuation\\\",\\r\\n                    \\\"punctuation.separator\\\",\\r\\n                    \\\"punctuation.section\\\",\\r\\n                    \\\"keyword.operator.assignment\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#b4b4b4\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": \\\"keyword.codetag\\\",\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#d8d525\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"entity.name.scope-resolution\\\",\\r\\n                    \\\"meta.block\\\",\\r\\n                    \\\"entity.name.scope-resolution.parameter\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#8796B0\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": \\\"entity.name.function.member\\\",\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#89A97D\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"comment.line.double-slash\\\",\\r\\n                    \\\"punctuation.definition.comment\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#6A9955\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"entity.name.type.parameter\\\",\\r\\n                    \\\"entity.name.type.class\\\",\\r\\n                    \\\"entity.name.function.definition.special.constructor\\\",\\r\\n                    \\\"entity.name.function.constructor\\\",\\r\\n                    \\\"meta.head.function.definition.special.constructor\\\",\\r\\n                    \\\"meta.function.definition.special.constructor\\\",\\r\\n                    \\\"entity.name.function.definition.special.member.destructor\\\",\\r\\n                    \\\"entity.name.function.destructor\\\",\\r\\n                    \\\"meta.head.function.definition.special.member.destructor\\\",\\r\\n                    \\\"meta.function.definition.special.member.destructor\\\",\\r\\n                    \\\"entity.name.type\\\",\\r\\n                    \\\"meta.qualified_type\\\",\\r\\n                    \\\"meta.function.definition\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#6F8FA1\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"variable.parameter\\\",\\r\\n                    \\\"meta.parameter\\\",\\r\\n                    \\\"meta.function.definition.parameters\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#AE6A21\\\"\\r\\n                }\\r\\n            },\\r\\n            {\\r\\n                \\\"scope\\\": [\\r\\n                    \\\"variable.parameter.function.language.special.self\\\"\\r\\n                ],\\r\\n                \\\"settings\\\": {\\r\\n                    \\\"foreground\\\": \\\"#B57EDC\\\"\\r\\n                }\\r\\n            }\\r\\n        ]\\r\\n    },\\r\\n    \\\"cSpell.userWords\\\": [\\r\\n        \\\"AILLM\\\",\\r\\n        \\\"ASGI\\\",\\r\\n        \\\"authtoken\\\",\\r\\n        \\\"autouse\\\",\\r\\n        \\\"azuretools\\\",\\r\\n        \\\"boto\\\",\\r\\n        \\\"botocore\\\",\\r\\n        \\\"Callsite\\\",\\r\\n        \\\"charliermarsh\\\",\\r\\n        \\\"codetag\\\",\\r\\n        \\\"Consolas\\\",\\r\\n        \\\"Cowabunga\\\",\\r\\n        \\\"dateutil\\\",\\r\\n        \\\"Dima\\\",\\r\\n        \\\"djangoql\\\",\\r\\n        \\\"dont\\\",\\r\\n        \\\"dotenv\\\",\\r\\n        \\\"esbenp\\\",\\r\\n        \\\"Fira\\\",\\r\\n        \\\"Krasik\\\",\\r\\n        \\\"langchain\\\",\\r\\n        \\\"localstack\\\",\\r\\n        \\\"makereport\\\",\\r\\n        \\\"mobx\\\",\\r\\n        \\\"openai\\\",\\r\\n        \\\"Parens\\\",\\r\\n        \\\"phonenumbers\\\",\\r\\n        \\\"phonenumberutil\\\",\\r\\n        \\\"pycache\\\",\\r\\n        \\\"pydantic\\\",\\r\\n        \\\"pytest\\\",\\r\\n        \\\"pytestmark\\\",\\r\\n        \\\"qfiler\\\",\\r\\n        \\\"retryable\\\",\\r\\n        \\\"scminput\\\",\\r\\n        \\\"Scrollback\\\",\\r\\n        \\\"secretmanager\\\",\\r\\n        \\\"semanticdiff\\\",\\r\\n        \\\"signum\\\",\\r\\n        \\\"sqltools\\\",\\r\\n        \\\"structlog\\\",\\r\\n        \\\"timeslot\\\",\\r\\n        \\\"tracebacks\\\",\\r\\n        \\\"tryfirst\\\",\\r\\n        \\\"unidecode\\\",\\r\\n        \\\"venv\\\",\\r\\n        \\\"vsicons\\\",\\r\\n        \\\"whitenoise\\\",\\r\\n        \\\"xframe\\\",\\r\\n        \\\"Yevgeny\\\"\\r\\n    ],\\r\\n    \\\"editor.inlineSuggest.suppressSuggestions\\\": true,\\r\\n    \\\"editor.quickSuggestions\\\": {\\r\\n        \\\"other\\\": \\\"inline\\\",\\r\\n        \\\"comments\\\": \\\"inline\\\",\\r\\n        \\\"strings\\\": \\\"inline\\\"\\r\\n    },\\r\\n    \\\"terminal.integrated.shellIntegration.suggestEnabled\\\": true,\\r\\n    \\\"editor.screenReaderAnnounceInlineSuggestion\\\": false,\\r\\n    \\\"editor.acceptSuggestionOnEnter\\\": \\\"smart\\\",\\r\\n    \\\"editor.suggestOnTriggerCharacters\\\": false,\\r\\n    \\\"[jsonc]\\\": {\\r\\n        \\\"editor.defaultFormatter\\\": \\\"vscode.json-language-features\\\"\\r\\n    },\\r\\n    \\\"files.exclude\\\": {\\r\\n        \\\"**/__pycache__\\\": true,\\r\\n        \\\"**/.pytest_cache\\\": true\\r\\n    },\\r\\n    \\\"python.analysis.autoImportCompletions\\\": true,\\r\\n    \\\"python.analysis.inlayHints.functionReturnTypes\\\": true,\\r\\n    \\\"python.analysis.inlayHints.pytestParameters\\\": true,\\r\\n    \\\"python.analysis.inlayHints.variableTypes\\\": true,\\r\\n    \\\"git.autofetch\\\": true,\\r\\n    \\\"diffEditor.experimental.showMoves\\\": true,\\r\\n    \\\"errorLens.enableOnDiffView\\\": true,\\r\\n    \\\"terminal.integrated.defaultProfile.windows\\\": \\\"Windows PowerShell\\\",\\r\\n    \\\"editor.lineHeight\\\": 1.6,\\r\\n    \\\"terminal.integrated.lineHeight\\\": 1.2,\\r\\n    \\\"git.allowForcePush\\\": true,\\r\\n    \\\"git.useForcePushIfIncludes\\\": false,\\r\\n    \\\"git.enableSmartCommit\\\": true,\\r\\n    \\\"githubPullRequests.pullBranch\\\": \\\"never\\\",\\r\\n    \\\"git.branchProtectionPrompt\\\": \\\"alwaysCommit\\\",\\r\\n    \\\"git.mergeEditor\\\": true,\\r\\n    \\\"gitlens.views.commits.files.layout\\\": \\\"tree\\\",\\r\\n    \\\"cmake.showOptionsMovedNotification\\\": false,\\r\\n    \\\"git.confirmForcePush\\\": false,\\r\\n    \\\"workbench.settings.applyToAllProfiles\\\": [\\r\\n        \\\"editor.formatOnSave\\\"\\r\\n    ],\\r\\n    \\\"git.confirmSync\\\": false,\\r\\n    \\\"[dotenv]\\\": {\\r\\n        \\\"editor.defaultFormatter\\\": \\\"foxundermoon.shell-format\\\"\\r\\n    },\\r\\n    \\\"codeium.enableConfig\\\": {\\r\\n        \\\"*\\\": true,\\r\\n        \\\"dotenv\\\": true,\\r\\n        \\\"django-html\\\": true,\\r\\n        \\\"markdown\\\": true,\\r\\n        \\\"ignore\\\": true,\\r\\n        \\\"nginx\\\": true,\\r\\n        \\\"plaintext\\\": true,\\r\\n        \\\"log\\\": true,\\r\\n        \\\"xml\\\": true,\\r\\n        \\\"bat\\\": true,\\r\\n        \\\"Log\\\": true,\\r\\n        \\\"properties\\\": true,\\r\\n        \\\"just\\\": true\\r\\n    },\\r\\n    \\\"python.analysis.packageIndexDepths\\\": [\\r\\n        {\\r\\n            \\\"name\\\": \\\"django\\\",\\r\\n            \\\"depth\\\": 5\\r\\n        }\\r\\n    ],\\r\\n    \\\"python.analysis.ignore\\\": [\\r\\n        \\\"venv\\\"\\r\\n    ],\\r\\n    \\\"codeium.enableCodeLens\\\": false,\\r\\n    \\\"amazonQ.telemetry\\\": false,\\r\\n    \\\"continue.enableTabAutocomplete\\\": true,\\r\\n    \\\"terminal.integrated.enableMultiLinePasteWarning\\\": false,\\r\\n    \\\"git.rebaseWhenSync\\\": true,\\r\\n    \\\"debug.toolBarLocation\\\": \\\"docked\\\",\\r\\n    \\\"debug.hideLauncherWhileDebugging\\\": true,\\r\\n    \\\"eslint.validate\\\": [\\r\\n        \\\"javascript\\\",\\r\\n        \\\"javascriptreact\\\",\\r\\n        \\\"vue\\\"\\r\\n    ],\\r\\n    // \\\"editor.formatOnSave\\\": true, // this will currently make \\\"eslint.codeActionsOnSave.rules\\\" setting not work\\r\\n    \\\"editor.formatOnPaste\\\": false,\\r\\n    \\\"editor.formatOnType\\\": false,\\r\\n    \\\"editor.codeActionsOnSave\\\": {\\r\\n        \\\"source.fixAll\\\": \\\"explicit\\\"\\r\\n    },\\r\\n    \\\"eslint.format.enable\\\": true,\\r\\n    \\\"eslint.useESLintClass\\\": true,\\r\\n    \\\"amazonQ.showInlineCodeSuggestionsWithCodeReferences\\\": false,\\r\\n    \\\"amazonQ.shareContentWithAWS\\\": false,\\r\\n    \\\"files.autoSaveDelay\\\": 2000,\\r\\n    \\\"javascript.updateImportsOnFileMove.enabled\\\": \\\"always\\\",\\r\\n    \\\"autoimport.useSemiColon\\\": false,\\r\\n    \\\"tsimporter.preferRelative\\\": true,\\r\\n    \\\"editor.formatOnSaveMode\\\": \\\"modifications\\\",\\r\\n    \\\"terminal.integrated.scrollback\\\": 3000,\\r\\n    \\\"ruff.nativeServer\\\": true,\\r\\n    \\\"amazonQ.workspaceIndex\\\": true,\\r\\n    \\\"amazonQ.workspaceIndexMaxSize\\\": 1024,\\r\\n    \\\"amazonQ.workspaceIndexUseGPU\\\": true,\\r\\n    \\\"python.terminal.activateEnvInCurrentTerminal\\\": true,\\r\\n    \\\"projectManager.git.baseFolders\\\": [\\r\\n        \\\"E:\\\\\\\\\\\"\\r\\n    ],\\r\\n    \\\"vs-kubernetes\\\": {\\r\\n        \\\"vscode-kubernetes.helm-path-linux\\\": \\\"/home/<USER>/.local/state/vs-kubernetes/tools/helm/linux-arm64/helm\\\",\\r\\n        \\\"vscode-kubernetes.minikube-path-linux\\\": \\\"/home/<USER>/.local/state/vs-kubernetes/tools/minikube/linux-arm64/minikube\\\"\\r\\n    },\\r\\n    \\\"hediet.vscode-drawio.resizeImages\\\": null,\\r\\n    \\\"editor.formatOnSave\\\": true,\\r\\n    \\\"[nginx]\\\": {\\r\\n        \\\"editor.defaultFormatter\\\": \\\"AaaaronZhou.nginx-config-formatter-vscode-extension\\\"\\r\\n    },\\r\\n    \\\"python.analysis.extraPaths\\\": [\\r\\n        \\\"libs/common\\\"\\r\\n    ],\\r\\n    \\\"dart.debugExternalPackageLibraries\\\": true,\\r\\n    \\\"dart.debugSdkLibraries\\\": true,\\r\\n    \\\"editor.minimap.enabled\\\": false,\\r\\n    \\\"cody.autocomplete.enabled\\\": true,\\r\\n    \\\"notebook.output.textLineLimit\\\": 999,\\r\\n    \\\"terminal.integrated.persistentSessionReviveProcess\\\": \\\"onExitAndWindowClose\\\",\\r\\n    \\\"editor.snippetSuggestions\\\": \\\"none\\\",\\r\\n    \\\"database-client.autoSync\\\": true,\\r\\n    \\\"dbcode.connections\\\": [\\r\\n        {\\r\\n            \\\"connectionId\\\": \\\"7HeGWQ0wlHoVNqOoSw7V1\\\",\\r\\n            \\\"name\\\": \\\"Deimos\\\",\\r\\n            \\\"driver\\\": \\\"postgres\\\",\\r\\n            \\\"connectionType\\\": \\\"host\\\",\\r\\n            \\\"host\\\": \\\"localhost\\\",\\r\\n            \\\"port\\\": 5432,\\r\\n            \\\"ssl\\\": false,\\r\\n            \\\"username\\\": \\\"postgres\\\",\\r\\n            \\\"password\\\": \\\"\\\",\\r\\n            \\\"savePassword\\\": \\\"secretStorage\\\",\\r\\n            \\\"connectionTimeout\\\": 30\\r\\n        },\\r\\n        {\\r\\n            \\\"connectionId\\\": \\\"bUPAn7ncKWLkQTA7xuK3a\\\",\\r\\n            \\\"name\\\": \\\"Deimos MySql\\\",\\r\\n            \\\"driver\\\": \\\"mysql\\\",\\r\\n            \\\"connectionType\\\": \\\"host\\\",\\r\\n            \\\"host\\\": \\\"localhost\\\",\\r\\n            \\\"port\\\": 3306,\\r\\n            \\\"ssl\\\": false,\\r\\n            \\\"username\\\": \\\"root\\\",\\r\\n            \\\"password\\\": \\\"\\\",\\r\\n            \\\"savePassword\\\": \\\"secretStorage\\\",\\r\\n            \\\"connectionTimeout\\\": 30,\\r\\n            \\\"driverOptions\\\": {\\r\\n                \\\"retrievePublickey\\\": true\\r\\n            }\\r\\n        },\\r\\n        {\\r\\n            \\\"connectionId\\\": \\\"ctNFszNJFuFB88gejO2l-\\\",\\r\\n            \\\"name\\\": \\\"Copy of Deimos 17\\\",\\r\\n            \\\"driver\\\": \\\"postgres\\\",\\r\\n            \\\"connectionType\\\": \\\"host\\\",\\r\\n            \\\"host\\\": \\\"localhost\\\",\\r\\n            \\\"port\\\": 5433,\\r\\n            \\\"ssl\\\": false,\\r\\n            \\\"username\\\": \\\"postgres\\\",\\r\\n            \\\"password\\\": \\\"\\\",\\r\\n            \\\"savePassword\\\": \\\"secretStorage\\\",\\r\\n            \\\"connectionTimeout\\\": 30\\r\\n        },\\r\\n        {\\r\\n            \\\"connectionId\\\": \\\"UCcXHpjOy8DPZdsAgan2x\\\",\\r\\n            \\\"name\\\": \\\"Leads Guru Prod\\\",\\r\\n            \\\"driver\\\": \\\"postgres\\\",\\r\\n            \\\"connectionType\\\": \\\"host\\\",\\r\\n            \\\"host\\\": \\\"core-db.cekxcz7qd0ll.us-east-1.rds.amazonaws.com\\\",\\r\\n            \\\"port\\\": 5432,\\r\\n            \\\"ssl\\\": true,\\r\\n            \\\"username\\\": \\\"postgres\\\",\\r\\n            \\\"password\\\": \\\"\\\",\\r\\n            \\\"savePassword\\\": \\\"secretStorage\\\",\\r\\n            \\\"database\\\": \\\"leads_guru\\\",\\r\\n            \\\"connectionTimeout\\\": 30\\r\\n        },\\r\\n        {\\r\\n            \\\"connectionId\\\": \\\"oCQ-vIeI9HBJbygA9Qi7w\\\",\\r\\n            \\\"name\\\": \\\"Leads Guru Local\\\",\\r\\n            \\\"driver\\\": \\\"postgres\\\",\\r\\n            \\\"connectionType\\\": \\\"host\\\",\\r\\n            \\\"host\\\": \\\"localhost\\\",\\r\\n            \\\"port\\\": 5432,\\r\\n            \\\"ssl\\\": false,\\r\\n            \\\"username\\\": \\\"postgres\\\",\\r\\n            \\\"password\\\": \\\"\\\",\\r\\n            \\\"savePassword\\\": \\\"secretStorage\\\",\\r\\n            \\\"database\\\": \\\"leads_guru\\\",\\r\\n            \\\"connectionTimeout\\\": 30\\r\\n        }\\r\\n    ],\\r\\n    \\\"editor.mouseWheelScrollSensitivity\\\": 3,\\r\\n    \\\"aider-composer.pythonPath\\\": \\\"C:\\\\\\\\Users\\\\\\\\<USER>\\\\\\\\scoop\\\\\\\\apps\\\\\\\\python\\\\\\\\current\\\\\\\\\\\",\\r\\n    \\\"aider-composer.inlineDiff.enable\\\": true,\\r\\n    \\\"editor.fastScrollSensitivity\\\": 4,\\r\\n    \\\"roo-cline.allowedCommands\\\": [\\r\\n        \\\"npm test\\\",\\r\\n        \\\"npm install\\\",\\r\\n        \\\"tsc\\\",\\r\\n        \\\"git log\\\",\\r\\n        \\\"git diff\\\",\\r\\n        \\\"git show\\\",\\r\\n        \\\"dart\\\",\\r\\n        \\\"flutter\\\"\\r\\n    ],\\r\\n    \\\"github.copilot.nextEditSuggestions.enabled\\\": true,\\r\\n    \\\"github.copilot.chat.editor.temporalContext.enabled\\\": true,\\r\\n    \\\"github.copilot.chat.edits.codesearch.enabled\\\": true,\\r\\n    \\\"github.copilot.chat.edits.temporalContext.enabled\\\": true,\\r\\n    \\\"github.copilot.chat.languageContext.typescript.enabled\\\": true,\\r\\n    \\\"github.copilot.chat.search.semanticTextResults\\\": true,\\r\\n    \\\"terminal.integrated.fontLigatures.enabled\\\": true,\\r\\n    \\\"terminal.integrated.suggest.enabled\\\": true,\\r\\n    // \\\"terminal.integrated.env.linux\\\": {\\r\\n    //   \\\"PATH\\\": \\\"/path/to/fnm/bin:$PATH\\\"\\r\\n    // }\\r\\n    // \\\"workbench.colorTheme\\\": \\\"Dark\\\",\\r\\n    \\\"window.titleBarStyle\\\": \\\"custom\\\",\\r\\n    \\\"editor.minimap.scale\\\": 2,\\r\\n    //\\\"editor.letterSpacing\\\": 0.2,\\r\\n    // \\\"editor.fontSize\\\": 15,\\r\\n    \\\"fontshortcuts.defaultFontSize\\\": 13,\\r\\n    \\\"fontshortcuts.defaultTerminalFontSize\\\": 13,\\r\\n    \\\"fontshortcuts.step\\\": 1,\\r\\n    \\\"editor.renderWhitespace\\\": \\\"trailing\\\",\\r\\n    \\\"editor.cursorSurroundingLines\\\": 8,\\r\\n    \\\"editor.smoothScrolling\\\": true,\\r\\n    \\\"editor.inlineSuggest.enabled\\\": true,\\r\\n    \\\"editor.folding\\\": true,\\r\\n    //gutter\\r\\n    \\\"editor.accessibilitySupport\\\": \\\"off\\\",\\r\\n    \\\"editor.lightbulb.enabled\\\": \\\"off\\\",\\r\\n    \\\"editor.guides.indentation\\\": true,\\r\\n    \\\"editor.hover.delay\\\": 250,\\r\\n    \\\"editor.hover.sticky\\\": true,\\r\\n    \\\"editor.renderLineHighlight\\\": \\\"line\\\",\\r\\n    \\\"editor.rulers\\\": [\\r\\n        140\\r\\n    ],\\r\\n    \\\"editor.scrollBeyondLastLine\\\": false,\\r\\n    \\\"editor.semanticHighlighting.enabled\\\": true,\\r\\n    \\\"editor.linkedEditing\\\": true,\\r\\n    \\\"editor.cursorStyle\\\": \\\"line\\\",\\r\\n    //workbench\\r\\n    \\\"workbench.editor.enablePreview\\\": true,\\r\\n    \\\"workbench.editor.highlightModifiedTabs\\\": true,\\r\\n    \\\"workbench.editor.labelFormat\\\": \\\"short\\\",\\r\\n    \\\"workbench.editor.splitInGroupLayout\\\": \\\"horizontal\\\",\\r\\n    \\\"workbench.editor.splitSizing\\\": \\\"split\\\",\\r\\n    \\\"workbench.editor.untitled.labelFormat\\\": \\\"name\\\",\\r\\n    \\\"workbench.editor.wrapTabs\\\": false,\\r\\n    \\\"workbench.panel.defaultLocation\\\": \\\"bottom\\\",\\r\\n    \\\"workbench.editor.showIcons\\\": true,\\r\\n    \\\"security.workspace.trust.untrustedFiles\\\": \\\"open\\\",\\r\\n    // speed up indexing\\r\\n    \\\"files.watcherExclude\\\": {\\r\\n        \\\"**/.git/objects/**\\\": true,\\r\\n        \\\"**/.git/subtree-cache/**\\\": true,\\r\\n        \\\"**/node_modules/*/**\\\": true,\\r\\n        \\\"**/target/**\\\": true\\r\\n    },\\r\\n    \\\"update.mode\\\": \\\"manual\\\",\\r\\n    \\\"workbench.settings.editor\\\": \\\"ui\\\",\\r\\n    \\\"workbench.editor.empty.hint\\\": \\\"hidden\\\",\\r\\n    // \\\"custom-ui-style.external.imports\\\": [\\r\\n    //     \\\"file://C:\\\\\\\\.vscode\\\\\\\\custom-ui/settings.css\\\",\\r\\n    //     // \\\"file://C:\\\\\\\\.vscode\\\\\\\\custom-ui/explorerTitleHandler.js\\\"\\r\\n    // ],\\r\\n    \\\"workbench.startupEditor\\\": \\\"none\\\",\\r\\n    \\\"eslint.format.enable\\\": true,\\r\\n    \\\"errorLens.enabled\\\": true,\\r\\n    \\\"errorLens.enabledDiagnosticLevels\\\": [\\r\\n        \\\"error\\\",\\r\\n        \\\"info\\\",\\r\\n        \\\"warning\\\"\\r\\n    ],\\r\\n    \\\"workbench.editor.showTabs\\\": \\\"multiple\\\",\\r\\n    \\\"symbols.hidesExplorerArrows\\\": true,\\r\\n    // \\\"window.menuBarVisibility\\\": \\\"compact\\\",\\r\\n    // \\\"workbench.tree.indent\\\": 16,\\r\\n    \\\"workbench.editor.tabSizing\\\": \\\"fit\\\",\\r\\n    \\\"workbench.editor.tabSizingFixedMaxWidth\\\": 200,\\r\\n    \\\"workbench.editor.tabActionLocation\\\": \\\"left\\\",\\r\\n    // \\\"workbench.layoutControl.enabled\\\": false,\\r\\n    // \\\"window.commandCenter\\\": false,\\r\\n    \\\"git.suggestSmartCommit\\\": false,\\r\\n    \\\"editor.bracketPairColorization.enabled\\\": true,\\r\\n    \\\"editor.guides.bracketPairs\\\": \\\"active\\\",\\r\\n    \\\"editor.scrollbar.vertical\\\": \\\"visible\\\",\\r\\n    \\\"workbench.colorCustomizations\\\": {\\r\\n        \\\"activityBarBadge.foreground\\\": \\\"#ffffffbf\\\",\\r\\n        // \\\"activityBarBadge.background\\\": \\\"#e64646\\\",\\r\\n        \\\"activityBar.background\\\": \\\"#2B2D30\\\",\\r\\n        // \\\"editor.background\\\": \\\"#1c1c1c\\\",\\r\\n        \\\"editor.lineHighlightBackground\\\": \\\"#26282d\\\",\\r\\n        \\\"editor.lineHighlightBorder\\\": \\\"#00000000\\\",\\r\\n        \\\"editorBracketMatch.background\\\": \\\"#43454a\\\",\\r\\n        \\\"editorBracketMatch.border\\\": \\\"#00000000\\\",\\r\\n        \\\"editorGroupHeader.tabsBackground\\\": \\\"#1c1c1c\\\",\\r\\n        \\\"editorGutter.background\\\": \\\"#00000000\\\",\\r\\n        \\\"editorStickyScroll.border\\\": \\\"#393939\\\",\\r\\n        \\\"editorStickyScroll.shadow\\\": \\\"#00000078\\\",\\r\\n        \\\"editorWidget.background\\\": \\\"#2B2D30\\\",\\r\\n        \\\"focusBorder\\\": \\\"#00000000\\\",\\r\\n        \\\"panel.background\\\": \\\"#1c1c1c\\\",\\r\\n        \\\"scrollbar.shadow\\\": \\\"#00000072\\\",\\r\\n        \\\"scrollbarSlider.background\\\": \\\"#ffffff12\\\",\\r\\n        \\\"sideBar.background\\\": \\\"#1c1c1c\\\",\\r\\n        \\\"sideBarSectionHeader.border\\\": \\\"#00000000\\\",\\r\\n        \\\"sideBarSectionHeader.background\\\": \\\"#1c1c1c\\\",\\r\\n        \\\"statusBar.background\\\": \\\"#2B2D30\\\",\\r\\n        \\\"statusBar.debuggingBackground\\\": \\\"#2B2D30\\\",\\r\\n        \\\"statusBar.foreground\\\": \\\"#757575\\\",\\r\\n        \\\"panel.border\\\": \\\"#00000000\\\",\\r\\n        // \\\"sideBar.border\\\": \\\"#00000000\\\",\\r\\n        \\\"statusBar.noFolderBackground\\\": \\\"#2B2D30\\\",\\r\\n        \\\"gitDecoration.modifiedResourceForeground\\\": \\\"#74aeec\\\",\\r\\n        \\\"gitDecoration.stageModifiedResourceForeground\\\": \\\"#74aeec\\\",\\r\\n        \\\"gitDecoration.addedResourceForeground\\\": \\\"#7fd27fd9\\\",\\r\\n        \\\"gitDecoration.renamedResourceForeground\\\": \\\"#74aeec\\\",\\r\\n        \\\"gitDecoration.untrackedResourceForeground\\\": \\\"#f7aeae\\\",\\r\\n        \\\"gitDecoration.ignoredResourceForeground\\\": \\\"#ffffff3b\\\",\\r\\n        \\\"statusBarItem.remoteBackground\\\": \\\"#2B2D30\\\",\\r\\n        \\\"statusBarItem.remoteForeground\\\": \\\"#757575\\\",\\r\\n        \\\"terminal.background\\\": \\\"#1E1F22\\\",\\r\\n        \\\"terminal.border\\\": \\\"#2B2D30\\\",\\r\\n        \\\"titleBar.activeBackground\\\": \\\"#2B2D30\\\",\\r\\n        \\\"titleBar.inactiveBackground\\\": \\\"#2B2D30\\\",\\r\\n        \\\"quickInput.background\\\": \\\"#2B2D30\\\",\\r\\n        \\\"input.background\\\": \\\"#3d3e42\\\",\\r\\n        \\\"button.background\\\": \\\"#3d3e42\\\"\\r\\n    },\\r\\n    \\\"dart.closeDevTools\\\": \\\"always\\\",\\r\\n    \\\"dart.devToolsLocation\\\": {\\r\\n        \\\"default\\\": \\\"external\\\"\\r\\n    },\\r\\n    \\\"geminicodeassist.project\\\": \\\"gen-lang-client-0469197985\\\",\\r\\n    \\\"terminal.integrated.rightClickBehavior\\\": \\\"default\\\",\\r\\n    \\\"diffEditor.codeLens\\\": true,\\r\\n    \\\"terminal.external.windowsExec\\\": \\\"pwsh\\\",\\r\\n    \\\"terminal.integrated.cursorStyle\\\": \\\"line\\\",\\r\\n    \\\"augment.chat.userGuidelines\\\": \\\"\\\",\\r\\n    \\\"diffEditor.ignoreTrimWhitespace\\\": false,\\r\\n    \\\"containers.containers.description\\\": [\\r\\n        \\\"Status\\\"\\r\\n    ],\\r\\n    \\\"containers.containers.label\\\": \\\"ContainerName\\\",\\r\\n    \\\"containers.containers.sortBy\\\": \\\"Label\\\",\\r\\n    \\\"docker.extension.enableComposeLanguageServer\\\": false,\\r\\n    \\\"geminicodeassist.updateChannel\\\": \\\"Insiders\\\",\\r\\n    \\\"dbcode.resultLocation\\\": \\\"panel\\\",\\r\\n}\"}", "keybindings": "{\"keybindings\":\"// Place your key bindings in this file to override the defaultsauto[]\\r\\n[\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+b\\\",\\r\\n    \\\"command\\\": \\\"editor.action.revealDefinition\\\",\\r\\n    \\\"when\\\": \\\"editorHasDefinitionProvider && editorTextFocus && !isInEmbeddedEditor\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"f12\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.revealDefinition\\\",\\r\\n    \\\"when\\\": \\\"editorHasDefinitionProvider && editorTextFocus && !isInEmbeddedEditor\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+b\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.toggleSidebarVisibility\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+d\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.duplicateSelection\\\",\\r\\n    \\\"when\\\": \\\"editorHasSelection && editorTextFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+t\\\",\\r\\n    \\\"command\\\": \\\"-git.sync\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+t\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.showAllSymbols\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+t\\\",\\r\\n    \\\"command\\\": \\\"editor.action.copyLinesDownAction\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"shift+alt+down\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.copyLinesDownAction\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+t\\\",\\r\\n    \\\"command\\\": \\\"editor.action.copyLinesDownAction\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorHasSelection && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+d\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.copyLinesDownAction\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorHasSelection && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+d\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.addSelectionToNextFindMatch\\\",\\r\\n    \\\"when\\\": \\\"editorFocus\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+d\\\",\\r\\n    \\\"command\\\": \\\"editor.action.deleteLines\\\",\\r\\n    \\\"when\\\": \\\"textInputFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+shift+k\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.deleteLines\\\",\\r\\n    \\\"when\\\": \\\"textInputFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+d\\\",\\r\\n    \\\"command\\\": \\\"editor.action.deleteLines\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+y\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.deleteLines\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"f2\\\",\\r\\n    \\\"command\\\": \\\"editor.action.rename\\\",\\r\\n    \\\"when\\\": \\\"editorHasRenameProvider && editorTextFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+m\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.toggleTabFocusMode\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+m\\\",\\r\\n    \\\"command\\\": \\\"workbench.action.gotoSymbol\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+y\\\",\\r\\n    \\\"command\\\": \\\"-redo\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+y\\\",\\r\\n    \\\"command\\\": \\\"explorer.newFile\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+h\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.startFindReplaceAction\\\",\\r\\n    \\\"when\\\": \\\"editorFocus || editorIsOpen\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+d\\\",\\r\\n    \\\"command\\\": \\\"workbench.files.action.compareWithClipboard\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+o\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.files.openFile\\\",\\r\\n    \\\"when\\\": \\\"true\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+o\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.files.openFolderViaWorkspace\\\",\\r\\n    \\\"when\\\": \\\"!openFolderWorkspaceSupport && workbenchState == 'workspace'\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+o\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.files.openFileFolder\\\",\\r\\n    \\\"when\\\": \\\"isMacNative && openFolderWorkspaceSupport\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+o\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.codeAction\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && javaLSReady && editorLangId == 'java'\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+o\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.files.openLocalFile\\\",\\r\\n    \\\"when\\\": \\\"remoteFileDialogVisible\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+alt+right\\\",\\r\\n    \\\"command\\\": \\\"workbench.action.nextEditor\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+right\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.nextEditor\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+alt+left\\\",\\r\\n    \\\"command\\\": \\\"workbench.action.previousEditor\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+left\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.previousEditor\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+alt+u\\\",\\r\\n    \\\"command\\\": \\\"-platformio-ide.upload\\\",\\r\\n    \\\"when\\\": \\\"pioProjectReady && pioProjectTasksReady\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+alt+u\\\",\\r\\n    \\\"command\\\": \\\"git.pullRebase\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"f4\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.goToDeclaration\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"f4\\\",\\r\\n    \\\"command\\\": \\\"gitlens.openWorkingFileInDiffRight\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"f4\\\",\\r\\n    \\\"command\\\": \\\"git.openChange\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !isInDiffEditor\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+r\\\",\\r\\n    \\\"command\\\": \\\"-toggleSearchEditorRegex\\\",\\r\\n    \\\"when\\\": \\\"inSearchEditor && searchInputBoxFocus\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+r\\\",\\r\\n    \\\"command\\\": \\\"-workbench.action.terminal.toggleFindRegex\\\",\\r\\n    \\\"when\\\": \\\"terminalFindFocused && terminalHasBeenCreated || terminalFindFocused && terminalProcessSupported || terminalFocusInAny && terminalHasBeenCreated || terminalFocusInAny && terminalProcessSupported\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+r\\\",\\r\\n    \\\"command\\\": \\\"continue.debugTerminal\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+shift+r\\\",\\r\\n    \\\"command\\\": \\\"-continue.debugTerminal\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+shift+enter\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.insertLineAfter\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorReadonly && !suggestWidgetVisible\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+shift+enter\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.insertLineBefore\\\",\\r\\n    \\\"when\\\": \\\"editorTextFocus && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+right\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.inlineSuggest.acceptNextWord\\\",\\r\\n    \\\"when\\\": \\\"inlineSuggestionVisible && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+right\\\",\\r\\n    \\\"command\\\": \\\"editor.action.inlineSuggest.showNext\\\",\\r\\n    \\\"when\\\": \\\"aws.codewhisperer.connected && inlineSuggestionVisible && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"right\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.inlineSuggest.showNext\\\",\\r\\n    \\\"when\\\": \\\"aws.codewhisperer.connected && inlineSuggestionVisible && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+left\\\",\\r\\n    \\\"command\\\": \\\"editor.action.inlineSuggest.showPrevious\\\",\\r\\n    \\\"when\\\": \\\"aws.codewhisperer.connected && inlineSuggestionVisible && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"left\\\",\\r\\n    \\\"command\\\": \\\"-editor.action.inlineSuggest.showPrevious\\\",\\r\\n    \\\"when\\\": \\\"aws.codewhisperer.connected && inlineSuggestionVisible && !editorReadonly\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+l\\\",\\r\\n    \\\"command\\\": \\\"-expandLineSelection\\\",\\r\\n    \\\"when\\\": \\\"textInputFocus\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+l\\\",\\r\\n    \\\"command\\\": \\\"workbench.action.terminal.clear\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"alt+f7\\\",\\r\\n    \\\"command\\\": \\\"typescript.findAllFileReferences\\\",\\r\\n    \\\"when\\\": \\\"filesExplorerFocus\\\"\\r\\n  },\\r\\n  {\\r\\n    \\\"key\\\": \\\"ctrl+d ctrl+b\\\",\\r\\n    \\\"command\\\": \\\"-dbcode.connections.view.focus\\\",\\r\\n    \\\"when\\\": \\\"!editorHasSelection || editorHasSelection && !editorTextFocus\\\"\\r\\n  }\\r\\n]\\r\\n\",\"platform\":3}", "snippets": "{\"snippets\":{\"python.json\":\"{\\n\\t// Place your snippets for python here. Each snippet is defined under a snippet name and has a prefix, body and \\n\\t// description. The prefix is what is used to trigger the snippet and the body will be expanded and inserted. Possible variables are:\\n\\t// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders. Placeholders with the \\n\\t// same ids are connected.\\n\\t// Example:\\n\\t// \\\"Print to console\\\": {\\n\\t// \\t\\\"prefix\\\": \\\"log\\\",\\n\\t// \\t\\\"body\\\": [\\n\\t// \\t\\t\\\"console.log('$1');\\\",\\n\\t// \\t\\t\\\"$2\\\"\\n\\t// \\t],\\n\\t// \\t\\\"description\\\": \\\"Log output to console\\\"\\n\\t// }\\n}\"}}", "extensions": "[{\"identifier\":{\"id\":\"aaaaronzhou.nginx-config-formatter-vscode-extension\",\"uuid\":\"187dc6f5-0396-4bdd-bb7e-3426924c71e5\"},\"displayName\":\"Nginx Config Formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"adpyke.vscode-sql-formatter\",\"uuid\":\"ac70a31d-d9ab-417b-b259-baf7cd9d6cb0\"},\"displayName\":\"SQL Formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ahmadalli.vscode-nginx-conf\",\"uuid\":\"9a97436d-76aa-479c-8ae9-db2f400a7b04\"},\"displayName\":\"NGINX Configuration Language Support\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ale<PERSON>gnani.project-manager\",\"uuid\":\"1b747f06-3789-4ebd-ac99-f1fe430c3347\"},\"displayName\":\"Project Manager\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"arturock.gitstash\",\"uuid\":\"c6f98943-7f9b-4776-b2a8-409227a481e2\"},\"displayName\":\"Git Stash\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"augment.vscode-augment\",\"uuid\":\"fc0e137d-e132-47ed-9455-c4636fa5b897\"},\"displayName\":\"Augment\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"charliermarsh.ruff\",\"uuid\":\"c2ca9b43-fa38-44fc-928e-5125970b9c00\"},\"displayName\":\"Ruff\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"christian-kohler.npm-intellisense\",\"uuid\":\"************************************\"},\"displayName\":\"npm Intellisense\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"codeium.codeium\",\"uuid\":\"acab4f40-b6db-42ec-bcd1-01802cbdd988\"},\"displayName\":\"Windsurf Plugin (formerly Codeium): AI Coding Autocomplete and Chat for Python, JavaScript, TypeScript, and more\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"codezombiech.gitignore\",\"uuid\":\"3e891cf9-53cb-49a3-8d01-8f0b1f0afb29\"},\"displayName\":\"gitignore\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"cweijan.dbclient-jdbc\",\"uuid\":\"1fe0d785-8db6-44ef-8113-cb0d98a88473\"},\"displayName\":\"Database Client JDBC\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"cweijan.vscode-redis-client\",\"uuid\":\"6c8a94fa-972f-4089-b688-78f8e2875023\"},\"displayName\":\"Redis\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dart-code.dart-code\",\"uuid\":\"f57f68ea-9ee8-42b5-9a97-041d3e4278c4\"},\"displayName\":\"Dart\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dart-code.flutter\",\"uuid\":\"f6c3ec04-6057-4d9c-b997-69cba07a6158\"},\"displayName\":\"Flutter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dbaeumer.vscode-eslint\",\"uuid\":\"583b2b34-2c1e-4634-8c0b-0b82e283ea3a\"},\"displayName\":\"ESLint\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"dbcode.dbcode\",\"uuid\":\"a172cbe5-f099-458f-b024-382d48f54aa0\"},\"displayName\":\"DBCode - Database Management\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"docker.docker\",\"uuid\":\"************************************\"},\"displayName\":\"Docker DX\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.githistory\",\"uuid\":\"5960f38e-0bbe-4644-8f9c-9c8824e82511\"},\"displayName\":\"Git History\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.python-environment-manager\",\"uuid\":\"0c9f60fd-5588-42f7-9176-e80c3ae111ec\"},\"displayName\":\"Python Environment Manager (deprecated)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"donjayamanne.python-extension-pack\",\"uuid\":\"f5188937-53e0-45bb-a16d-61231003fa3b\"},\"displayName\":\"Python Extension Pack\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"eamodio.gitlens\",\"uuid\":\"4de763bd-505d-4978-9575-2b7696ecf94e\"},\"displayName\":\"GitLens — Git supercharged\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"emilast.logfilehighlighter\",\"uuid\":\"e8b488af-fccf-4adf-b60c-fc7455bea107\"},\"displayName\":\"Log File Highlighter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"esbenp.prettier-vscode\",\"uuid\":\"96fa4707-6983-4489-b7c5-d5ffdfdcce90\"},\"displayName\":\"Prettier - Code formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"esphome.esphome-vscode\",\"uuid\":\"b48b693e-8a2d-4491-a373-c98cde694c27\"},\"displayName\":\"ESPHome\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"flutterando.flutter-mobx\",\"uuid\":\"4da4c97b-b272-4552-a789-0af9ddaec7f9\"},\"displayName\":\"flutter_mobx\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"formulahendry.auto-close-tag\",\"uuid\":\"d3836729-9cc1-42c1-b2af-d50071f57d29\"},\"displayName\":\"Auto Close Tag\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"formulahendry.auto-rename-tag\",\"uuid\":\"6e440e71-8ed9-4f25-bb78-4b13096b8a03\"},\"displayName\":\"Auto Rename Tag\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"foxundermoon.shell-format\",\"uuid\":\"5fb19573-2183-4cf2-b53d-0fb869dae7ae\"},\"displayName\":\"shell-format\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.copilot\",\"uuid\":\"23c4aeee-f844-43cd-b53e-1113e483f1a6\"},\"displayName\":\"GitHub Copilot\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.copilot-chat\",\"uuid\":\"7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f\"},\"displayName\":\"GitHub Copilot Chat\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.remotehub\",\"uuid\":\"fc7d7e85-2e58-4c1c-97a3-2172ed9a77cd\"},\"displayName\":\"GitHub Repositories\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.vscode-github-actions\",\"uuid\":\"04f49bfc-8330-4eee-8237-ea938fb755ef\"},\"displayName\":\"GitHub Actions\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"github.vscode-pull-request-github\",\"uuid\":\"69ddd764-339a-4ecc-97c1-9c4ece58e36d\"},\"displayName\":\"GitHub Pull Requests\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"gitworktrees.git-worktrees\",\"uuid\":\"7a773281-9fa5-47ce-8e6f-aabeec3859a0\"},\"displayName\":\"Git Worktrees\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"google.geminicodeassist\",\"uuid\":\"51643712-2cb2-4384-b7cc-d55b01b8274b\"},\"displayName\":\"Gemini Code Assist\",\"version\":\"2.41.0-insiders.1\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"graphql.vscode-graphql\",\"uuid\":\"55ef6448-487b-49a0-a66e-4d2d9bb82229\"},\"displayName\":\"GraphQL: Language Feature Support\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"graphql.vscode-graphql-syntax\",\"uuid\":\"e1ab76b1-9acd-4ffa-baf7-1d9eaf7cf3d2\"},\"displayName\":\"GraphQL: Syntax Highlighting\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"hbenl.vscode-test-explorer\",\"uuid\":\"ff96f1b4-a4b8-45ef-8ecf-c232c0cb75c8\"},\"displayName\":\"Test Explorer UI\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"hediet.vscode-drawio\",\"uuid\":\"ea6a6046-2132-421f-a984-664909fcf0b8\"},\"displayName\":\"Draw.io Integration\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"k--kato.intellij-idea-keybindings\",\"uuid\":\"f30b63fa-e34a-40af-a573-5de5ecfb6c5e\"},\"displayName\":\"IntelliJ IDEA Keybindings\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"keesschollaart.vscode-home-assistant\",\"uuid\":\"d2d3c048-6fab-4e8f-a618-c1b9559cfd56\"},\"displayName\":\"Home Assistant Config Helper\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"kevinrose.vsc-python-indent\",\"uuid\":\"f3cbfb84-b1e1-40ff-b70f-************\"},\"displayName\":\"Python Indent\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"littlefoxteam.vscode-python-test-adapter\",\"uuid\":\"912fd5ae-d9f2-4c31-8a0b-d7fb669afbb4\"},\"displayName\":\"Python Test Explorer for Visual Studio Code\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"luanpotter.dart-import\",\"uuid\":\"81fc200f-fa28-4491-8d20-cda841da5566\"},\"displayName\":\"dart-import\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"lukas-tr.materialdesignicons-intellisense\",\"uuid\":\"6bddf0ae-fff2-4b40-9091-3361abfb87d4\"},\"displayName\":\"Material Design Icons Intellisense\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mechatroner.rainbow-csv\",\"uuid\":\"3792588c-3d35-442d-91ea-fe6a755e8155\"},\"displayName\":\"Rainbow CSV\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mikestead.dotenv\",\"uuid\":\"532533c9-a894-4a58-9eee-bbfbe7c06f71\"},\"displayName\":\"DotENV\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"mindaro-dev.file-downloader\",\"uuid\":\"c5f9ea77-0f11-4bd6-9736-341deca7a35c\"},\"displayName\":\"File Downloader\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-azuretools.vscode-containers\",\"uuid\":\"2cd1d691-3d69-4d2d-ae39-fda4bc4cfd3d\"},\"displayName\":\"Container Tools\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-azuretools.vscode-docker\",\"uuid\":\"************************************\"},\"displayName\":\"Docker\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-kubernetes-tools.vscode-kubernetes-tools\",\"uuid\":\"4837e4f3-1b01-4732-b1a6-daa57ef64cab\"},\"displayName\":\"Kubernetes\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.debugpy\",\"uuid\":\"4bd5d2c9-9d65-401a-b0b2-7498d9f17615\"},\"displayName\":\"Python Debugger\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.python\",\"uuid\":\"f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5\"},\"displayName\":\"Python\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-python.vscode-pylance\",\"uuid\":\"364d2426-116a-433a-a5d8-a5098dc3afbd\"},\"displayName\":\"Pylance\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter\",\"uuid\":\"6c2f1801-1e7f-45b2-9b5c-7782f1e076e8\"},\"displayName\":\"Jupyter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-keymap\",\"uuid\":\"9f6dc8db-620c-4844-b8c5-e74914f1be27\"},\"displayName\":\"Jupyter Keymap\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.jupyter-renderers\",\"uuid\":\"b15c72f8-d5fe-421a-a4f7-27ed9f6addbf\"},\"displayName\":\"Jupyter Notebook Renderers\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-cell-tags\",\"uuid\":\"ab4fb32a-befb-4102-adf9-1652d0cd6a5e\"},\"displayName\":\"Jupyter Cell Tags\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-toolsai.vscode-jupyter-slideshow\",\"uuid\":\"e153ca70-b543-4865-b4c5-b31d34185948\"},\"displayName\":\"Jupyter Slide Show\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-containers\",\"uuid\":\"93ce222b-5f6f-49b7-9ab1-a0463c6238df\"},\"displayName\":\"Dev Containers\",\"preRelease\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh\",\"uuid\":\"607fd052-be03-4363-b657-2bd62b83d28a\"},\"displayName\":\"Remote - SSH\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-ssh-edit\",\"uuid\":\"bfeaf631-bcff-4908-93ed-fda4ef9a0c5c\"},\"displayName\":\"Remote - SSH: Editing Configuration Files\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.remote-wsl\",\"uuid\":\"f0c5397b-d357-4197-99f0-cb4202f22818\"},\"displayName\":\"WSL\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode-remote.vscode-remote-extensionpack\",\"uuid\":\"23d72dfc-8dd1-4e30-926e-8783b4378f13\"},\"displayName\":\"Remote Development\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.makefile-tools\",\"uuid\":\"e09cf600-90a1-414e-92a0-031f1a5391c6\"},\"displayName\":\"Makefile Tools\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-explorer\",\"uuid\":\"11858313-52cc-4e57-b3e4-d7b65281e34b\"},\"displayName\":\"Remote Explorer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-repositories\",\"uuid\":\"cf5142f0-3701-4992-980c-9895a750addf\"},\"displayName\":\"Remote Repositories\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.remote-server\",\"uuid\":\"105c0b3c-07a9-4156-a4fc-4141040eb07e\"},\"displayName\":\"Remote - Tunnels\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.test-adapter-converter\",\"uuid\":\"47210ec2-0324-4cbb-9523-9dff02a5f9ec\"},\"displayName\":\"Test Adapter Converter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vscode.vscode-serial-monitor\",\"uuid\":\"17dcad22-0c4b-428f-8a04-5d2629f1480e\"},\"displayName\":\"Serial Monitor\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"ms-vsliveshare.vsliveshare\",\"uuid\":\"5a6dc0d5-dc02-4121-8e24-cad33a2ff0af\"},\"displayName\":\"Live Share\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"nefrob.vscode-just-syntax\",\"uuid\":\"4d059c6e-aaea-42e3-97f5-f4fd35b70d3d\"},\"displayName\":\"vscode-just\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"njpwerner.autodocstring\",\"uuid\":\"2d6fea35-f68e-461d-9b7b-5cd05be99451\"},\"displayName\":\"autoDocstring - Python Docstring Generator\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"oderwat.indent-rainbow\",\"uuid\":\"eaa2127d-cb69-4ab9-8505-a60c9ee5f28b\"},\"applicationScoped\":false},{\"identifier\":{\"id\":\"rangav.vscode-thunder-client\",\"uuid\":\"2fd56207-78ef-49d4-95d2-9b801eee4dbf\"},\"displayName\":\"Thunder Client\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"raynigon.nginx-formatter\",\"uuid\":\"1a7b1f64-469d-4116-bb35-508ea3894f88\"},\"displayName\":\"nginx-formatter\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"redhat.vscode-yaml\",\"uuid\":\"2061917f-f76a-458a-8da9-f162de22b97e\"},\"displayName\":\"YAML\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rockingskier.copy-copy-paste\",\"uuid\":\"06457f7c-6032-4500-880d-1575c1b1cd01\"},\"displayName\":\"Copy Copy Paste\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rooveterinaryinc.roo-cline\",\"uuid\":\"4ce92b26-476a-4dd1-bf50-a8df00b87a74\"},\"displayName\":\"Roo Code (prev. Roo Cline)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rust-lang.rust-analyzer\",\"uuid\":\"06574cb4-e5dc-4631-8174-a543a4533621\"},\"displayName\":\"rust-analyzer\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"rvest.vs-code-prettier-eslint\",\"uuid\":\"d4b06bd6-36a0-469f-be55-c0a73413b688\"},\"displayName\":\"Prettier ESLint\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"saoudrizwan.claude-dev\",\"uuid\":\"ad94c633-a9d5-4f78-b85f-c664e7d91a0f\"},\"displayName\":\"Cline\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"streetsidesoftware.code-spell-checker\",\"uuid\":\"f6dbd813-b0a0-42c1-90ea-10dde9d925a7\"},\"displayName\":\"Code Spell Checker\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"stylelint.vscode-stylelint\",\"uuid\":\"ec35b5a3-9802-4c68-b5ff-e85f19ec0977\"},\"displayName\":\"Stylelint\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"subframe7536.custom-ui-style\",\"uuid\":\"5d260afc-8d75-4731-a881-66951c630816\"},\"displayName\":\"Custom UI Style\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"tamasfe.even-better-toml\",\"uuid\":\"b2215d5f-675e-4a2b-b6ac-1ca737518b78\"},\"displayName\":\"Even Better TOML\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"teclado.vscode-nginx-format\",\"uuid\":\"40261fd4-b94e-428c-9ce4-c8e9f7acda78\"},\"displayName\":\"nginx-format\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"usernamehw.errorlens\",\"uuid\":\"9d8c32ab-354c-4daf-a9bf-20b633734435\"},\"displayName\":\"Error Lens\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vadimcn.vscode-lldb\",\"uuid\":\"bee31e34-a44b-4a76-9ec2-e9fd1439a0f6\"},\"displayName\":\"CodeLLDB\",\"version\":\"1.11.5\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"visualstudioexptteam.intellicode-api-usage-examples\",\"uuid\":\"9fa2a00e-3bfa-4c2a-abc4-a865bb2b5cf3\"},\"displayName\":\"IntelliCode API Usage Examples\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"visualstudioexptteam.vscodeintellicode\",\"uuid\":\"876e8f93-74d0-4f4f-91b7-34a09f19f444\"},\"displayName\":\"IntelliCode\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vscode-icons-team.vscode-icons\",\"uuid\":\"9ccc1dd7-7ec4-4a46-bd4f-7d7b8b9d322a\"},\"displayName\":\"vscode-icons\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"vue.volar\",\"uuid\":\"a95ee795-1576-4ffa-acda-8d6e6a95c584\"},\"displayName\":\"Vue (Official)\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"wholroyd.jinja\",\"uuid\":\"c941a679-d500-46a8-b2a9-************\"},\"displayName\":\"Jinja\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"william-voyek.vscode-nginx\",\"uuid\":\"414b2873-c80e-4dc6-9031-bd185cfb3944\"},\"displayName\":\"NGINX Configuration\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"xshrim.txt-syntax\",\"uuid\":\"c5900888-28e9-40d6-84d0-d1645e62f4cc\"},\"displayName\":\"Txt Syntax\",\"disabled\":true,\"applicationScoped\":false},{\"identifier\":{\"id\":\"yoavbls.pretty-ts-errors\",\"uuid\":\"1e149c89-8f97-447e-863d-1146f0ad1b70\"},\"displayName\":\"Pretty TypeScript Errors\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"zhuangtongfa.material-theme\",\"uuid\":\"26a529c9-2654-4b95-a63f-02f6a52429e6\"},\"displayName\":\"One Dark Pro\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"zokugun.cron-tasks\",\"uuid\":\"b7bbbbd5-7a39-4312-87aa-ef6a41de67fb\"},\"displayName\":\"Cron Tasks\",\"applicationScoped\":false},{\"identifier\":{\"id\":\"zokugun.sync-settings\",\"uuid\":\"96e8e36b-03bd-44a6-b158-b7ad48d2e58f\"},\"displayName\":\"Sync Settings\",\"applicationScoped\":false}]", "globalState": "{\"storage\":{\"workbench.activity.pinnedViewlets2\":\"[{\\\"id\\\":\\\"workbench.view.explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.view.search\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.view.scm\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.view.debug\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"workbench.view.extensions\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.github-cweijan-mysql\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.github-dbclient-history\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.github-cweijan-nosql\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.sidebarDevToolsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.sidebarDevTools\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.sidebarDevToolsInspectorContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.sidebarDevToolsDeepLinksContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.dockerView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.remote\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"workbench.view.extension.flutterPropertyEditor\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.localHistory\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":18},{\\\"id\\\":\\\"workbench.view.extension.test\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.PearAI\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.references-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.dbcodeActivitybarContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":12},{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.flutter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.package-explorer\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":19},{\\\"id\\\":\\\"workbench.view.extension.gitlens\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"workbench.view.extension.gitlensInspect\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":14},{\\\"id\\\":\\\"workbench.view.extension.gitlensPatch\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":15},{\\\"id\\\":\\\"workbench.view.extension.github-actions\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":24},{\\\"id\\\":\\\"workbench.view.extension.github-pull-requests\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.github-pull-request\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.kubernetesView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":29},{\\\"id\\\":\\\"workbench.view.extension.jupyter\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":30},{\\\"id\\\":\\\"workbench.view.extension.copilot-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":23},{\\\"id\\\":\\\"workbench.view.extension.makefile__viewContainer\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":31},{\\\"id\\\":\\\"workbench.view.extension.liveshare\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":32},{\\\"id\\\":\\\"workbench.view.extension.sqltoolsActivityBarContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.thunder-client\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":33},{\\\"id\\\":\\\"workbench.view.extension.cspell-info-explorer\\\",\\\"pinned\\\":false,\\\"visible\\\":false,\\\"order\\\":38},{\\\"id\\\":\\\"workbench.view.extension.project-manager\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.containersView\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":16},{\\\"id\\\":\\\"workbench.view.extension.cspell-regexp-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":39},{\\\"id\\\":\\\"workbench.view.extension.rustSyntaxTreeContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":35},{\\\"id\\\":\\\"workbench.panel.chatSidebar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"userDataProfiles\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.sync\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.editSessions\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.view.extension.cspell-explorer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":30}]\",\"workbench.panel.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.panel.markers\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.panel.repl\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.panel.testResults\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"~remote.forwardedPortsContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.view.extension.dbclient-search-result\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.augment-panel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.view.extension.dbclient-variable-config-view\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.view.extension.dbcodePanelContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.view.extension.gitlensPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.jupyter-variables\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":11},{\\\"id\\\":\\\"workbench.view.extension.vscode-serial-monitor-tools\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":10},{\\\"id\\\":\\\"workbench.view.extension.cspellPanel\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":13},{\\\"id\\\":\\\"terminal\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"refactorPreview\\\",\\\"pinned\\\":true,\\\"visible\\\":false},{\\\"id\\\":\\\"workbench.views.service.panel.3b6d4b07-25d4-4235-a25e-2befe3bca161\\\",\\\"pinned\\\":true,\\\"visible\\\":false}]\",\"workbench.view.debug.state.hidden\":\"[{\\\"id\\\":\\\"workbench.debug.welcome\\\",\\\"isHidden\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.debug.watchExpressionsView\\\",\\\"isHidden\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"workbench.debug.callStackView\\\",\\\"isHidden\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"workbench.debug.loadedScriptsView\\\",\\\"isHidden\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"workbench.debug.breakPointsView\\\",\\\"isHidden\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"jsBrowserBreakpoints\\\",\\\"isHidden\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"jsExcludedCallers\\\",\\\"isHidden\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"CppSshTargetsView\\\",\\\"isHidden\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"platformio-debug.peripherals\\\",\\\"isHidden\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"platformio-debug.registers\\\",\\\"isHidden\\\":false,\\\"order\\\":5},{\\\"id\\\":\\\"platformio-debug.memory\\\",\\\"isHidden\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"platformio-debug.disassembly\\\",\\\"isHidden\\\":false,\\\"order\\\":7},{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"jsDebugNetworkTree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"lldb.loadedModules\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"lldb.excludedCallers\\\",\\\"isHidden\\\":false}]\",\"colorThemeData\":\"{\\\"id\\\":\\\"vs-dark vscode-theme-defaults-themes-dark_modern-json\\\",\\\"label\\\":\\\"Dark Modern\\\",\\\"settingsId\\\":\\\"Default Dark Modern\\\",\\\"themeTokenColors\\\":[{\\\"settings\\\":{\\\"foreground\\\":\\\"#D4D4D4\\\"},\\\"scope\\\":[\\\"meta.embedded\\\",\\\"source.groovy.embedded\\\",\\\"string meta.image.inline.markdown\\\",\\\"variable.legacy.builtin.python\\\"]},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"emphasis\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\"},\\\"scope\\\":\\\"strong\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#000080\\\"},\\\"scope\\\":\\\"header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"comment\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"constant.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":[\\\"constant.numeric\\\",\\\"variable.other.enummember\\\",\\\"keyword.operator.plus.exponent\\\",\\\"keyword.operator.minus.exponent\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#646695\\\"},\\\"scope\\\":\\\"constant.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"entity.name.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.name.tag.css\\\",\\\"entity.name.tag.less\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"entity.other.attribute-name\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":[\\\"entity.other.attribute-name.class.css\\\",\\\"source.css entity.other.attribute-name.class\\\",\\\"entity.other.attribute-name.id.css\\\",\\\"entity.other.attribute-name.parent-selector.css\\\",\\\"entity.other.attribute-name.parent.less\\\",\\\"source.css entity.other.attribute-name.pseudo-class\\\",\\\"entity.other.attribute-name.pseudo-element.css\\\",\\\"source.css.less entity.other.attribute-name.id\\\",\\\"entity.other.attribute-name.scss\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#f44747\\\"},\\\"scope\\\":\\\"invalid\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"underline\\\"},\\\"scope\\\":\\\"markup.underline\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.bold\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"bold\\\",\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.heading\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"italic\\\"},\\\"scope\\\":\\\"markup.italic\\\"},{\\\"settings\\\":{\\\"fontStyle\\\":\\\"strikethrough\\\"},\\\"scope\\\":\\\"markup.strikethrough\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"markup.inserted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.deleted\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"markup.changed\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6A9955\\\"},\\\"scope\\\":\\\"punctuation.definition.quote.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#6796e6\\\"},\\\"scope\\\":\\\"punctuation.definition.list.begin.markdown\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"markup.inline.raw\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#808080\\\"},\\\"scope\\\":\\\"punctuation.definition.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"meta.preprocessor\\\",\\\"entity.name.function.preprocessor\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"meta.preprocessor.string\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"meta.preprocessor.numeric\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"meta.structure.dictionary.key.python\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"meta.diff.header\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"storage.type\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"storage.modifier\\\",\\\"keyword.operator.noexcept\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":[\\\"string\\\",\\\"meta.embedded.assembly\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.tag\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#ce9178\\\"},\\\"scope\\\":\\\"string.value\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":\\\"string.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.definition.template-expression.begin\\\",\\\"punctuation.definition.template-expression.end\\\",\\\"punctuation.section.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"meta.template.expression\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":[\\\"support.type.vendored.property-name\\\",\\\"support.type.property-name\\\",\\\"source.css variable\\\",\\\"source.coffee.embedded\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"keyword.control\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":\\\"keyword.operator\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"keyword.operator.new\\\",\\\"keyword.operator.expression\\\",\\\"keyword.operator.cast\\\",\\\"keyword.operator.sizeof\\\",\\\"keyword.operator.alignof\\\",\\\"keyword.operator.typeid\\\",\\\"keyword.operator.alignas\\\",\\\"keyword.operator.instanceof\\\",\\\"keyword.operator.logical.python\\\",\\\"keyword.operator.wordlike\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"keyword.other.unit\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"punctuation.section.embedded.begin.php\\\",\\\"punctuation.section.embedded.end.php\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9cdcfe\\\"},\\\"scope\\\":\\\"support.function.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#b5cea8\\\"},\\\"scope\\\":\\\"constant.sha.git-rebase\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d4d4d4\\\"},\\\"scope\\\":[\\\"storage.modifier.import.java\\\",\\\"variable.language.wildcard.java\\\",\\\"storage.modifier.package.java\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":\\\"variable.language\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"entity.name.function\\\",\\\"support.function\\\",\\\"support.constant.handlebars\\\",\\\"source.powershell variable.other.member\\\",\\\"entity.name.operator.custom-literal\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"support.class\\\",\\\"support.type\\\",\\\"entity.name.type\\\",\\\"entity.name.namespace\\\",\\\"entity.other.attribute\\\",\\\"entity.name.scope-resolution\\\",\\\"entity.name.class\\\",\\\"storage.type.numeric.go\\\",\\\"storage.type.byte.go\\\",\\\"storage.type.boolean.go\\\",\\\"storage.type.string.go\\\",\\\"storage.type.uintptr.go\\\",\\\"storage.type.error.go\\\",\\\"storage.type.rune.go\\\",\\\"storage.type.cs\\\",\\\"storage.type.generic.cs\\\",\\\"storage.type.modifier.cs\\\",\\\"storage.type.variable.cs\\\",\\\"storage.type.annotation.java\\\",\\\"storage.type.generic.java\\\",\\\"storage.type.java\\\",\\\"storage.type.object.array.java\\\",\\\"storage.type.primitive.array.java\\\",\\\"storage.type.primitive.java\\\",\\\"storage.type.token.java\\\",\\\"storage.type.groovy\\\",\\\"storage.type.annotation.groovy\\\",\\\"storage.type.parameters.groovy\\\",\\\"storage.type.generic.groovy\\\",\\\"storage.type.object.array.groovy\\\",\\\"storage.type.primitive.array.groovy\\\",\\\"storage.type.primitive.groovy\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4EC9B0\\\"},\\\"scope\\\":[\\\"meta.type.cast.expr\\\",\\\"meta.type.new.expr\\\",\\\"support.constant.math\\\",\\\"support.constant.dom\\\",\\\"support.constant.json\\\",\\\"entity.other.inherited-class\\\",\\\"punctuation.separator.namespace.ruby\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C586C0\\\"},\\\"scope\\\":[\\\"keyword.control\\\",\\\"source.cpp keyword.operator.new\\\",\\\"keyword.operator.delete\\\",\\\"keyword.other.using\\\",\\\"keyword.other.directive.using\\\",\\\"keyword.other.operator\\\",\\\"entity.name.operator\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"variable\\\",\\\"meta.definition.variable.name\\\",\\\"support.variable\\\",\\\"entity.name.variable\\\",\\\"constant.other.placeholder\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#4FC1FF\\\"},\\\"scope\\\":[\\\"variable.other.constant\\\",\\\"variable.other.enummember\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#9CDCFE\\\"},\\\"scope\\\":[\\\"meta.object-literal.key\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"support.constant.property-value\\\",\\\"support.constant.font-name\\\",\\\"support.constant.media-type\\\",\\\"support.constant.media\\\",\\\"constant.other.color.rgb-value\\\",\\\"constant.other.rgb-value\\\",\\\"support.constant.color\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#CE9178\\\"},\\\"scope\\\":[\\\"punctuation.definition.group.regexp\\\",\\\"punctuation.definition.group.assertion.regexp\\\",\\\"punctuation.definition.character-class.regexp\\\",\\\"punctuation.character.set.begin.regexp\\\",\\\"punctuation.character.set.end.regexp\\\",\\\"keyword.operator.negation.regexp\\\",\\\"support.other.parenthesis.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d16969\\\"},\\\"scope\\\":[\\\"constant.character.character-class.regexp\\\",\\\"constant.other.character-class.set.regexp\\\",\\\"constant.other.character-class.regexp\\\",\\\"constant.character.set.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#DCDCAA\\\"},\\\"scope\\\":[\\\"keyword.operator.or.regexp\\\",\\\"keyword.control.anchor.regexp\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"keyword.operator.quantifier.regexp\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#569cd6\\\"},\\\"scope\\\":[\\\"constant.character\\\",\\\"constant.other.option\\\"]},{\\\"settings\\\":{\\\"foreground\\\":\\\"#d7ba7d\\\"},\\\"scope\\\":\\\"constant.character.escape\\\"},{\\\"settings\\\":{\\\"foreground\\\":\\\"#C8C8C8\\\"},\\\"scope\\\":\\\"entity.name.label\\\"}],\\\"semanticTokenRules\\\":[{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#d4d4d4\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"newOperator\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#c586c0\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"stringLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#ce9178\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"customLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#dcdcaa\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}},{\\\"_selector\\\":\\\"numberLiteral\\\",\\\"_style\\\":{\\\"_foreground\\\":\\\"#b5cea8\\\",\\\"_bold\\\":null,\\\"_underline\\\":null,\\\"_italic\\\":null,\\\"_strikethrough\\\":null}}],\\\"extensionData\\\":{\\\"_extensionId\\\":\\\"vscode.theme-defaults\\\",\\\"_extensionIsBuiltin\\\":true,\\\"_extensionName\\\":\\\"theme-defaults\\\",\\\"_extensionPublisher\\\":\\\"vscode\\\"},\\\"themeSemanticHighlighting\\\":true,\\\"colorMap\\\":{\\\"checkbox.border\\\":\\\"#3c3c3c\\\",\\\"editor.background\\\":\\\"#1f1f1f\\\",\\\"editor.foreground\\\":\\\"#cccccc\\\",\\\"editor.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"editorIndentGuide.background1\\\":\\\"#404040\\\",\\\"editorIndentGuide.activeBackground1\\\":\\\"#707070\\\",\\\"editor.selectionHighlightBackground\\\":\\\"#add6ff26\\\",\\\"list.dropBackground\\\":\\\"#383b3d\\\",\\\"activityBarBadge.background\\\":\\\"#0078d4\\\",\\\"sideBarTitle.foreground\\\":\\\"#cccccc\\\",\\\"input.placeholderForeground\\\":\\\"#989898\\\",\\\"menu.background\\\":\\\"#1f1f1f\\\",\\\"menu.foreground\\\":\\\"#cccccc\\\",\\\"menu.separatorBackground\\\":\\\"#454545\\\",\\\"menu.border\\\":\\\"#454545\\\",\\\"menu.selectionBackground\\\":\\\"#0078d4\\\",\\\"statusBarItem.remoteForeground\\\":\\\"#ffffff\\\",\\\"statusBarItem.remoteBackground\\\":\\\"#0078d4\\\",\\\"ports.iconRunningProcessForeground\\\":\\\"#369432\\\",\\\"sideBarSectionHeader.background\\\":\\\"#181818\\\",\\\"sideBarSectionHeader.border\\\":\\\"#2b2b2b\\\",\\\"tab.selectedBackground\\\":\\\"#222222\\\",\\\"tab.selectedForeground\\\":\\\"#ffffffa0\\\",\\\"tab.lastPinnedBorder\\\":\\\"#cccccc33\\\",\\\"list.activeSelectionIconForeground\\\":\\\"#ffffff\\\",\\\"terminal.inactiveSelectionBackground\\\":\\\"#3a3d41\\\",\\\"widget.border\\\":\\\"#313131\\\",\\\"actionBar.toggledBackground\\\":\\\"#383a49\\\",\\\"activityBar.activeBorder\\\":\\\"#0078d4\\\",\\\"activityBar.background\\\":\\\"#181818\\\",\\\"activityBar.border\\\":\\\"#2b2b2b\\\",\\\"activityBar.foreground\\\":\\\"#d7d7d7\\\",\\\"activityBar.inactiveForeground\\\":\\\"#868686\\\",\\\"activityBarBadge.foreground\\\":\\\"#ffffff\\\",\\\"badge.background\\\":\\\"#616161\\\",\\\"badge.foreground\\\":\\\"#f8f8f8\\\",\\\"button.background\\\":\\\"#0078d4\\\",\\\"button.border\\\":\\\"#ffffff12\\\",\\\"button.foreground\\\":\\\"#ffffff\\\",\\\"button.hoverBackground\\\":\\\"#026ec1\\\",\\\"button.secondaryBackground\\\":\\\"#313131\\\",\\\"button.secondaryForeground\\\":\\\"#cccccc\\\",\\\"button.secondaryHoverBackground\\\":\\\"#3c3c3c\\\",\\\"chat.slashCommandBackground\\\":\\\"#34414b\\\",\\\"chat.slashCommandForeground\\\":\\\"#40a6ff\\\",\\\"chat.editedFileForeground\\\":\\\"#e2c08d\\\",\\\"checkbox.background\\\":\\\"#313131\\\",\\\"debugToolBar.background\\\":\\\"#181818\\\",\\\"descriptionForeground\\\":\\\"#9d9d9d\\\",\\\"dropdown.background\\\":\\\"#313131\\\",\\\"dropdown.border\\\":\\\"#3c3c3c\\\",\\\"dropdown.foreground\\\":\\\"#cccccc\\\",\\\"dropdown.listBackground\\\":\\\"#1f1f1f\\\",\\\"editor.findMatchBackground\\\":\\\"#9e6a03\\\",\\\"editorGroup.border\\\":\\\"#ffffff17\\\",\\\"editorGroupHeader.tabsBackground\\\":\\\"#181818\\\",\\\"editorGroupHeader.tabsBorder\\\":\\\"#2b2b2b\\\",\\\"editorGutter.addedBackground\\\":\\\"#2ea043\\\",\\\"editorGutter.deletedBackground\\\":\\\"#f85149\\\",\\\"editorGutter.modifiedBackground\\\":\\\"#0078d4\\\",\\\"editorLineNumber.activeForeground\\\":\\\"#cccccc\\\",\\\"editorLineNumber.foreground\\\":\\\"#6e7681\\\",\\\"editorOverviewRuler.border\\\":\\\"#010409\\\",\\\"editorWidget.background\\\":\\\"#202020\\\",\\\"errorForeground\\\":\\\"#f85149\\\",\\\"focusBorder\\\":\\\"#0078d4\\\",\\\"foreground\\\":\\\"#cccccc\\\",\\\"icon.foreground\\\":\\\"#cccccc\\\",\\\"input.background\\\":\\\"#313131\\\",\\\"input.border\\\":\\\"#3c3c3c\\\",\\\"input.foreground\\\":\\\"#cccccc\\\",\\\"inputOption.activeBackground\\\":\\\"#2489db82\\\",\\\"inputOption.activeBorder\\\":\\\"#2488db\\\",\\\"keybindingLabel.foreground\\\":\\\"#cccccc\\\",\\\"notificationCenterHeader.background\\\":\\\"#1f1f1f\\\",\\\"notificationCenterHeader.foreground\\\":\\\"#cccccc\\\",\\\"notifications.background\\\":\\\"#1f1f1f\\\",\\\"notifications.border\\\":\\\"#2b2b2b\\\",\\\"notifications.foreground\\\":\\\"#cccccc\\\",\\\"panel.background\\\":\\\"#181818\\\",\\\"panel.border\\\":\\\"#2b2b2b\\\",\\\"panelInput.border\\\":\\\"#2b2b2b\\\",\\\"panelTitle.activeBorder\\\":\\\"#0078d4\\\",\\\"panelTitle.activeForeground\\\":\\\"#cccccc\\\",\\\"panelTitle.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"peekViewEditor.background\\\":\\\"#1f1f1f\\\",\\\"peekViewEditor.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"peekViewResult.background\\\":\\\"#1f1f1f\\\",\\\"peekViewResult.matchHighlightBackground\\\":\\\"#bb800966\\\",\\\"pickerGroup.border\\\":\\\"#3c3c3c\\\",\\\"progressBar.background\\\":\\\"#0078d4\\\",\\\"quickInput.background\\\":\\\"#222222\\\",\\\"quickInput.foreground\\\":\\\"#cccccc\\\",\\\"settings.dropdownBackground\\\":\\\"#313131\\\",\\\"settings.dropdownBorder\\\":\\\"#3c3c3c\\\",\\\"settings.headerForeground\\\":\\\"#ffffff\\\",\\\"settings.modifiedItemIndicator\\\":\\\"#bb800966\\\",\\\"sideBar.background\\\":\\\"#181818\\\",\\\"sideBar.border\\\":\\\"#2b2b2b\\\",\\\"sideBar.foreground\\\":\\\"#cccccc\\\",\\\"sideBarSectionHeader.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.background\\\":\\\"#181818\\\",\\\"statusBar.border\\\":\\\"#2b2b2b\\\",\\\"statusBar.debuggingBackground\\\":\\\"#0078d4\\\",\\\"statusBar.debuggingForeground\\\":\\\"#ffffff\\\",\\\"statusBar.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBar.foreground\\\":\\\"#cccccc\\\",\\\"statusBar.noFolderBackground\\\":\\\"#1f1f1f\\\",\\\"statusBarItem.focusBorder\\\":\\\"#0078d4\\\",\\\"statusBarItem.prominentBackground\\\":\\\"#6e768166\\\",\\\"tab.activeBackground\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorder\\\":\\\"#1f1f1f\\\",\\\"tab.activeBorderTop\\\":\\\"#0078d4\\\",\\\"tab.activeForeground\\\":\\\"#ffffff\\\",\\\"tab.selectedBorderTop\\\":\\\"#6caddf\\\",\\\"tab.border\\\":\\\"#2b2b2b\\\",\\\"tab.hoverBackground\\\":\\\"#1f1f1f\\\",\\\"tab.inactiveBackground\\\":\\\"#181818\\\",\\\"tab.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"tab.unfocusedActiveBorder\\\":\\\"#1f1f1f\\\",\\\"tab.unfocusedActiveBorderTop\\\":\\\"#2b2b2b\\\",\\\"tab.unfocusedHoverBackground\\\":\\\"#1f1f1f\\\",\\\"terminal.foreground\\\":\\\"#cccccc\\\",\\\"terminal.tab.activeBorder\\\":\\\"#0078d4\\\",\\\"textBlockQuote.background\\\":\\\"#2b2b2b\\\",\\\"textBlockQuote.border\\\":\\\"#616161\\\",\\\"textCodeBlock.background\\\":\\\"#2b2b2b\\\",\\\"textLink.activeForeground\\\":\\\"#4daafc\\\",\\\"textLink.foreground\\\":\\\"#4daafc\\\",\\\"textPreformat.foreground\\\":\\\"#d0d0d0\\\",\\\"textPreformat.background\\\":\\\"#3c3c3c\\\",\\\"textSeparator.foreground\\\":\\\"#21262d\\\",\\\"titleBar.activeBackground\\\":\\\"#181818\\\",\\\"titleBar.activeForeground\\\":\\\"#cccccc\\\",\\\"titleBar.border\\\":\\\"#2b2b2b\\\",\\\"titleBar.inactiveBackground\\\":\\\"#1f1f1f\\\",\\\"titleBar.inactiveForeground\\\":\\\"#9d9d9d\\\",\\\"welcomePage.tileBackground\\\":\\\"#2b2b2b\\\",\\\"welcomePage.progress.foreground\\\":\\\"#0078d4\\\"},\\\"watch\\\":false}\",\"workbench.statusbar.hidden\":\"[\\\"status.workspaceTrust.1688240802746\\\",\\\"status.workspaceTrust.1688240936754\\\",\\\"status.workspaceTrust.1688240983804\\\",\\\"status.workspaceTrust.1688241056669\\\",\\\"status.workspaceTrust.1688241081137\\\",\\\"status.workspaceTrust.1688241205516\\\",\\\"status.workspaceTrust.b9d7a83195556ae8e5577dfa4020f4ef\\\",\\\"status.workspaceTrust.1688419015797\\\",\\\"status.workspaceTrust.e175cfa7826e7e033cc9369a27a1a502\\\",\\\"status.workspaceTrust.ce9338c975bd75adc0410fbe01dc166a\\\",\\\"status.workspaceTrust.1689314021761\\\",\\\"status.workspaceTrust.cf91c0bf58b777dac3d544779b62b721\\\",\\\"status.workspaceTrust.a7c74ca4b72babb84f69532085af67a7\\\",\\\"status.workspaceTrust.1689369014321\\\",\\\"status.workspaceTrust.1689447150957\\\",\\\"status.workspaceTrust.1691347951934\\\",\\\"status.workspaceTrust.1691348028951\\\",\\\"status.workspaceTrust.1691348042664\\\",\\\"status.workspaceTrust.c7e5b58db18746341a2759d2eff92299\\\",\\\"status.workspaceTrust.a80bc34021b4fd9a7ff70c2fbcd0a7bc\\\",\\\"status.workspaceTrust.1694178076842\\\",\\\"status.workspaceTrust.1694178711815\\\",\\\"status.workspaceTrust.1697367275573\\\",\\\"status.workspaceTrust.96fdb14c56508587debf2144d47ce5ee\\\",\\\"status.workspaceTrust.7320fe4ad11df4b2c87bc86d9c145a53\\\",\\\"status.workspaceTrust.1700944540659\\\",\\\"status.workspaceTrust.1703409517138\\\",\\\"status.workspaceTrust.1707472099124\\\",\\\"status.workspaceTrust.1707519063273\\\",\\\"status.workspaceTrust.e7c735adc200d37e280166fc204a8ea3\\\",\\\"status.workspaceTrust.eee11cb26a18777e363994e9f12f2356\\\",\\\"status.workspaceTrust.1709238122622\\\",\\\"GitHub.copilot.status\\\"]\",\"themeUpdatedNotificationShown\":\"true\",\"extensionsAssistant/deprecated\":\"[\\\"eg2.vscode-npm-script\\\",\\\"msjsdiag.debugger-for-chrome\\\"]\",\"commandPalette.mru.cache\":\"{\\\"usesLRU\\\":true,\\\"entries\\\":[{\\\"key\\\":\\\"workbench.action.quickOpen\\\",\\\"value\\\":1},{\\\"key\\\":\\\"prettier.createConfigFile\\\",\\\"value\\\":13},{\\\"key\\\":\\\"editor.action.formatDocument\\\",\\\"value\\\":26},{\\\"key\\\":\\\"syncSettings.viewDifferences\\\",\\\"value\\\":46},{\\\"key\\\":\\\"syncSettings.openSettings\\\",\\\"value\\\":47},{\\\"key\\\":\\\"syncSettings.upload\\\",\\\"value\\\":49},{\\\"key\\\":\\\"dart.openDevTools\\\",\\\"value\\\":52},{\\\"key\\\":\\\"editor.action.inspectTMScopes\\\",\\\"value\\\":63},{\\\"key\\\":\\\"editor.action.organizeImports\\\",\\\"value\\\":65},{\\\"key\\\":\\\"bracket-pair-color-dlw.run\\\",\\\"value\\\":76},{\\\"key\\\":\\\"workbench.action.chat.openInSidebar\\\",\\\"value\\\":97},{\\\"key\\\":\\\"workbench.action.configureRuntimeArguments\\\",\\\"value\\\":105},{\\\"key\\\":\\\"dart-import.fix\\\",\\\"value\\\":116},{\\\"key\\\":\\\"workbench.action.terminal.openSettings\\\",\\\"value\\\":117},{\\\"key\\\":\\\"remote-containers.createDevContainerFile\\\",\\\"value\\\":124},{\\\"key\\\":\\\"remote-containers.reopenInContainer\\\",\\\"value\\\":127},{\\\"key\\\":\\\"remote-containers.rebuildContainer\\\",\\\"value\\\":129},{\\\"key\\\":\\\"workbench.action.openSettingsJson\\\",\\\"value\\\":132},{\\\"key\\\":\\\"workbench.action.reloadWindow\\\",\\\"value\\\":133},{\\\"key\\\":\\\"workbench.profiles.actions.manageProfiles\\\",\\\"value\\\":134}]}\",\"commandPalette.mru.counter\":\"135\",\"workbench.view.extensions.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.extensions.popular\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.workspaceRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.otherRecommendations\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extensions.recommendedList\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.enabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.disabled\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.extensions.marketplace\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchInstalled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchEnabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchDisabled\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchOutdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchBuiltin\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinFeatureExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinThemeExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.builtinProgrammingLanguageExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchRecentlyUpdated\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.searchWorkspaceUnsupported\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.untrustedPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualUnsupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.virtualPartiallySupportedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.deprecatedExtensions\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.local.installed\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.extensions.remote.installed\\\",\\\"isHidden\\\":false}]\",\"workbench.view.remote.state.hidden\":\"[{\\\"id\\\":\\\"targetsWsl\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteTargets\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"~remote.helpPanel\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"targetsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"detailsContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"devVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"remoteHub.views.workspaceRepositories\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.alignment\":\"center\",\"workbench.view.extension.gitlens.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.repositories:gitlens\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory:gitlens\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory:gitlens\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.compare:gitlens\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.search:gitlens\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.updates\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.welcome\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.home\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.workspaces\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.account\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.drafts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.launchpad\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensInspect.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.commitDetails\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.pullRequest\\\",\\\"isHidden\\\":false}]\",\"workbench.scm.views.state.hidden\":\"[{\\\"id\\\":\\\"workbench.scm.repositories\\\",\\\"isHidden\\\":false,\\\"order\\\":0},{\\\"id\\\":\\\"workbench.scm\\\",\\\"isHidden\\\":false,\\\"order\\\":1},{\\\"id\\\":\\\"gitlens.views.repositories:scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory:scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory:scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.compare:scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.search:scm\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.repositories\\\",\\\"isHidden\\\":false,\\\"order\\\":4},{\\\"id\\\":\\\"gitlens.views.commits\\\",\\\"isHidden\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"gitlens.views.fileHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"gitlens.views.branches\\\",\\\"isHidden\\\":false,\\\"order\\\":3},{\\\"id\\\":\\\"gitlens.views.remotes\\\",\\\"isHidden\\\":true,\\\"order\\\":5},{\\\"id\\\":\\\"gitlens.views.stashes\\\",\\\"isHidden\\\":false,\\\"order\\\":6},{\\\"id\\\":\\\"gitlens.views.tags\\\",\\\"isHidden\\\":true,\\\"order\\\":7},{\\\"id\\\":\\\"gitlens.views.contributors\\\",\\\"isHidden\\\":true,\\\"order\\\":9},{\\\"id\\\":\\\"gitlens.views.searchAndCompare\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"gitlens.views.worktrees\\\",\\\"isHidden\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.scm.sync\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitstash.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.scm.history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.scm.grouped\\\",\\\"isHidden\\\":false}]\",\"remote.explorerType\":\"tunnel,ssh-remote\",\"workbench.welcomePage.walkthroughMetadata\":\"[[\\\"ms-vscode-remote.remote-wsl#wslWalkthrough\\\",{\\\"firstSeen\\\":1739138712031,\\\"stepIDs\\\":[\\\"explore.commands\\\",\\\"open.wslwindow\\\",\\\"create.project\\\",\\\"open.project\\\",\\\"linux.environment\\\",\\\"install.tools\\\",\\\"run.debug\\\",\\\"come.back\\\"],\\\"manaullyOpened\\\":false}],[\\\"GitHub.copilot-chat#copilotWelcome\\\",{\\\"firstSeen\\\":1739138790609,\\\"stepIDs\\\":[\\\"copilot.setup.signIn\\\",\\\"copilot.setup.signInNoAction\\\",\\\"copilot.setup.signUp\\\",\\\"copilot.setup.signUpNoAction\\\",\\\"copilot.panelChat\\\",\\\"copilot.edits\\\",\\\"copilot.firstSuggest\\\",\\\"copilot.inlineChatNotMac\\\",\\\"copilot.inlineChatMac\\\",\\\"copilot.sparkle\\\"],\\\"manaullyOpened\\\":false}],[\\\"alefragnani.project-manager#projectManagerWelcome\\\",{\\\"firstSeen\\\":1739138883335,\\\"stepIDs\\\":[\\\"saveYourFavoriteProjects\\\",\\\"autoDetectGitRepositories\\\",\\\"findAndOpenProjects\\\",\\\"organizeWithTags\\\",\\\"exclusiveSideBar\\\",\\\"workingWithRemotes\\\"],\\\"manaullyOpened\\\":false}],[\\\"eamodio.gitlens#welcome\\\",{\\\"firstSeen\\\":1739138883335,\\\"stepIDs\\\":[\\\"get-started-community\\\",\\\"welcome-in-trial\\\",\\\"welcome-in-trial-expired\\\",\\\"welcome-in-trial-expired-eligible\\\",\\\"welcome-paid\\\",\\\"visualize-code-history\\\",\\\"accelerate-pr-reviews\\\",\\\"streamline-collaboration\\\",\\\"improve-workflows-with-integrations\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-docker#dockerStart\\\",{\\\"firstSeen\\\":1739138883335,\\\"stepIDs\\\":[\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"dockerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonWelcome\\\",{\\\"firstSeen\\\":1739138883335,\\\"stepIDs\\\":[\\\"python.createPythonFolder\\\",\\\"python.createPythonFile\\\",\\\"python.installPythonWin8\\\",\\\"python.installPythonMac\\\",\\\"python.installPythonLinux\\\",\\\"python.createEnvironment\\\",\\\"python.runAndDebug\\\",\\\"python.learnMoreWithDS\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-python.python#pythonDataScienceWelcome\\\",{\\\"firstSeen\\\":1739138883335,\\\"stepIDs\\\":[\\\"python.installJupyterExt\\\",\\\"python.createNewNotebook\\\",\\\"python.openInteractiveWindow\\\",\\\"python.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-toolsai.jupyter#jupyterWelcome\\\",{\\\"firstSeen\\\":1739138883335,\\\"stepIDs\\\":[\\\"ipynb.newUntitledIpynb\\\",\\\"jupyter.selectKernel\\\",\\\"jupyter.exploreAndDebug\\\",\\\"jupyter.dataScienceLearnMore\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-vscode.remote-repositories#remoteRepositoriesWalkthrough\\\",{\\\"firstSeen\\\":1739138883335,\\\"stepIDs\\\":[\\\"editCommitRepo\\\",\\\"createGitHubPullRequest\\\",\\\"continueOn\\\",\\\"openRepo\\\",\\\"remoteIndicator\\\"],\\\"manaullyOpened\\\":false}],[\\\"ms-azuretools.vscode-containers#containersStart\\\",{\\\"firstSeen\\\":1748729241874,\\\"stepIDs\\\":[\\\"chooseContainerRuntime\\\",\\\"openFolder\\\",\\\"openFolderMac\\\",\\\"scaffold\\\",\\\"buildImage\\\",\\\"runContainer\\\",\\\"containerExplorer\\\",\\\"pushImage\\\",\\\"azDeploy\\\",\\\"learn\\\"],\\\"manaullyOpened\\\":false}],[\\\"rust-lang.rust-analyzer#landing\\\",{\\\"firstSeen\\\":1748729241874,\\\"stepIDs\\\":[\\\"setup\\\",\\\"docs\\\",\\\"faq\\\",\\\"changelog\\\"],\\\"manaullyOpened\\\":false}]]\",\"~remote.forwardedPortsContainer.hidden\":\"[{\\\"id\\\":\\\"~remote.forwardedPorts\\\",\\\"isHidden\\\":false}]\",\"memento/gettingStartedService\":\"{\\\"installGit\\\":{\\\"done\\\":true},\\\"CopilotSetupSignedOut\\\":{\\\"done\\\":true},\\\"CopilotSetupSignedIn\\\":{\\\"done\\\":true},\\\"GitHub.copilot-chat#copilotWelcome#copilot.setup.signInNoAction\\\":{\\\"done\\\":true},\\\"GitHub.copilot-chat#copilotWelcome#copilot.firstSuggest\\\":{\\\"done\\\":true},\\\"pickColorTheme\\\":{\\\"done\\\":true},\\\"pickColorThemeWeb\\\":{\\\"done\\\":true},\\\"settingsAndSync\\\":{\\\"done\\\":true},\\\"settingsSyncWeb\\\":{\\\"done\\\":true},\\\"commandPaletteTask\\\":{\\\"done\\\":true},\\\"commandPaletteTaskWeb\\\":{\\\"done\\\":true},\\\"commandPaletteTaskAccessibility\\\":{\\\"done\\\":true}}\",\"fileBasedRecommendations/promptedRecommendations\":\"{\\\"dockercompose\\\":[\\\"ms-azuretools.vscode-docker\\\"],\\\"cpp\\\":[\\\"ms-vscode.cpptools-extension-pack\\\"],\\\"python\\\":[\\\"ms-python.python\\\"],\\\"plaintext\\\":[\\\"mechatroner.rainbow-csv\\\",\\\"ms-vscode.cmake-tools\\\"],\\\"makefile\\\":[\\\"ms-vscode.makefile-tools\\\"]}\",\"workbench.view.extension.dockerView.state.hidden\":\"[{\\\"id\\\":\\\"dockerContainers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerImages\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerRegistries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerNetworks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerVolumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-docker.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.explorer.views.state.hidden\":\"[{\\\"id\\\":\\\"outline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"timeline\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.openEditorsView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.emptyView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.explorer.fileView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"npm\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"commitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"compareCommitViewProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.repositories:explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.fileHistory:explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.lineHistory:explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.compare:explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.search:explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"materialDesignIconsExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"txtsyntaxHighlightExplore\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"filters\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cell-tag\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dartDependencyTree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"platformio-ide.projectTasks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"platformio-ide.quickAccess\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"continue.continueGUIView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"codeium.chatPanelView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"codeium.searchPanelView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"recommendation-panel-webview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.codeWhisperer.referenceLog\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.transformationHub\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.AmazonCommonAuth\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.transformationProposedChangesTree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.AmazonQChatView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.AmazonQNeverShowBadge\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.session.explorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"treeLocalHistoryExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"clipboardHistory\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"austin-vscode.callStacks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"austin-vscode.top\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"austin-vscode.flame-graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cody.chat\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"doublebot.sidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"claude-dev.SidebarProvider\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.cweijan.mysql\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.dbclient.history\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github.cweijan.nosql\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"supermaven-view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"augment-next-edit\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"rustDependencies\\\",\\\"isHidden\\\":false}]\",\"terminal.integrated.showTerminalConfigPrompt\":\"false\",\"nps/lastSessionDate\":\"Wed Jul 05 2023\",\"nps/sessionCount\":\"9\",\"cpp.1.lastSessionDate\":\"Sun Jul 13 2025\",\"cpp.1.sessionCount\":\"471\",\"java.2.lastSessionDate\":\"Sun Jul 13 2025\",\"java.2.sessionCount\":\"471\",\"javascript.1.lastSessionDate\":\"Sun Aug 18 2024\",\"javascript.1.sessionCount\":\"322\",\"typescript.1.lastSessionDate\":\"Sat May 25 2024\",\"typescript.1.sessionCount\":\"246\",\"csharp.1.lastSessionDate\":\"Sun Jul 13 2025\",\"csharp.1.sessionCount\":\"467\",\"tabs-list-width-horizontal\":\"256\",\"fileBasedRecommendations/promptedFileExtensions\":\"[\\\"cnf\\\",\\\"sample\\\",\\\"env\\\",\\\"crt\\\",\\\"key\\\"]\",\"remote.tunnels.toRestore.ssh-remote+eros.-43048982\":\"[]\",\"nps/isCandidate\":\"false\",\"nps/skipVersion\":\"1.79.2\",\"workbench.view.extension.sqltoolsActivityBarContainer.state.hidden\":\"[{\\\"id\\\":\\\"sqltoolsViewConnectionExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sqltoolsViewBookmarksExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"sqltoolsViewHistoryExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sqltoolsPanelContainer.state.hidden\":\"[{\\\"id\\\":\\\"sqltoolsViewConsoleMessages\\\",\\\"isHidden\\\":false}]\",\"sqltools-driver-credentials-'homeassistant' on 'homeassistant'\":\"[{\\\"id\\\":\\\"mtxr.sqltools-driver-pg\\\",\\\"name\\\":\\\"SQLTools PostgreSQL/Cockroach Driver\\\",\\\"allowed\\\":true}]\",\"sqltools-driver-credentials-'postgres' on 'homeassistant'\":\"[{\\\"id\\\":\\\"mtxr.sqltools-driver-pg\\\",\\\"name\\\":\\\"SQLTools PostgreSQL/Cockroach Driver\\\",\\\"allowed\\\":true}]\",\"sqltools-driver-credentials-'postgres' on 'Paperless'\":\"[{\\\"id\\\":\\\"mtxr.sqltools-driver-pg\\\",\\\"name\\\":\\\"SQLTools PostgreSQL/Cockroach Driver\\\",\\\"allowed\\\":true}]\",\"sqltools-driver-credentials-'postgres' on 'Home Assistant'\":\"[{\\\"id\\\":\\\"mtxr.sqltools-driver-pg\\\",\\\"name\\\":\\\"SQLTools PostgreSQL/Cockroach Driver\\\",\\\"allowed\\\":true}]\",\"workbench.view.extension.platformio.state.hidden\":\"[{\\\"id\\\":\\\"platformio-ide.projectTasks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"platformio-ide.quickAccess\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.gitlensPanel.state.hidden\":\"[{\\\"id\\\":\\\"gitlens.views.graph\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"gitlens.views.graphDetails\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.test.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.testing\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.view.testCoverage\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"test-explorer\\\",\\\"isHidden\\\":false}]\",\"cpp.1.editedCount\":\"2\",\"cpp.1.editedDate\":\"Mon Feb 26 2024\",\"expandSuggestionDocs\":\"true\",\"extensionTips/promptedExecutableTips\":\"{\\\"docker\\\":[\\\"ms-vscode-remote.remote-containers\\\"]}\",\"sqltools-driver-credentials-'telegraf' on 'Telegraf'\":\"[{\\\"id\\\":\\\"mtxr.sqltools-driver-pg\\\",\\\"name\\\":\\\"SQLTools PostgreSQL/Cockroach Driver\\\",\\\"allowed\\\":true}]\",\"workbench.view.extension.github-cweijan-mysql.state.hidden\":\"[{\\\"id\\\":\\\"github.cweijan.mysql\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-cweijan-nosql.state.hidden\":\"[{\\\"id\\\":\\\"github.cweijan.nosql\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.clipboard-manager.state.hidden\":\"[{\\\"id\\\":\\\"clipboardHistory\\\",\\\"isHidden\\\":false}]\",\"memento/workbench.editor.keybindings\":\"{\\\"searchHistory\\\":[\\\"new file\\\",\\\"\\\\\\\"ctrl+h\\\\\\\"\\\",\\\"compare active\\\",\\\"re-r\\\",\\\"ret\\\",\\\"re-\\\",\\\"re-run\\\",\\\"\\\\\\\"ctrl+o\\\\\\\"\\\",\\\"back\\\",\\\"alt\\\",\\\"alt+arr\\\",\\\"alt+arro\\\",\\\"alt+arrow\\\",\\\"retry\\\",\\\"rer\\\",\\\"last\\\",\\\"task\\\",\\\",\\\",\\\"mid\\\",\\\"middle\\\",\\\"pull\\\",\\\"\\\\\\\"ctrl+alt+u\\\\\\\"\\\",\\\"git pull\\\",\\\"git fetch\\\",\\\"\\\\\\\"ctrl+alt+i\\\\\\\"\\\",\\\"\\\\\\\"ctrl+j\\\\\\\"\\\",\\\"\\\\\\\"ctrl+u\\\\\\\"\\\",\\\"inline.va\\\",\\\"inline v\\\",\\\"inline var\\\",\\\"inline va\\\",\\\"reru\\\",\\\"open fi\\\",\\\"open file\\\",\\\"\\\\\\\"f4\\\\\\\"\\\",\\\"diff open \\\",\\\"diff open fil\\\",\\\"diff open file\\\",\\\"diff\\\",\\\"git open \\\",\\\"git open changes\\\",\\\"ctrl+shift+k\\\",\\\"git push\\\",\\\"@command:git.commit.compare.file.compare\\\",\\\"merge.\\\",\\\"merge\\\",\\\"goto cl\\\",\\\"goto cla\\\",\\\"got\\\",\\\"class\\\",\\\"ctrl\\\",\\\"ctrl+n\\\",\\\"go to\\\",\\\"go to cl\\\",\\\"go to c\\\",\\\"go to \\\",\\\"automatically de\\\",\\\"continue deb\\\",\\\"\\\\\\\"ctrl+q\\\\\\\"\\\",\\\"continue \\\",\\\"\\\\\\\"alt+r\\\\\\\"\\\",\\\"continue debug\\\",\\\"move l\\\",\\\"move le\\\",\\\"move left\\\",\\\"move para\\\",\\\"move ar\\\",\\\"move arg\\\",\\\"move par\\\",\\\"move \\\",\\\"move\\\",\\\"ctrl+i\\\",\\\"codeium: su\\\",\\\"codeium.o\\\",\\\"codeium.openCo\\\",\\\"codeium.openCod\\\",\\\"ctrl+shif\\\",\\\"ctrl+shift+enter\\\",\\\"ctrl+rig\\\",\\\"ctrl+ri\\\",\\\"ctrl+arrow\\\",\\\"brow\\\",\\\"aws.amazonq.rejectCodeSuggestion\\\",\\\"clear te\\\",\\\"clear term\\\",\\\"\\\\\\\"ctrl+l\\\\\\\"\\\",\\\"t\\\",\\\"terminal clea\\\",\\\"find file re\\\",\\\"find file ref\\\",\\\"q iniline\\\",\\\"amazonq\\\",\\\"amazon q\\\",\\\"re\\\",\\\"rerun \\\",\\\"rerun\\\",\\\"rerun test\\\",\\\"ctrl+d\\\",\\\"f4\\\",\\\"Gemini Code Assist\\\"]}\",\"extensionsAssistant/fileExtensionsSuggestionIgnore\":\"[\\\"1\\\"]\",\"workbench.view.extension.copilot-labs.state.hidden\":\"[{\\\"id\\\":\\\"copilotSidebar.explain\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"copilotSidebar.translate\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"copilotSidebar.toolbox\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"copilotSidebar.generateTests\\\",\\\"isHidden\\\":false}]\",\"github-ykrasik\":\"[{\\\"id\\\":\\\"github.copilot-labs\\\",\\\"name\\\":\\\"GitHub Copilot Labs\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.vscode-github-actions\\\",\\\"name\\\":\\\"GitHub Actions\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.vscode-pull-request-github\\\",\\\"name\\\":\\\"GitHub Pull Requests and Issues\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"vscode.configuration-editing\\\",\\\"name\\\":\\\"Configuration Editing\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"github.copilot-chat\\\",\\\"name\\\":\\\"GitHub Copilot Chat\\\",\\\"allowed\\\":true},{\\\"id\\\":\\\"dbcode.dbcode\\\",\\\"name\\\":\\\"DBCode\\\",\\\"allowed\\\":true}]\",\"views.customizations\":\"{\\\"viewContainerLocations\\\":{\\\"workbench.panel.comments\\\":0,\\\"workbench.view.extension.sqltoolsPanelContainer\\\":2,\\\"workbench.view.extension.continue\\\":2,\\\"workbench.views.service.panel.3b6d4b07-25d4-4235-a25e-2befe3bca161\\\":1,\\\"workbench.view.extension.codeium\\\":2,\\\"workbench.view.extension.cody\\\":2,\\\"workbench.view.extension.claude-dev-ActivityBar\\\":2,\\\"workbench.view.extension.amazonq\\\":2,\\\"workbench.view.extension.aider-composer-activitybar\\\":2,\\\"workbench.view.extension.supermaven-chat\\\":2,\\\"workbench.view.extension.1-geminiAIChatViewContainer\\\":2,\\\"workbench.view.extension.roo-cline-ActivityBar\\\":2,\\\"workbench.view.extension.augment-chat\\\":2,\\\"workbench.view.extension.geminiChat\\\":2},\\\"viewLocations\\\":{\\\"workbench.debug.startView\\\":\\\"workbench.view.debug\\\",\\\"extensions.listView\\\":\\\"workbench.view.extensions\\\",\\\"extensions.enabledExtensionList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.disabledExtensionList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.popularExtensionsList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.enabledExtensionList2\\\":\\\"workbench.view.extensions\\\",\\\"extensions.disabledExtensionList2\\\":\\\"workbench.view.extensions\\\",\\\"extensions.builtInExtensionsList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.builtInBasicsExtensionsList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.builtInThemesExtensionsList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.otherrecommendedList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.workspaceRecommendedList\\\":\\\"workbench.view.extensions\\\",\\\"extensions.vscode-local.installed\\\":\\\"workbench.view.extensions\\\",\\\"extensions.vscode-local.outdated\\\":\\\"workbench.view.extensions\\\",\\\"extensions.vscode-local.default\\\":\\\"workbench.view.extensions\\\",\\\"workbench.panel.repl.view\\\":\\\"workbench.views.service.panel.3b6d4b07-25d4-4235-a25e-2befe3bca161\\\"},\\\"viewContainerBadgeEnablementStates\\\":{}}\",\"workbench.panel.markers.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.markers.view\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.output.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.output\\\",\\\"isHidden\\\":false}]\",\"terminal.hidden\":\"[{\\\"id\\\":\\\"terminal\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.search.state.hidden\":\"[{\\\"id\\\":\\\"workbench.view.search\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.repl.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false,\\\"order\\\":2},{\\\"id\\\":\\\"workbench.debug.variablesView\\\",\\\"isHidden\\\":false,\\\"order\\\":1}]\",\"ces/skipSurvey\":\"1.82.0\",\"workbench.welcomePage.hiddenCategories\":\"[\\\"github.copilot\\\"]\",\"workbench.view.sync.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.sync.conflicts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.remoteActivity\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.machines\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"workbench.views.sync.localActivity\\\",\\\"isHidden\\\":true},{\\\"id\\\":\\\"workbench.views.sync.troubleshoot\\\",\\\"isHidden\\\":true}]\",\"remoteTunnelServicePromptedPreview\":\"true\",\"remoteTunnelServiceUsed\":\"{\\\"hostName\\\":\\\"orion\\\",\\\"timeStamp\\\":1694465227766}\",\"workbench.panel.chatSidebar.copilot.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dockerComposeView.state.hidden\":\"[{\\\"id\\\":\\\"dockerComposeProjects\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dockerComposeServices\\\",\\\"isHidden\\\":false}]\",\"settingsEditor2.splitViewWidth\":\"287\",\"workbench.panel.chatSidebar.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"workbench.activityBar.location\":\"default\",\"snippets.usageTimestamps\":\"[[\\\"syntaxes/snippets.json/CaseTemplate\\\",1703629806877]]\",\"workbench.view.extension.github-pull-requests.state.hidden\":\"[{\\\"id\\\":\\\"github:login\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pr:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"issues:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:conflictResolution\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"notifications:github\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-actions.state.hidden\":\"[{\\\"id\\\":\\\"github-actions.current-branch\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github-actions.workflows\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github-actions.settings\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github-actions.empty-view\\\",\\\"isHidden\\\":false}]\",\"inline-chat-history\":\"[\\\"Scroll to the bottom of chatArea\\\",\\\"make the received messages greyish\\\",\\\"Display some sort of loading indication on the chatArea\\\",\\\"prepend \\\\\\\"Hi! What is your zip code? I would like to make sure we cover your area.\\\\\\\"\\\",\\\"Make it so that while messageInput is focused, enter acts on sendButton\\\",\\\"add \\\\\\\"system_message\\\\\\\" as an empty string to request.body before validating\\\",\\\"decode base64 from auth before splitting\\\",\\\"generate a random uuid\\\",\\\"make this super call AppConfig's super\\\",\\\"do not write 0, omit 0s\\\",\\\"extract this into a generic function that can write any duration as human readable in this format\\\",\\\"write this in human readable, broken down to minutes, seconds and millis\\\",\\\"measure the time it takes this function to run and log it as debug\\\",\\\"add a docstring explaining that only these stage ids can be customized by users\\\",\\\"/fix\\\",\\\"How to make this button submit as json?\\\",\\\"make only this row in caps\\\",\\\"make this sentence in caps\\\",\\\"implement a deep merge between 2 dicts representing a json, that can contain nested dicts or lists\\\",\\\"Can | in python merge dicts?\\\",\\\"What's a good thing to tell a customer that is spamming or trying to manipulate the prompts? I want a \\\\\\\"thank you and goodbye\\\\\\\" sort of message\\\",\\\"how to make this not create an empty line in jinja2?\\\",\\\"does this check for truthiness?\\\",\\\"is it possible to run some python logic on services.services when rendering them? I want to render this list if services.services is present: [service.services.to_dict(by_alias=True) for service in services.services]\\\",\\\"is it possible to run some python logic on services.services when rendering them? I want to run this: [service.services.to_dict(by_alias=True) for service in services.services]\\\",\\\"Make this block optional in jinja2 syntax - if the template variable isn't defined, omit the block\\\",\\\"/fix Operator \\\\\\\"+\\\\\\\" not supported for types \\\\\\\"dict[str, Any]\\\\\\\" and \\\\\\\"dict[str, str]\\\\\\\"\\\",\\\"turn this into a single triple quoted string\\\",\\\"whats the correct status for unauthenticated?\\\",\\\"Change this prompt so that it returns the time zone as well, but only if it can be detected in the conversation\\\",\\\"match with this regex: \\\\\\\"Calendar with id .* not found\\\\\\\"\\\",\\\"make this check that the spike is not in 2h\\\",\\\"make this if not spike in 2h\\\",\\\"/fix Undefined name `async_to_sync`, \\\\\\\"async_to_sync\\\\\\\" is not defined\\\",\\\"/fix Undefined name `boto_ses_manager`, \\\\\\\"boto_ses_manager\\\\\\\" is not defined, Argument type is unknown\\\\n  Argument corresponds to parameter \\\\\\\"boto_ses_manager\\\\\\\" in function \\\\\\\"__init__\\\\\\\"\\\",\\\"/fix Expression of type \\\\\\\"bytes\\\\\\\" cannot be assigned to declared type \\\\\\\"str\\\\\\\"\\\\n  \\\\\\\"bytes\\\\\\\" is incompatible with \\\\\\\"str\\\\\\\"\\\",\\\"make this a lazy cached property\\\",\\\"will this work if type is a TypedDict?\\\",\\\"/fix \\\\\\\"_PageIterator[ListSecretsResponseTypeDef]\\\\\\\" is not iterable\\\\n  \\\\\\\"__aiter__\\\\\\\" method not defined\\\",\\\"Fetch all secrets from secret manager. Use ListSecrets with pagination, and fetch each individual secret in parallel. Use async for this\\\",\\\"Make this method generic on a type TPydantic\\\",\\\"make this generic that extends BaseModel\\\",\\\"/fix Within an `except` clause, raise exceptions with `raise ... from err` or `raise ... from None` to distinguish them from errors in exception handling\\\",\\\"/fix \\\\\\\"data\\\\\\\" overrides symbol of same name in class \\\\\\\"BaseSqsMessage\\\\\\\"\\\\n  Variable is mutable so its type is invariant\\\\n    Override type \\\\\\\"GhlMessageWebhook\\\\\\\" is not the same as base type \\\\\\\"BaseSqsMessageData\\\\\\\"\\\",\\\"Ignore timezones for dates\\\",\\\"Can this be done while only creating the list of slots once?\\\",\\\"Change this so it only puts date:DateSlots pair if the resulting slots=[slot for slot in date_slots.slots if slot.start not in excluded_time_slots] are not empty\\\",\\\"is hh:mm a 24 hour format?\\\",\\\"How to select the earliest date from this map?\\\",\\\"make the default country for phone number US\\\",\\\"/fix Type of \\\\\\\"client\\\\\\\" is partially unknown\\\\n  Type of \\\\\\\"client\\\\\\\" is \\\\\\\"Overload[(service_name: Literal['accessanalyzer'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['account'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['acm'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['acm-pca'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['alexaforbusiness'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['amp'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['amplify'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['amplifybackend'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['amplifyuibuilder'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['apigateway'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['apigatewaymanagementapi'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['apigatewayv2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appconfig'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appconfigdata'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appfabric'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appflow'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appintegrations'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['application-autoscaling'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['application-insights'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['applicationcostprofiler'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appmesh'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['apprunner'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appstream'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['appsync'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['arc-zonal-shift'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['athena'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['auditmanager'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['autoscaling'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['autoscaling-plans'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['b2bi'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['backup'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['backup-gateway'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['backupstorage'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['batch'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['bcm-data-exports'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['bedrock'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['bedrock-agent'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['bedrock-agent-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['bedrock-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['billingconductor'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['braket'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['budgets'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ce'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['chime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['chime-sdk-identity'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['chime-sdk-media-pipelines'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['chime-sdk-meetings'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['chime-sdk-messaging'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['chime-sdk-voice'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cleanrooms'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cleanroomsml'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloud9'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudcontrol'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['clouddirectory'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudformation'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> CloudFormationClient, (service_name: Literal['cloudfront'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudfront-keyvaluestore'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudhsm'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudhsmv2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudsearch'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudsearchdomain'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudtrail'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudtrail-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cloudwatch'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codeartifact'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codebuild'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codecatalyst'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codecommit'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codedeploy'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codeguru-reviewer'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codeguru-security'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codeguruprofiler'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codepipeline'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codestar'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codestar-connections'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['codestar-notifications'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cognito-identity'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cognito-idp'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cognito-sync'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['comprehend'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['comprehendmedical'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['compute-optimizer'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['config'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['connect'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['connect-contact-lens'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['connectcampaigns'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['connectcases'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['connectparticipant'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['controltower'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cost-optimization-hub'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['cur'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['customer-profiles'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['databrew'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['dataexchange'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['datapipeline'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['datasync'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['datazone'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['dax'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['detective'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['devicefarm'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['devops-guru'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['directconnect'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['discovery'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['dlm'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['dms'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['docdb'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['docdb-elastic'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['drs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ds'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['dynamodb'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> DynamoDBClient, (service_name: Literal['dynamodbstreams'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ebs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ec2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> EC2Client, (service_name: Literal['ec2-instance-connect'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ecr'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ecr-public'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ecs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['efs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['eks'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['eks-auth'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['elastic-inference'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['elasticache'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['elasticbeanstalk'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['elastictranscoder'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['elb'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['elbv2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['emr'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['emr-containers'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['emr-serverless'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['entityresolution'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['es'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['events'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['evidently'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['finspace'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['finspace-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['firehose'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['fis'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['fms'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['forecast'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['forecastquery'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['frauddetector'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['freetier'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['fsx'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['gamelift'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['glacier'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['globalaccelerator'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['glue'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['grafana'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['greengrass'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['greengrassv2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['groundstation'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['guardduty'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['health'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['healthlake'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['honeycode'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iam'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['identitystore'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['imagebuilder'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['importexport'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['inspector'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['inspector-scan'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['inspector2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['internetmonitor'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iot'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iot-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iot-jobs-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iot-roborunner'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iot1click-devices'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iot1click-projects'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotanalytics'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotdeviceadvisor'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotevents'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotevents-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotfleethub'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotfleetwise'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotsecuretunneling'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotsitewise'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotthingsgraph'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iottwinmaker'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['iotwireless'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ivs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ivs-realtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ivschat'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kafka'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kafkaconnect'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kendra'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kendra-ranking'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['keyspaces'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesis'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesis-video-archived-media'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesis-video-media'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesis-video-signaling'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesis-video-webrtc-storage'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesisanalytics'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesisanalyticsv2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kinesisvideo'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['kms'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lakeformation'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lambda'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> LambdaClient, (service_name: Literal['launch-wizard'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lex-models'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lex-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lexv2-models'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lexv2-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['license-manager'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['license-manager-linux-subscriptions'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['license-manager-user-subscriptions'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lightsail'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['location'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['logs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lookoutequipment'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lookoutmetrics'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['lookoutvision'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['m2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['machinelearning'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['macie2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['managedblockchain'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['managedblockchain-query'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['marketplace-agreement'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['marketplace-catalog'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['marketplace-deployment'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['marketplace-entitlement'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['marketplacecommerceanalytics'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediaconnect'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediaconvert'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['medialive'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediapackage'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediapackage-vod'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediapackagev2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediastore'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediastore-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mediatailor'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['medical-imaging'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['memorydb'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['meteringmarketplace'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mgh'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mgn'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['migration-hub-refactor-spaces'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['migrationhub-config'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['migrationhuborchestrator'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['migrationhubstrategy'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mobile'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mq'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mturk'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['mwaa'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['neptune'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['neptune-graph'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['neptunedata'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['network-firewall'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['networkmanager'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['networkmonitor'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['nimble'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['oam'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['omics'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['opensearch'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['opensearchserverless'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['opsworks'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['opsworkscm'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['organizations'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['osis'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['outposts'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['panorama'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['payment-cryptography'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['payment-cryptography-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pca-connector-ad'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['personalize'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['personalize-events'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['personalize-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pi'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pinpoint'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pinpoint-email'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pinpoint-sms-voice'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pinpoint-sms-voice-v2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pipes'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['polly'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['pricing'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['privatenetworks'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['proton'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['qbusiness'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['qconnect'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['qldb'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['qldb-session'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['quicksight'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ram'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['rbin'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['rds'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> RDSClient, (service_name: Literal['rds-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['redshift'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['redshift-data'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['redshift-serverless'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['rekognition'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['repostspace'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['resiliencehub'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['resource-explorer-2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['resource-groups'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['resourcegroupstaggingapi'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['robomaker'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['rolesanywhere'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['route53'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['route53-recovery-cluster'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['route53-recovery-control-config'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['route53-recovery-readiness'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['route53domains'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['route53resolver'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['rum'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['s3'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> S3Client, (service_name: Literal['s3control'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['s3outposts'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sagemaker'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sagemaker-a2i-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sagemaker-edge'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sagemaker-featurestore-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sagemaker-geospatial'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sagemaker-metrics'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sagemaker-runtime'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['savingsplans'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['scheduler'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['schemas'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sdb'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['secretsmanager'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['securityhub'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['securitylake'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['serverlessrepo'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['service-quotas'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['servicecatalog'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['servicecatalog-appregistry'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['servicediscovery'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ses'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sesv2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['shield'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['signer'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['simspaceweaver'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sms'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sms-voice'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['snow-device-management'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['snowball'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sns'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sqs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> SQSClient, (service_name: Literal['ssm'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ssm-contacts'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ssm-incidents'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['ssm-sap'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sso'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sso-admin'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sso-oidc'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['stepfunctions'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['storagegateway'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['sts'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['supplychain'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['support'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['support-app'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['swf'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['synthetics'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['textract'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['timestream-query'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['timestream-write'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['tnb'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['transcribe'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['transfer'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['translate'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['trustedadvisor'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['verifiedpermissions'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['voice-id'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['vpc-lattice'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['waf'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['waf-regional'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['wafv2'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['wellarchitected'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['wisdom'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['workdocs'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['worklink'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['workmail'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['workmailmessageflow'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['workspaces'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['workspaces-thin-client'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['workspaces-web'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown, (service_name: Literal['xray'], region_name: str | None = ..., api_version: str | None = ..., use_ssl: bool | None = ..., verify: bool | str | None = ..., endpoint_url: str | None = ..., aws_access_key_id: str | None = ..., aws_secret_access_key: str | None = ..., aws_session_token: str | None = ..., config: Config | None = ...) -> Unknown]\\\\\\\"\\\",\\\"/fix Type of \\\\\\\"rep\\\\\\\" is unknown\\\",\\\"/fix Type of parameter \\\\\\\"item\\\\\\\" is unknown, Type annotation is missing for parameter \\\\\\\"item\\\\\\\"\\\",\\\"/fix Use a single `if` statement instead of nested `if` statements\\\",\\\"/fix Type of \\\\\\\"value\\\\\\\" is partially unknown\\\\n  Type of \\\\\\\"value\\\\\\\" is \\\\\\\"str | int | Unknown\\\\\\\"\\\",\\\"/fix Type of parameter \\\\\\\"kwargs\\\\\\\" is unknown, Type annotation is missing for parameter \\\\\\\"kwargs\\\\\\\"\\\",\\\"/fix Argument type is partially unknown\\\\n  Argument corresponds to parameter \\\\\\\"d\\\\\\\" in function \\\\\\\"omit_falsy_values\\\\\\\"\\\\n  Argument type is \\\\\\\"dict[Unknown, Unknown]\\\\\\\"\\\",\\\"/fix Argument of type \\\\\\\"type[GhlConversationsResponse]\\\\\\\" cannot be assigned to parameter \\\\\\\"t\\\\\\\" of type \\\\\\\"T@from_json_str\\\\\\\" in function \\\\\\\"from_json_str\\\\\\\"\\\\n  \\\\\\\"type[ABCMeta]\\\\\\\" is incompatible with \\\\\\\"type[GhlConversationsResponse]\\\\\\\"\\\",\\\"/fix Expression of type \\\\\\\"dict[str, Any] | list[Any]\\\\\\\" cannot be assigned to declared type \\\\\\\"dict[str, Any]\\\\\\\"\\\\n  Type \\\\\\\"dict[str, Any] | list[Any]\\\\\\\" cannot be assigned to type \\\\\\\"dict[str, Any]\\\\\\\"\\\\n    \\\\\\\"list[Any]\\\\\\\" is incompatible with \\\\\\\"dict[str, Any]\\\\\\\"\\\",\\\"/fix Incorrect type. Expected \\\\\\\"include\\\\\\\".\\\",\\\"/fix Line too long (228 > 140)\\\",\\\"/fix Colon expected\\\",\\\"/tests \\\",\\\"/fix \\\\\\\"_db_conversation\\\\\\\" is protected and used outside of the class in which it is declared\\\",\\\"/fix Cannot access member \\\\\\\"agreed\\\\\\\" for type \\\\\\\"type[BaseModel]\\\\\\\"\\\\n  Member \\\\\\\"agreed\\\\\\\" is unknown\\\",\\\"generate this test case\\\",\\\"Generate this testcase\\\"]\",\"workbench.panel.testResults.state.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.testResults.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.github-pull-request.state.hidden\":\"[{\\\"id\\\":\\\"github:createPullRequestWebview\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesFiles\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:compareChangesCommits\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"prStatus:github\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"github:activePullRequest:welcome\\\",\\\"isHidden\\\":false}]\",\"Comments.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.comments\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.package-explorer.state.hidden\":\"[{\\\"id\\\":\\\"workspaceEnvironments\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"pythonEnvironments\\\",\\\"isHidden\\\":false}]\",\"workbench.auxiliarybar.pinnedPanels\":\"[{\\\"id\\\":\\\"workbench.view.extension.sqltoolsPanelContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":8},{\\\"id\\\":\\\"workbench.panel.chatEditing\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":101},{\\\"id\\\":\\\"workbench.view.extension.roo-cline-ActivityBar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":17},{\\\"id\\\":\\\"workbench.view.extension.cody\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":23},{\\\"id\\\":\\\"workbench.view.extension.aider-composer-activitybar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.view.extension.1-geminiAIChatViewContainer\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":25},{\\\"id\\\":\\\"workbench.view.extension.claude-dev-ActivityBar\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":36},{\\\"id\\\":\\\"workbench.view.extension.codeium\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.supermaven-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":30},{\\\"id\\\":\\\"workbench.view.extension.amazonq\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":26},{\\\"id\\\":\\\"workbench.panel.chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":100},{\\\"id\\\":\\\"workbench.view.extension.augment-chat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":9},{\\\"id\\\":\\\"workbench.view.extension.geminiChat\\\",\\\"pinned\\\":true,\\\"visible\\\":false,\\\"order\\\":27}]\",\"userDataProfiles.state.hidden\":\"[{\\\"id\\\":\\\"workbench.views.profiles.export.preview\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.project-manager.state.hidden\":\"[{\\\"id\\\":\\\"projectsExplorerFavorites\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerGit\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerSVN\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerAny\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerMercurial\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectsExplorerVSCode\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"projectManagerHelpAndFeedback\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.githd-explorer.state.hidden\":\"[{\\\"id\\\":\\\"committedFiles\\\",\\\"isHidden\\\":false}]\",\"file.particpants.additionalEdits\":\"false\",\"javascript.1.editedCount\":\"10\",\"javascript.1.editedDate\":\"Sat Aug 17 2024\",\"workbench.view.extension.flutter.state.hidden\":\"[{\\\"id\\\":\\\"dartFlutterSidebar\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dartFlutterOutline\\\",\\\"isHidden\\\":false}]\",\"<EMAIL>\":\"[{\\\"id\\\":\\\"ms-vscode.remote-server\\\",\\\"name\\\":\\\"Remote - Tunnels\\\",\\\"allowed\\\":true}]\",\"workbench.view.extension.references-view.state.hidden\":\"[{\\\"id\\\":\\\"references-view.tree\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"CppReferencesView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.continue.state.hidden\":\"[{\\\"id\\\":\\\"continue.continueGUIView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.codeium.state.hidden\":\"[{\\\"id\\\":\\\"codeium.chatPanelView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"codeium.searchPanelView\\\",\\\"isHidden\\\":false}]\",\"codeium_auth-Yevgeny Krasik\":\"[{\\\"id\\\":\\\"codeium.codeium\\\",\\\"name\\\":\\\"Codeium: AI Coding Autocomplete and Chat for Python, Javascript, Typescript, Java, Go, and more\\\",\\\"allowed\\\":true}]\",\"workbench.view.extension.metabob-webview.state.hidden\":\"[{\\\"id\\\":\\\"recommendation-panel-webview\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.kubernetesView.state.hidden\":\"[{\\\"id\\\":\\\"extension.vsKubernetesExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"extension.vsKubernetesHelmRepoExplorer\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"kubernetes.cloudExplorer\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.amazonq.state.hidden\":\"[{\\\"id\\\":\\\"aws.amazonq.AmazonCommonAuth\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.AmazonQChatView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.AmazonQNeverShowBadge\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.notifications\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"aws.amazonq.SecurityIssuesTree\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.aws-codewhisperer-reference-log.state.hidden\":\"[{\\\"id\\\":\\\"aws.codeWhisperer.referenceLog\\\",\\\"isHidden\\\":false}]\",\"typescript.1.editedCount\":\"10\",\"typescript.1.editedDate\":\"Wed May 22 2024\",\"workbench.views.service.panel.3b6d4b07-25d4-4235-a25e-2befe3bca161.state.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.repl.view\\\",\\\"isHidden\\\":false}]\",\"typescript.1.isCandidate\":\"false\",\"typescript.1.skipVersion\":\"1.89.1\",\"workbench.view.extension.thunder-client.state.hidden\":\"[{\\\"id\\\":\\\"thunder-client-sidebar\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.makefile__viewContainer.state.hidden\":\"[{\\\"id\\\":\\\"makefile.outline\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.liveshare.state.hidden\":\"[{\\\"id\\\":\\\"liveshare.session\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.help\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"liveshare.devtools\\\",\\\"isHidden\\\":false}]\",\"javascript.1.isCandidate\":\"false\",\"javascript.1.skipVersion\":\"1.92.2\",\"workbench.view.extension.flame-graph-container.state.hidden\":\"[{\\\"id\\\":\\\"austin-vscode.flame-graph\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.austin-stats.state.hidden\":\"[{\\\"id\\\":\\\"austin-vscode.callStacks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"austin-vscode.top\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cody.state.hidden\":\"[{\\\"id\\\":\\\"cody.chat\\\",\\\"isHidden\\\":false}]\",\"http.linkProtectionTrustedDomains\":\"[\\\"https://sourcegraph.com\\\"]\",\"refactorPreview.hidden\":\"[{\\\"id\\\":\\\"refactorPreview\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.doublebot.state.hidden\":\"[{\\\"id\\\":\\\"doublebot.sidebar\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.claude-dev-ActivityBar.state.hidden\":\"[{\\\"id\\\":\\\"claude-dev.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.telemetryOptOutShown\":\"true\",\"workbench.view.extension.PearAI.state.hidden\":\"[{\\\"id\\\":\\\"pearai.pearAIChatView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.supermaven-chat.state.hidden\":\"[{\\\"id\\\":\\\"supermaven-view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cspell-info-explorer.state.hidden\":\"[{\\\"id\\\":\\\"cSpellInfoView\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cSpellRegExpView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.cspellPanel.state.hidden\":\"[{\\\"id\\\":\\\"cSpellIssuesViewByFile\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"cSpellIssuesViewByIssue\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dbcodeActivitybarContainer.state.hidden\":\"[{\\\"id\\\":\\\"dbcode.connections.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.tunnels.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.history.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.account.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.help.view\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"dbcode.favorites.view\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.dbcodePanelContainer.state.hidden\":\"[{\\\"id\\\":\\\"dbcode.panelView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.aider-composer-activitybar.state.hidden\":\"[{\\\"id\\\":\\\"aider-composer.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.1-geminiAIChatViewContainer.state.hidden\":\"[{\\\"id\\\":\\\"cloudcode.gemini.chatView\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.roo-cline-ActivityBar.state.hidden\":\"[{\\\"id\\\":\\\"roo-cline.SidebarProvider\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.chat.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.copilot\\\",\\\"isHidden\\\":false}]\",\"workbench.panel.chatEditing.hidden\":\"[{\\\"id\\\":\\\"workbench.panel.chat.view.edits\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.augment-chat.state.hidden\":\"[{\\\"id\\\":\\\"augment-chat\\\",\\\"isHidden\\\":false}]\",\"extensions.trustedPublishers\":\"{\\\"aaaaronzhou\\\":{\\\"publisher\\\":\\\"aaaaronzhou\\\",\\\"publisherDisplayName\\\":\\\"Aaaaron Zhou\\\"},\\\"adpyke\\\":{\\\"publisher\\\":\\\"adpyke\\\",\\\"publisherDisplayName\\\":\\\"adpyke\\\"},\\\"ahmadalli\\\":{\\\"publisher\\\":\\\"ahmadalli\\\",\\\"publisherDisplayName\\\":\\\"ahmadalli\\\"},\\\"alefragnani\\\":{\\\"publisher\\\":\\\"alefragnani\\\",\\\"publisherDisplayName\\\":\\\"Alessandro Fragnani\\\"},\\\"arturock\\\":{\\\"publisher\\\":\\\"arturock\\\",\\\"publisherDisplayName\\\":\\\"arturock\\\"},\\\"augment\\\":{\\\"publisher\\\":\\\"augment\\\",\\\"publisherDisplayName\\\":\\\"Augment Computing\\\"},\\\"charliermarsh\\\":{\\\"publisher\\\":\\\"charliermarsh\\\",\\\"publisherDisplayName\\\":\\\"Astral Software\\\"},\\\"christian-kohler\\\":{\\\"publisher\\\":\\\"christian-kohler\\\",\\\"publisherDisplayName\\\":\\\"Christian Kohler\\\"},\\\"codeium\\\":{\\\"publisher\\\":\\\"codeium\\\",\\\"publisherDisplayName\\\":\\\"Codeium\\\"},\\\"codezombiech\\\":{\\\"publisher\\\":\\\"codezombiech\\\",\\\"publisherDisplayName\\\":\\\"CodeZombie\\\"},\\\"cweijan\\\":{\\\"publisher\\\":\\\"cweijan\\\",\\\"publisherDisplayName\\\":\\\"Weijan Chen\\\"},\\\"dart-code\\\":{\\\"publisher\\\":\\\"dart-code\\\",\\\"publisherDisplayName\\\":\\\"Dart Code\\\"},\\\"dbcode\\\":{\\\"publisher\\\":\\\"dbcode\\\",\\\"publisherDisplayName\\\":\\\"DBCode\\\"},\\\"donjayamanne\\\":{\\\"publisher\\\":\\\"donjayamanne\\\",\\\"publisherDisplayName\\\":\\\"Don Jayamanne\\\"},\\\"eamodio\\\":{\\\"publisher\\\":\\\"eamodio\\\",\\\"publisherDisplayName\\\":\\\"GitKraken\\\"},\\\"emilast\\\":{\\\"publisher\\\":\\\"emilast\\\",\\\"publisherDisplayName\\\":\\\"Emil Åström\\\"},\\\"esbenp\\\":{\\\"publisher\\\":\\\"esbenp\\\",\\\"publisherDisplayName\\\":\\\"Prettier\\\"},\\\"esphome\\\":{\\\"publisher\\\":\\\"esphome\\\",\\\"publisherDisplayName\\\":\\\"ESPHome\\\"},\\\"flutterando\\\":{\\\"publisher\\\":\\\"flutterando\\\",\\\"publisherDisplayName\\\":\\\"Flutterando\\\"},\\\"formulahendry\\\":{\\\"publisher\\\":\\\"formulahendry\\\",\\\"publisherDisplayName\\\":\\\"Jun Han\\\"},\\\"foxundermoon\\\":{\\\"publisher\\\":\\\"foxundermoon\\\",\\\"publisherDisplayName\\\":\\\"foxundermoon\\\"},\\\"gitworktrees\\\":{\\\"publisher\\\":\\\"gitworktrees\\\",\\\"publisherDisplayName\\\":\\\"Git Worktrees\\\"},\\\"graphql\\\":{\\\"publisher\\\":\\\"graphql\\\",\\\"publisherDisplayName\\\":\\\"GraphQL Foundation\\\"},\\\"hbenl\\\":{\\\"publisher\\\":\\\"hbenl\\\",\\\"publisherDisplayName\\\":\\\"Holger Benl\\\"},\\\"hediet\\\":{\\\"publisher\\\":\\\"hediet\\\",\\\"publisherDisplayName\\\":\\\"Henning Dieterichs\\\"},\\\"k--kato\\\":{\\\"publisher\\\":\\\"k--kato\\\",\\\"publisherDisplayName\\\":\\\"Keisuke Kato\\\"},\\\"keesschollaart\\\":{\\\"publisher\\\":\\\"keesschollaart\\\",\\\"publisherDisplayName\\\":\\\"Kees Schollaart\\\"},\\\"kevinrose\\\":{\\\"publisher\\\":\\\"kevinrose\\\",\\\"publisherDisplayName\\\":\\\"Kevin Rose\\\"},\\\"lee2py\\\":{\\\"publisher\\\":\\\"lee2py\\\",\\\"publisherDisplayName\\\":\\\"Jimmie Lee\\\"},\\\"littlefoxteam\\\":{\\\"publisher\\\":\\\"littlefoxteam\\\",\\\"publisherDisplayName\\\":\\\"Little Fox Team\\\"},\\\"lukas-tr\\\":{\\\"publisher\\\":\\\"lukas-tr\\\",\\\"publisherDisplayName\\\":\\\"Lukas Troyer\\\"},\\\"mechatroner\\\":{\\\"publisher\\\":\\\"mechatroner\\\",\\\"publisherDisplayName\\\":\\\"mechatroner\\\"},\\\"mikestead\\\":{\\\"publisher\\\":\\\"mikestead\\\",\\\"publisherDisplayName\\\":\\\"mikestead\\\"},\\\"mindaro-dev\\\":{\\\"publisher\\\":\\\"mindaro-dev\\\",\\\"publisherDisplayName\\\":\\\"Microsoft DevLabs\\\"},\\\"nefrob\\\":{\\\"publisher\\\":\\\"nefrob\\\",\\\"publisherDisplayName\\\":\\\"Robert Neff\\\"},\\\"njpwerner\\\":{\\\"publisher\\\":\\\"njpwerner\\\",\\\"publisherDisplayName\\\":\\\"Nils Werner\\\"},\\\"oderwat\\\":{\\\"publisher\\\":\\\"oderwat\\\",\\\"publisherDisplayName\\\":\\\"oderwat\\\"},\\\"pmneo\\\":{\\\"publisher\\\":\\\"pmneo\\\",\\\"publisherDisplayName\\\":\\\"pmneo\\\"},\\\"rangav\\\":{\\\"publisher\\\":\\\"rangav\\\",\\\"publisherDisplayName\\\":\\\"Thunder Client\\\"},\\\"raynigon\\\":{\\\"publisher\\\":\\\"raynigon\\\",\\\"publisherDisplayName\\\":\\\"Simon Schneider\\\"},\\\"redhat\\\":{\\\"publisher\\\":\\\"redhat\\\",\\\"publisherDisplayName\\\":\\\"Red Hat\\\"},\\\"rockingskier\\\":{\\\"publisher\\\":\\\"rockingskier\\\",\\\"publisherDisplayName\\\":\\\"RockingSkier\\\"},\\\"rooveterinaryinc\\\":{\\\"publisher\\\":\\\"rooveterinaryinc\\\",\\\"publisherDisplayName\\\":\\\"Roo Code\\\"},\\\"rvest\\\":{\\\"publisher\\\":\\\"rvest\\\",\\\"publisherDisplayName\\\":\\\"Rebecca Vest\\\"},\\\"saoudrizwan\\\":{\\\"publisher\\\":\\\"saoudrizwan\\\",\\\"publisherDisplayName\\\":\\\"Cline\\\"},\\\"streetsidesoftware\\\":{\\\"publisher\\\":\\\"streetsidesoftware\\\",\\\"publisherDisplayName\\\":\\\"Street Side Software\\\"},\\\"stylelint\\\":{\\\"publisher\\\":\\\"stylelint\\\",\\\"publisherDisplayName\\\":\\\"Stylelint\\\"},\\\"tamasfe\\\":{\\\"publisher\\\":\\\"tamasfe\\\",\\\"publisherDisplayName\\\":\\\"tamasfe\\\"},\\\"teclado\\\":{\\\"publisher\\\":\\\"teclado\\\",\\\"publisherDisplayName\\\":\\\"teclado\\\"},\\\"usernamehw\\\":{\\\"publisher\\\":\\\"usernamehw\\\",\\\"publisherDisplayName\\\":\\\"Alexander\\\"},\\\"vscode-icons-team\\\":{\\\"publisher\\\":\\\"vscode-icons-team\\\",\\\"publisherDisplayName\\\":\\\"VSCode Icons Team\\\"},\\\"vue\\\":{\\\"publisher\\\":\\\"vue\\\",\\\"publisherDisplayName\\\":\\\"Vue\\\"},\\\"wholroyd\\\":{\\\"publisher\\\":\\\"wholroyd\\\",\\\"publisherDisplayName\\\":\\\"wholroyd\\\"},\\\"william-voyek\\\":{\\\"publisher\\\":\\\"william-voyek\\\",\\\"publisherDisplayName\\\":\\\"William Voyek\\\"},\\\"xshrim\\\":{\\\"publisher\\\":\\\"xshrim\\\",\\\"publisherDisplayName\\\":\\\"xshrim\\\"},\\\"yoavbls\\\":{\\\"publisher\\\":\\\"yoavbls\\\",\\\"publisherDisplayName\\\":\\\"yoavbls\\\"},\\\"zhuangtongfa\\\":{\\\"publisher\\\":\\\"zhuangtongfa\\\",\\\"publisherDisplayName\\\":\\\"binaryify\\\"},\\\"zokugun\\\":{\\\"publisher\\\":\\\"zokugun\\\",\\\"publisherDisplayName\\\":\\\"zokugun\\\"},\\\"leandro-rodrigues\\\":{\\\"publisher\\\":\\\"leandro-rodrigues\\\",\\\"publisherDisplayName\\\":\\\"Leandro Rodrigues\\\"},\\\"docker\\\":{\\\"publisher\\\":\\\"docker\\\",\\\"publisherDisplayName\\\":\\\"Docker\\\"},\\\"yanfeixin\\\":{\\\"publisher\\\":\\\"yanfeixin\\\",\\\"publisherDisplayName\\\":\\\"yanfeixin\\\"},\\\"subframe7536\\\":{\\\"publisher\\\":\\\"subframe7536\\\",\\\"publisherDisplayName\\\":\\\"subframe7536\\\"},\\\"luanpotter\\\":{\\\"publisher\\\":\\\"luanpotter\\\",\\\"publisherDisplayName\\\":\\\"Luan\\\"},\\\"bracketpaircolordlw\\\":{\\\"publisher\\\":\\\"BracketPairColorDLW\\\",\\\"publisherDisplayName\\\":\\\"Bracket Pair Color DLW\\\"},\\\"google\\\":{\\\"publisher\\\":\\\"Google\\\",\\\"publisherDisplayName\\\":\\\"Google\\\"},\\\"rust-lang\\\":{\\\"publisher\\\":\\\"rust-lang\\\",\\\"publisherDisplayName\\\":\\\"The Rust Programming Language \\\"},\\\"vadimcn\\\":{\\\"publisher\\\":\\\"vadimcn\\\",\\\"publisherDisplayName\\\":\\\"Vadim Chugunov\\\"}}\",\"chat.currentLanguageModel.editing-session\":\"github.copilot-chat/o3-mini\",\"extension.features.state\":\"{\\\"github.copilot-chat\\\":{\\\"copilot\\\":{\\\"disabled\\\":false,\\\"accessTimes\\\":[1750220177586,1750221511683]}}}\",\"workbench.view.extension.vscode-serial-monitor-tools.state.hidden\":\"[{\\\"id\\\":\\\"vscode-serial-monitor.monitor0\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-serial-monitor.monitor1\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-serial-monitor.monitor2\\\",\\\"isHidden\\\":false}]\",\"chat.currentLanguageModel.panel\":\"github.copilot-chat/o3-mini\",\"workbench.view.extension.augment-panel.state.hidden\":\"[{\\\"id\\\":\\\"augment-next-edit\\\",\\\"isHidden\\\":false}]\",\"scm.input.lastActionId\":\"github.copilot.git.generateCommitMessage\",\"workbench.view.extension.containersView.state.hidden\":\"[{\\\"id\\\":\\\"vscode-containers.views.containers\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.images\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.registries\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.networks\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.volumes\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.dockerContexts\\\",\\\"isHidden\\\":false},{\\\"id\\\":\\\"vscode-containers.views.help\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.sidebarDevToolsInspectorContainer.state.hidden\":\"[{\\\"id\\\":\\\"sidebarDevToolsInspector\\\",\\\"isHidden\\\":false}]\",\"remote.tunnels.toRestore.ssh-remote+deimos.-43048982\":\"[{\\\"remoteHost\\\":\\\"0.0.0.0\\\",\\\"remotePort\\\":9428,\\\"localPort\\\":9428,\\\"localAddress\\\":\\\"localhost:9428\\\",\\\"localUri\\\":{\\\"$mid\\\":1,\\\"path\\\":\\\"/\\\",\\\"scheme\\\":\\\"http\\\",\\\"authority\\\":\\\"localhost:9428\\\"},\\\"protocol\\\":\\\"http\\\",\\\"source\\\":{\\\"source\\\":1,\\\"description\\\":\\\"Auto Forwarded\\\"}}]\",\"remote.tunnels.toRestoreExpiration.ssh-remote+deimos.-43048982\":\"1753649780786\",\"workbench.view.extension.flutterPropertyEditor.state.hidden\":\"[{\\\"id\\\":\\\"flutterPropertyEditor\\\",\\\"isHidden\\\":false}]\",\"workbench.view.extension.geminiChat.state.hidden\":\"[{\\\"id\\\":\\\"cloudcode.gemini.chatView\\\",\\\"isHidden\\\":false}]\"}}"}