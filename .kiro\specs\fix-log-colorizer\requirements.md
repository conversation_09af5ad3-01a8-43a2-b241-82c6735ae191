# Requirements Document

## Introduction

The log colorizer implementation needs to be fixed to pass all existing tests. The current implementation has issues with debug tag naming, bracket level numbering, and content parsing that cause test failures.

## Requirements

### Requirement 1

**User Story:** As a developer, I want the log colorizer to produce correct debug output tags, so that the test assertions pass.

#### Acceptance Criteria

1. WHEN colorizing function calls THEN the system SHALL use `paren_0`, `paren_1`, etc. for parenthesis level tags
2. WHEN colorizing parameters THEN the system SHALL use `paramKey` for parameter keys and `paramVal` for parameter values
3. WHEN colorizing symbols THEN the system SHALL use `symbol` for equals signs, commas, and colons
4. WHEN colorizing brackets THEN the system SHALL use `bracket_0`, `bracket_1`, etc. for bracket level tags
5. WHEN colorizing braces THEN the system SHALL use `brace_0`, `brace_1`, etc. for brace level tags

### Requirement 2

**User Story:** As a developer, I want bracket and brace content to be parsed correctly, so that nested structures are properly colorized.

#### Acceptance Criteria

1. WHEN parsing bracket content like `[file1.txt, file2.txt]` THEN the system SHALL create separate paramVal nodes for each item
2. WHEN parsing brace content like `{recursive: true}` THEN the system SHALL parse the key-value pairs correctly
3. WHEN parsing comma-separated values THEN the system SHALL create individual paramVal nodes with symbol nodes for commas
4. WHEN parsing nested structures THEN the system SHALL increment bracket/brace levels appropriately

### Requirement 3

**User Story:** As a developer, I want standalone result statements to be colorized correctly, so that result parsing works as expected.

#### Acceptance Criteria

1. WHEN parsing standalone `Result: value` THEN the system SHALL use `resultKey` and `resultVal` tags
2. WHEN parsing result parameters in functions THEN the system SHALL use `resultKey` and `resultVal` tags
3. WHEN parsing result arrays THEN the system SHALL colorize array contents with `resultVal` tags

### Requirement 4

**User Story:** As a developer, I want arrow operations to be handled correctly, so that log entries with arrows are properly formatted.

#### Acceptance Criteria

1. WHEN parsing arrow operations THEN the system SHALL use `symbol` tag for the arrow
2. WHEN normalizing arrow spacing THEN the system SHALL ensure consistent spacing around arrows
3. WHEN parsing complex arrow expressions THEN the system SHALL maintain proper nesting levels