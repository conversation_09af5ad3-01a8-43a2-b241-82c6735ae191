import 'package:meta/meta.dart';
import 'package:mobx/mobx.dart';

import '../domain/side.dart';
import './pane_store.dart';

part '.gen/all_panes_store.g.dart';

class AllPanesStore = _AllPanesStore with _$AllPanesStore;

abstract class _AllPanesStore with Store {
  final PaneStore left;
  final PaneStore right;

  @observable
  Side sourcePaneSide = Side.left;

  _AllPanesStore({required this.left, required this.right});

  Side get targetPaneSide => sourcePaneSide == Side.left ? Side.right : Side.left;

  PaneStore get sourcePane => sourcePaneSide == Side.left ? left : right;

  PaneStore get targetPane => sourcePaneSide == Side.left ? right : left;

  @action
  void setSourcePane(Side side) {
    if (sourcePaneSide != side) {
      sourcePaneSide = side;
    }
  }

  @action
  void switchSourcePane() {
    setSourcePane(targetPaneSide);
  }

  @visibleForTesting
  void dispose() {
    left.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    right.dispose(); // ignore: invalid_use_of_visible_for_testing_member
  }
}
